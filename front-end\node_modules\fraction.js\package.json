{"name": "fraction.js", "title": "fraction.js", "version": "4.3.7", "homepage": "https://www.xarg.org/2014/03/rational-numbers-in-javascript/", "bugs": "https://github.com/rawify/Fraction.js/issues", "description": "A rational number library", "keywords": ["math", "fraction", "rational", "rationals", "number", "parser", "rational numbers"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://raw.org/"}, "type": "module", "main": "fraction.cjs", "exports": {".": {"import": "./fraction.js", "require": "./fraction.cjs", "types": "./fraction.d.ts"}}, "types": "./fraction.d.ts", "private": false, "readmeFilename": "README.md", "directories": {"example": "examples"}, "license": "MIT", "repository": {"type": "git", "url": "git://github.com/rawify/Fraction.js.git"}, "funding": {"type": "patreon", "url": "https://github.com/sponsors/rawify"}, "engines": {"node": "*"}, "scripts": {"test": "mocha tests/*.js"}, "devDependencies": {"mocha": "*"}}