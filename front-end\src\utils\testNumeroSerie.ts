// Utilitaire de test pour le système de numérotation
import { generateNumeroSerie, getNextNumeroSerie, parseNumeroSerie, validateNumeroSerie } from '../services/numeroSerieService';

/**
 * Fonction de test pour démontrer le fonctionnement du système de numérotation
 */
export const testNumeroSerieSystem = () => {
  console.log('🧪 Test du système de numérotation');
  console.log('=====================================');

  // Test des annonces légales (système annuel)
  console.log('\n📋 Test Annonces Légales (système annuel):');
  const al1 = generateNumeroSerie('annonce-legale');
  const al2 = generateNumeroSerie('annonce-legale');
  const al3 = generateNumeroSerie('annonce-legale');
  console.log(`1ère annonce: ${al1}`);
  console.log(`2ème annonce: ${al2}`);
  console.log(`3ème annonce: ${al3}`);

  // Test du journal (système quotidien)
  console.log('\n📰 Test Journal (système quotidien):');
  const j1 = generateNumeroSerie('journal');
  const j2 = generateNumeroSerie('journal');
  const j3 = generateNumeroSerie('journal');
  console.log(`1ère publication: ${j1}`);
  console.log(`2ème publication: ${j2}`);
  console.log(`3ème publication: ${j3}`);

  // Test de prévisualisation
  console.log('\n👁️ Test Prévisualisation:');
  const nextAL = getNextNumeroSerie('annonce-legale');
  const nextJ = getNextNumeroSerie('journal');
  console.log(`Prochaine annonce légale: ${nextAL}`);
  console.log(`Prochaine publication journal: ${nextJ}`);

  // Test de validation
  console.log('\n✅ Test Validation:');
  const testCases = [
    'AL-2024-0001',           // Format standard valide
    'JRN-2024-01-15-0001',    // Format journal valide
    'DOM-2024-0123',          // Format standard valide
    'JRN-2024-0001',          // Format journal invalide
    'INVALID-FORMAT',         // Format complètement invalide
  ];

  testCases.forEach(test => {
    const isValid = validateNumeroSerie(test);
    console.log(`${test}: ${isValid ? '✅ Valide' : '❌ Invalide'}`);
  });

  // Test de parsing
  console.log('\n🔍 Test Parsing:');
  try {
    const parsedAL = parseNumeroSerie(al1);
    const parsedJ = parseNumeroSerie(j1);
    
    console.log('Annonce légale parsée:', parsedAL);
    console.log('Journal parsé:', parsedJ);
  } catch (error) {
    console.error('Erreur de parsing:', error);
  }

  console.log('\n🎉 Tests terminés!');
};

/**
 * Simule le passage à un nouveau jour pour tester le reset du journal
 */
export const simulateNewDay = () => {
  console.log('\n🌅 Simulation nouveau jour pour le journal');
  console.log('==========================================');

  // Sauvegarder l'état actuel
  const currentJournal = localStorage.getItem('charikti_journal_counter');
  console.log('État actuel:', currentJournal);

  // Simuler un nouveau jour en modifiant la date stockée
  if (currentJournal) {
    const data = JSON.parse(currentJournal);
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    data.date = yesterday.toISOString().split('T')[0];
    localStorage.setItem('charikti_journal_counter', JSON.stringify(data));
    console.log('Date modifiée pour simuler hier:', data.date);
  }

  // Générer un nouveau numéro (devrait être reset à 0001)
  const newJournal = generateNumeroSerie('journal');
  console.log('Nouveau numéro après "changement de jour":', newJournal);

  // Vérifier que c'est bien 0001
  const parsed = parseNumeroSerie(newJournal);
  if (parsed.isJournal && parsed.number === 1) {
    console.log('✅ Reset quotidien fonctionne correctement!');
  } else {
    console.log('❌ Problème avec le reset quotidien');
  }
};

/**
 * Affiche les statistiques actuelles
 */
export const showStatistics = () => {
  console.log('\n📊 Statistiques actuelles');
  console.log('=========================');

  const keys = [
    'charikti_annonce_legale_counter',
    'charikti_domiciliation_counter', 
    'charikti_juridique_counter',
    'charikti_journal_counter'
  ];

  keys.forEach(key => {
    const data = localStorage.getItem(key);
    if (data) {
      const parsed = JSON.parse(data);
      console.log(`${key}:`, parsed);
    } else {
      console.log(`${key}: Aucune donnée`);
    }
  });
};

/**
 * Nettoie toutes les données de test
 */
export const cleanTestData = () => {
  console.log('\n🧹 Nettoyage des données de test');
  console.log('=================================');

  const keys = [
    'charikti_annonce_legale_counter',
    'charikti_domiciliation_counter',
    'charikti_juridique_counter', 
    'charikti_journal_counter'
  ];

  keys.forEach(key => {
    localStorage.removeItem(key);
    console.log(`✅ ${key} supprimé`);
  });

  console.log('🎉 Nettoyage terminé!');
};

// Export pour utilisation dans la console du navigateur
if (typeof window !== 'undefined') {
  (window as any).testNumeroSerie = {
    test: testNumeroSerieSystem,
    simulateNewDay,
    showStats: showStatistics,
    clean: cleanTestData
  };
  
  console.log('🔧 Utilitaires de test disponibles dans window.testNumeroSerie');
  console.log('   - test(): Lance tous les tests');
  console.log('   - simulateNewDay(): Simule un changement de jour');
  console.log('   - showStats(): Affiche les statistiques');
  console.log('   - clean(): Nettoie les données de test');
}
