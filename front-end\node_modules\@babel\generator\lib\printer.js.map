{"version": 3, "names": ["_buffer", "require", "n", "_t", "generatorFunctions", "isFunction", "isStatement", "isClassBody", "isTSInterfaceBody", "isTSEnumDeclaration", "SCIENTIFIC_NOTATION", "ZERO_DECIMAL_INTEGER", "HAS_NEWLINE", "HAS_NEWLINE_OR_BlOCK_COMMENT_END", "needsParens", "Printer", "constructor", "format", "map", "inForStatementInit", "tokenContext", "_currentNode", "_indent", "_indentRepeat", "_insideAux", "_parenPushNewlineState", "_noLineTerminator", "_printAuxAfterOnNextUserNode", "_printedComments", "Set", "_endsWithInteger", "_endsWithWord", "_endsWithDiv", "_lastCommentLine", "_endsWithInnerRaw", "_indentInnerComments", "indent", "style", "length", "_inputMap", "_buf", "<PERSON><PERSON><PERSON>", "enterForStatementInit", "val", "old", "generate", "ast", "print", "_maybe<PERSON>dd<PERSON>uxComment", "get", "compact", "concise", "dedent", "semicolon", "force", "_appendChar", "_queue", "rightBrace", "node", "minified", "removeLastSemicolon", "sourceWithOffset", "loc", "token", "rightParens", "space", "_space", "<PERSON><PERSON><PERSON><PERSON>", "lastCp", "getLastChar", "word", "str", "noLineTerminatorAfter", "_maybePrintInnerComments", "charCodeAt", "_append", "number", "isNonDecimalLiteral", "secondChar", "Number", "isInteger", "test", "maybeNewline", "lastChar", "str<PERSON><PERSON><PERSON>", "tokenChar", "char", "newline", "i", "retainLines", "getNewlineCount", "j", "_newline", "endsWith", "endsWithCharAndNewline", "removeTrailingNewline", "exactSource", "cb", "_catchUp", "source", "prop", "columnOffset", "sourceIdentifierName", "identifierName", "pos", "_canMarkIdName", "sourcePosition", "_sourcePosition", "identifierNamePos", "_<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_maybeIndent", "append", "_maybeAddParenChar", "appendChar", "queue", "firstChar", "queueIndentation", "_getIndent", "_shouldIndent", "parenPushNewlineState", "printed", "len", "cha", "chaPost", "catchUp", "line", "count", "getCurrentLine", "_loc$prop", "printTerminatorless", "isLabel", "terminatorState", "trailingCommentsLineOffset", "forceParens", "_node$extra", "_node$leadingComments", "nodeType", "type", "oldConcise", "_compact", "printMethod", "undefined", "ReferenceError", "JSON", "stringify", "name", "parent", "oldInAux", "parenthesized", "extra", "shouldPrintParens", "retainFunctionParens", "leadingComments", "parentType", "callee", "exitInForStatementInit", "_printLeadingComments", "bind", "_printTrailingComments", "enteredPositionlessNode", "_printAuxBeforeComment", "_printAuxAfterComment", "comment", "auxiliaryCommentBefore", "_printComment", "value", "auxiliaryCommentAfter", "getPossibleRaw", "raw", "rawValue", "printJoin", "nodes", "opts", "_nodes$0$loc", "startLine", "start", "newlineOpts", "addNewlines", "nextNodeStartLine", "separator", "statement", "_printNewline", "iterator", "_node$trailingComment", "trailingComments", "_nextNode$loc", "nextNode", "printAndIndentOnComments", "printBlock", "body", "lineOffset", "innerComments", "_printComments", "comments", "printInnerComments", "hasSpace", "printedCommentsCount", "size", "noIndentInnerCommentsHere", "printSequence", "_opts$indent", "printList", "items", "commaSeparator", "newLine", "lastCommentLine", "offset", "_should<PERSON>rintComment", "ignore", "has", "add", "shouldPrintComment", "skipNewLines", "noLineTerminator", "isBlockComment", "printNewLines", "lastCharCode", "adjustMultilineComment", "_comment$loc", "column", "newlineRegex", "RegExp", "replace", "indentSize", "getCurrentColumn", "repeat", "nodeLoc", "hasLoc", "nodeStartLine", "nodeEndLine", "end", "lastLine", "leadingCommentNewline", "should<PERSON><PERSON>t", "commentStartLine", "commentEndLine", "Math", "max", "min", "singleLine", "shouldSkipNewline", "properties", "Object", "assign", "prototype", "Noop", "_default", "exports", "default"], "sources": ["../src/printer.ts"], "sourcesContent": ["import Buffer, { type Po<PERSON> } from \"./buffer.ts\";\nimport type { Loc } from \"./buffer.ts\";\nimport * as n from \"./node/index.ts\";\nimport type * as t from \"@babel/types\";\nimport {\n  isFunction,\n  isStatement,\n  isClassBody,\n  isTSInterfaceBody,\n  isTSEnumDeclaration,\n} from \"@babel/types\";\nimport type { Opts as jsescOptions } from \"jsesc\";\n\nimport type { GeneratorOptions } from \"./index.ts\";\nimport * as generatorFunctions from \"./generators/index.ts\";\nimport type SourceMap from \"./source-map.ts\";\nimport type { TraceMap } from \"@jridgewell/trace-mapping\";\n\n// We inline this package\n// eslint-disable-next-line import/no-extraneous-dependencies\nimport * as charCodes from \"charcodes\";\n\nconst SCIENTIFIC_NOTATION = /e/i;\nconst ZERO_DECIMAL_INTEGER = /\\.0+$/;\nconst HAS_NEWLINE = /[\\n\\r\\u2028\\u2029]/;\nconst HAS_NEWLINE_OR_BlOCK_COMMENT_END = /[\\n\\r\\u2028\\u2029]|\\*\\//;\n\nconst { needsParens } = n;\n\nconst enum COMMENT_TYPE {\n  LEADING,\n  INNER,\n  TRAILING,\n}\n\nconst enum COMMENT_SKIP_NEWLINE {\n  DEFAULT,\n  ALL,\n  LEADING,\n  TRAILING,\n}\n\nconst enum PRINT_COMMENT_HINT {\n  SKIP,\n  ALLOW,\n  DEFER,\n}\n\nexport type Format = {\n  shouldPrintComment: (comment: string) => boolean;\n  retainLines: boolean;\n  retainFunctionParens: boolean;\n  comments: boolean;\n  auxiliaryCommentBefore: string;\n  auxiliaryCommentAfter: string;\n  compact: boolean | \"auto\";\n  minified: boolean;\n  concise: boolean;\n  indent: {\n    adjustMultilineComment: boolean;\n    style: string;\n  };\n  /**\n   * @deprecated Removed in Babel 8, syntax type is always 'hash'\n   */\n  recordAndTupleSyntaxType?: GeneratorOptions[\"recordAndTupleSyntaxType\"];\n  jsescOption: jsescOptions;\n  /**\n   * @deprecated Removed in Babel 8, use `jsescOption` instead\n   */\n  jsonCompatibleStrings?: boolean;\n  /**\n   * For use with the Hack-style pipe operator.\n   * Changes what token is used for pipe bodies’ topic references.\n   */\n  topicToken?: GeneratorOptions[\"topicToken\"];\n  /**\n   * @deprecated Removed in Babel 8\n   */\n  decoratorsBeforeExport?: boolean;\n  /**\n   * The import attributes syntax style:\n   * - \"with\"        : `import { a } from \"b\" with { type: \"json\" };`\n   * - \"assert\"      : `import { a } from \"b\" assert { type: \"json\" };`\n   * - \"with-legacy\" : `import { a } from \"b\" with type: \"json\";`\n   */\n  importAttributesKeyword?: \"with\" | \"assert\" | \"with-legacy\";\n};\n\ninterface AddNewlinesOptions {\n  addNewlines(leading: boolean, node: t.Node): number;\n  nextNodeStartLine: number;\n}\n\ninterface PrintSequenceOptions extends Partial<AddNewlinesOptions> {\n  statement?: boolean;\n  indent?: boolean;\n  trailingCommentsLineOffset?: number;\n}\n\ninterface PrintListOptions {\n  separator?: (this: Printer) => void;\n  iterator?: (node: t.Node, index: number) => void;\n  statement?: boolean;\n  indent?: boolean;\n}\n\nexport type PrintJoinOptions = PrintListOptions & PrintSequenceOptions;\nclass Printer {\n  constructor(format: Format, map: SourceMap) {\n    this.format = format;\n\n    this._indentRepeat = format.indent.style.length;\n\n    this._inputMap = map?._inputMap;\n\n    this._buf = new Buffer(map, format.indent.style[0]);\n  }\n  declare _inputMap: TraceMap;\n\n  declare format: Format;\n\n  inForStatementInit: boolean = false;\n  enterForStatementInit(val: boolean) {\n    const old = this.inForStatementInit;\n    if (old === val) return () => {};\n    this.inForStatementInit = val;\n    return () => {\n      this.inForStatementInit = old;\n    };\n  }\n\n  tokenContext: number = 0;\n\n  declare _buf: Buffer;\n  _currentNode: t.Node = null;\n  _indent: number = 0;\n  _indentRepeat: number = 0;\n  _insideAux: boolean = false;\n  _parenPushNewlineState: { printed: boolean } | null = null;\n  _noLineTerminator: boolean = false;\n  _printAuxAfterOnNextUserNode: boolean = false;\n  _printedComments = new Set<t.Comment>();\n  _endsWithInteger = false;\n  _endsWithWord = false;\n  _endsWithDiv = false;\n  _lastCommentLine = 0;\n  _endsWithInnerRaw: boolean = false;\n  _indentInnerComments: boolean = true;\n\n  generate(ast: t.Node) {\n    this.print(ast);\n    this._maybeAddAuxComment();\n\n    return this._buf.get();\n  }\n\n  /**\n   * Increment indent size.\n   */\n\n  indent(): void {\n    if (this.format.compact || this.format.concise) return;\n\n    this._indent++;\n  }\n\n  /**\n   * Decrement indent size.\n   */\n\n  dedent(): void {\n    if (this.format.compact || this.format.concise) return;\n\n    this._indent--;\n  }\n\n  /**\n   * Add a semicolon to the buffer.\n   */\n\n  semicolon(force: boolean = false): void {\n    this._maybeAddAuxComment();\n    if (force) {\n      this._appendChar(charCodes.semicolon);\n    } else {\n      this._queue(charCodes.semicolon);\n    }\n    this._noLineTerminator = false;\n  }\n\n  /**\n   * Add a right brace to the buffer.\n   */\n\n  rightBrace(node: t.Node): void {\n    if (this.format.minified) {\n      this._buf.removeLastSemicolon();\n    }\n    this.sourceWithOffset(\"end\", node.loc, -1);\n    this.token(\"}\");\n  }\n\n  rightParens(node: t.Node): void {\n    this.sourceWithOffset(\"end\", node.loc, -1);\n    this.token(\")\");\n  }\n\n  /**\n   * Add a space to the buffer unless it is compact.\n   */\n\n  space(force: boolean = false): void {\n    if (this.format.compact) return;\n\n    if (force) {\n      this._space();\n    } else if (this._buf.hasContent()) {\n      const lastCp = this.getLastChar();\n      if (lastCp !== charCodes.space && lastCp !== charCodes.lineFeed) {\n        this._space();\n      }\n    }\n  }\n\n  /**\n   * Writes a token that can't be safely parsed without taking whitespace into account.\n   */\n\n  word(str: string, noLineTerminatorAfter: boolean = false): void {\n    this.tokenContext = 0;\n\n    this._maybePrintInnerComments();\n\n    // prevent concatenating words and creating // comment out of division and regex\n    if (\n      this._endsWithWord ||\n      (this._endsWithDiv && str.charCodeAt(0) === charCodes.slash)\n    ) {\n      this._space();\n    }\n\n    this._maybeAddAuxComment();\n    this._append(str, false);\n\n    this._endsWithWord = true;\n    this._noLineTerminator = noLineTerminatorAfter;\n  }\n\n  /**\n   * Writes a number token so that we can validate if it is an integer.\n   */\n\n  number(str: string, number?: number): void {\n    // const NON_DECIMAL_LITERAL = /^0[box]/;\n    function isNonDecimalLiteral(str: string) {\n      if (str.length > 2 && str.charCodeAt(0) === charCodes.digit0) {\n        const secondChar = str.charCodeAt(1);\n        return (\n          secondChar === charCodes.lowercaseB ||\n          secondChar === charCodes.lowercaseO ||\n          secondChar === charCodes.lowercaseX\n        );\n      }\n      return false;\n    }\n    this.word(str);\n\n    // Integer tokens need special handling because they cannot have '.'s inserted\n    // immediately after them.\n    this._endsWithInteger =\n      Number.isInteger(number) &&\n      !isNonDecimalLiteral(str) &&\n      !SCIENTIFIC_NOTATION.test(str) &&\n      !ZERO_DECIMAL_INTEGER.test(str) &&\n      str.charCodeAt(str.length - 1) !== charCodes.dot;\n  }\n\n  /**\n   * Writes a simple token.\n   */\n  token(str: string, maybeNewline = false): void {\n    this.tokenContext = 0;\n\n    this._maybePrintInnerComments();\n\n    const lastChar = this.getLastChar();\n    const strFirst = str.charCodeAt(0);\n    if (\n      (lastChar === charCodes.exclamationMark &&\n        // space is mandatory to avoid outputting <!--\n        // http://javascript.spec.whatwg.org/#comment-syntax\n        (str === \"--\" ||\n          // Needs spaces to avoid changing a! == 0 to a!== 0\n          strFirst === charCodes.equalsTo)) ||\n      // Need spaces for operators of the same kind to avoid: `a+++b`\n      (strFirst === charCodes.plusSign && lastChar === charCodes.plusSign) ||\n      (strFirst === charCodes.dash && lastChar === charCodes.dash) ||\n      // Needs spaces to avoid changing '34' to '34.', which would still be a valid number.\n      (strFirst === charCodes.dot && this._endsWithInteger)\n    ) {\n      this._space();\n    }\n\n    this._maybeAddAuxComment();\n    this._append(str, maybeNewline);\n    this._noLineTerminator = false;\n  }\n\n  tokenChar(char: number): void {\n    this.tokenContext = 0;\n\n    this._maybePrintInnerComments();\n\n    const lastChar = this.getLastChar();\n    if (\n      // Need spaces for operators of the same kind to avoid: `a+++b`\n      (char === charCodes.plusSign && lastChar === charCodes.plusSign) ||\n      (char === charCodes.dash && lastChar === charCodes.dash) ||\n      // Needs spaces to avoid changing '34' to '34.', which would still be a valid number.\n      (char === charCodes.dot && this._endsWithInteger)\n    ) {\n      this._space();\n    }\n\n    this._maybeAddAuxComment();\n    this._appendChar(char);\n    this._noLineTerminator = false;\n  }\n\n  /**\n   * Add a newline (or many newlines), maintaining formatting.\n   * This function checks the number of newlines in the queue and subtracts them.\n   * It currently has some limitations.\n   * @see {Buffer#getNewlineCount}\n   */\n  newline(i: number = 1, force?: boolean): void {\n    if (i <= 0) return;\n\n    if (!force) {\n      if (this.format.retainLines || this.format.compact) return;\n\n      if (this.format.concise) {\n        this.space();\n        return;\n      }\n    }\n\n    if (i > 2) i = 2; // Max two lines\n\n    i -= this._buf.getNewlineCount();\n\n    for (let j = 0; j < i; j++) {\n      this._newline();\n    }\n\n    return;\n  }\n\n  endsWith(char: number): boolean {\n    return this.getLastChar() === char;\n  }\n\n  getLastChar(): number {\n    return this._buf.getLastChar();\n  }\n\n  endsWithCharAndNewline(): number {\n    return this._buf.endsWithCharAndNewline();\n  }\n\n  removeTrailingNewline(): void {\n    this._buf.removeTrailingNewline();\n  }\n\n  exactSource(loc: Loc | undefined, cb: () => void) {\n    if (!loc) {\n      cb();\n      return;\n    }\n\n    this._catchUp(\"start\", loc);\n\n    this._buf.exactSource(loc, cb);\n  }\n\n  source(prop: \"start\" | \"end\", loc: Loc | undefined): void {\n    if (!loc) return;\n\n    this._catchUp(prop, loc);\n\n    this._buf.source(prop, loc);\n  }\n\n  sourceWithOffset(\n    prop: \"start\" | \"end\",\n    loc: Loc | undefined,\n    columnOffset: number,\n  ): void {\n    if (!loc) return;\n\n    this._catchUp(prop, loc);\n\n    this._buf.sourceWithOffset(prop, loc, columnOffset);\n  }\n\n  sourceIdentifierName(identifierName: string, pos?: Pos): void {\n    if (!this._buf._canMarkIdName) return;\n\n    const sourcePosition = this._buf._sourcePosition;\n    sourcePosition.identifierNamePos = pos;\n    sourcePosition.identifierName = identifierName;\n  }\n\n  _space(): void {\n    this._queue(charCodes.space);\n  }\n\n  _newline(): void {\n    this._queue(charCodes.lineFeed);\n  }\n\n  _append(str: string, maybeNewline: boolean): void {\n    this._maybeAddParen(str);\n    this._maybeIndent(str.charCodeAt(0));\n\n    this._buf.append(str, maybeNewline);\n\n    // callers are expected to then set these to `true` when needed\n    this._endsWithWord = false;\n    this._endsWithInteger = false;\n    this._endsWithDiv = false;\n  }\n\n  _appendChar(char: number): void {\n    this._maybeAddParenChar(char);\n    this._maybeIndent(char);\n\n    this._buf.appendChar(char);\n\n    // callers are expected to then set these to `true` when needed\n    this._endsWithWord = false;\n    this._endsWithInteger = false;\n    this._endsWithDiv = false;\n  }\n\n  _queue(char: number) {\n    this._maybeAddParenChar(char);\n    this._maybeIndent(char);\n\n    this._buf.queue(char);\n\n    this._endsWithWord = false;\n    this._endsWithInteger = false;\n  }\n\n  _maybeIndent(firstChar: number): void {\n    // we've got a newline before us so prepend on the indentation\n    if (\n      this._indent &&\n      firstChar !== charCodes.lineFeed &&\n      this.endsWith(charCodes.lineFeed)\n    ) {\n      this._buf.queueIndentation(this._getIndent());\n    }\n  }\n\n  _shouldIndent(firstChar: number) {\n    // we've got a newline before us so prepend on the indentation\n    if (\n      this._indent &&\n      firstChar !== charCodes.lineFeed &&\n      this.endsWith(charCodes.lineFeed)\n    ) {\n      return true;\n    }\n  }\n\n  _maybeAddParenChar(char: number): void {\n    // see startTerminatorless() instance method\n    const parenPushNewlineState = this._parenPushNewlineState;\n    if (!parenPushNewlineState) return;\n\n    // This function does two things:\n    // - If needed, prints a parenthesis\n    // - If the currently printed string removes the need for the paren,\n    //   it resets the _parenPushNewlineState field.\n    //   Almost everything removes the need for a paren, except for\n    //   comments and whitespaces.\n\n    if (char === charCodes.space) {\n      // Whitespaces only, the parentheses might still be needed.\n      return;\n    }\n\n    // Check for newline or comment.\n    if (char !== charCodes.lineFeed) {\n      this._parenPushNewlineState = null;\n      return;\n    }\n\n    this.token(\"(\");\n    this.indent();\n    parenPushNewlineState.printed = true;\n  }\n\n  _maybeAddParen(str: string): void {\n    // see startTerminatorless() instance method\n    const parenPushNewlineState = this._parenPushNewlineState;\n    if (!parenPushNewlineState) return;\n\n    // This function does two things:\n    // - If needed, prints a parenthesis\n    // - If the currently printed string removes the need for the paren,\n    //   it resets the _parenPushNewlineState field.\n    //   Almost everything removes the need for a paren, except for\n    //   comments and whitespaces.\n\n    const len = str.length;\n\n    let i;\n    for (i = 0; i < len && str.charCodeAt(i) === charCodes.space; i++) continue;\n    if (i === len) {\n      // Whitespaces only, the parentheses might still be needed.\n      return;\n    }\n\n    // Check for newline or comment.\n    const cha = str.charCodeAt(i);\n    if (cha !== charCodes.lineFeed) {\n      if (\n        // This is not a comment (it doesn't start with /)\n        cha !== charCodes.slash ||\n        // This is not a comment (it's a / operator)\n        i + 1 === len\n      ) {\n        // After a normal token, the parentheses aren't needed anymore\n        this._parenPushNewlineState = null;\n        return;\n      }\n\n      const chaPost = str.charCodeAt(i + 1);\n\n      if (chaPost === charCodes.asterisk) {\n        // This is a block comment\n        return;\n      } else if (chaPost !== charCodes.slash) {\n        // This is neither a block comment, nor a line comment.\n        // After a normal token, the parentheses aren't needed anymore\n        this._parenPushNewlineState = null;\n        return;\n      }\n    }\n\n    this.token(\"(\");\n    this.indent();\n    parenPushNewlineState.printed = true;\n  }\n\n  catchUp(line: number) {\n    if (!this.format.retainLines) return;\n\n    // catch up to this nodes newline if we're behind\n    const count = line - this._buf.getCurrentLine();\n\n    for (let i = 0; i < count; i++) {\n      this._newline();\n    }\n  }\n\n  _catchUp(prop: \"start\" | \"end\", loc?: Loc) {\n    if (!this.format.retainLines) return;\n\n    // catch up to this nodes newline if we're behind\n    const line = loc?.[prop]?.line;\n    if (line != null) {\n      const count = line - this._buf.getCurrentLine();\n\n      for (let i = 0; i < count; i++) {\n        this._newline();\n      }\n    }\n  }\n\n  /**\n   * Get the current indent.\n   */\n\n  _getIndent(): number {\n    return this._indentRepeat * this._indent;\n  }\n\n  printTerminatorless(node: t.Node, isLabel: boolean) {\n    /**\n     * Set some state that will be modified if a newline has been inserted before any\n     * non-space characters.\n     *\n     * This is to prevent breaking semantics for terminatorless separator nodes. eg:\n     *\n     *   return foo;\n     *\n     * returns `foo`. But if we do:\n     *\n     *   return\n     *   foo;\n     *\n     *  `undefined` will be returned and not `foo` due to the terminator.\n     */\n    if (isLabel) {\n      this._noLineTerminator = true;\n      this.print(node);\n    } else {\n      const terminatorState = {\n        printed: false,\n      };\n      this._parenPushNewlineState = terminatorState;\n      this.print(node);\n      /**\n       * Print an ending parentheses if a starting one has been printed.\n       */\n      if (terminatorState.printed) {\n        this.dedent();\n        this.newline();\n        this.token(\")\");\n      }\n    }\n  }\n\n  print(\n    node: t.Node | null,\n    noLineTerminatorAfter?: boolean,\n    // trailingCommentsLineOffset also used to check if called from printJoin\n    // it will be ignored if `noLineTerminatorAfter||this._noLineTerminator`\n    trailingCommentsLineOffset?: number,\n    forceParens?: boolean,\n  ) {\n    if (!node) return;\n\n    this._endsWithInnerRaw = false;\n\n    const nodeType = node.type;\n    const format = this.format;\n\n    const oldConcise = format.concise;\n    if (\n      // @ts-expect-error document _compact AST properties\n      node._compact\n    ) {\n      format.concise = true;\n    }\n\n    const printMethod =\n      this[\n        nodeType as Exclude<\n          t.Node[\"type\"],\n          // removed\n          | \"Noop\"\n          // renamed\n          | t.DeprecatedAliases[\"type\"]\n        >\n      ];\n    if (printMethod === undefined) {\n      throw new ReferenceError(\n        `unknown node of type ${JSON.stringify(\n          nodeType,\n        )} with constructor ${JSON.stringify(node.constructor.name)}`,\n      );\n    }\n\n    const parent = this._currentNode;\n    this._currentNode = node;\n\n    const oldInAux = this._insideAux;\n    this._insideAux = node.loc == null;\n    this._maybeAddAuxComment(this._insideAux && !oldInAux);\n\n    const parenthesized = node.extra?.parenthesized as boolean | undefined;\n    let shouldPrintParens =\n      forceParens ||\n      (parenthesized &&\n        format.retainFunctionParens &&\n        nodeType === \"FunctionExpression\") ||\n      needsParens(node, parent, this.tokenContext, this.inForStatementInit);\n\n    if (\n      !shouldPrintParens &&\n      parenthesized &&\n      node.leadingComments?.length &&\n      node.leadingComments[0].type === \"CommentBlock\"\n    ) {\n      const parentType = parent?.type;\n      switch (parentType) {\n        case \"ExpressionStatement\":\n        case \"VariableDeclarator\":\n        case \"AssignmentExpression\":\n        case \"ReturnStatement\":\n          break;\n        case \"CallExpression\":\n        case \"OptionalCallExpression\":\n        case \"NewExpression\":\n          if (parent.callee !== node) break;\n        // falls through\n        default:\n          shouldPrintParens = true;\n      }\n    }\n\n    let exitInForStatementInit;\n    if (shouldPrintParens) {\n      this.token(\"(\");\n      this._endsWithInnerRaw = false;\n      exitInForStatementInit = this.enterForStatementInit(false);\n    }\n\n    this._lastCommentLine = 0;\n\n    this._printLeadingComments(node, parent);\n\n    const loc = nodeType === \"Program\" || nodeType === \"File\" ? null : node.loc;\n\n    this.exactSource(\n      loc,\n      // We must use @ts-ignore because this error appears in VSCode but not\n      // when doing a full build?\n      // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n      // @ts-ignore https://github.com/microsoft/TypeScript/issues/58468\n      printMethod.bind(this, node, parent),\n    );\n\n    if (shouldPrintParens) {\n      this._printTrailingComments(node, parent);\n      this.token(\")\");\n      this._noLineTerminator = noLineTerminatorAfter;\n      exitInForStatementInit();\n    } else if (noLineTerminatorAfter && !this._noLineTerminator) {\n      this._noLineTerminator = true;\n      this._printTrailingComments(node, parent);\n    } else {\n      this._printTrailingComments(node, parent, trailingCommentsLineOffset);\n    }\n\n    // end\n    this._currentNode = parent;\n    format.concise = oldConcise;\n    this._insideAux = oldInAux;\n\n    this._endsWithInnerRaw = false;\n  }\n\n  _maybeAddAuxComment(enteredPositionlessNode?: boolean) {\n    if (enteredPositionlessNode) this._printAuxBeforeComment();\n    if (!this._insideAux) this._printAuxAfterComment();\n  }\n\n  _printAuxBeforeComment() {\n    if (this._printAuxAfterOnNextUserNode) return;\n    this._printAuxAfterOnNextUserNode = true;\n\n    const comment = this.format.auxiliaryCommentBefore;\n    if (comment) {\n      this._printComment(\n        {\n          type: \"CommentBlock\",\n          value: comment,\n        },\n        COMMENT_SKIP_NEWLINE.DEFAULT,\n      );\n    }\n  }\n\n  _printAuxAfterComment() {\n    if (!this._printAuxAfterOnNextUserNode) return;\n    this._printAuxAfterOnNextUserNode = false;\n\n    const comment = this.format.auxiliaryCommentAfter;\n    if (comment) {\n      this._printComment(\n        {\n          type: \"CommentBlock\",\n          value: comment,\n        },\n        COMMENT_SKIP_NEWLINE.DEFAULT,\n      );\n    }\n  }\n\n  getPossibleRaw(\n    node:\n      | t.StringLiteral\n      | t.NumericLiteral\n      | t.BigIntLiteral\n      | t.DecimalLiteral\n      | t.DirectiveLiteral\n      | t.JSXText,\n  ): string | undefined {\n    const extra = node.extra;\n    if (\n      extra?.raw != null &&\n      extra.rawValue != null &&\n      node.value === extra.rawValue\n    ) {\n      // @ts-expect-error: The extra.raw of these AST node types must be a string\n      return extra.raw;\n    }\n  }\n\n  printJoin(\n    nodes: Array<t.Node> | undefined | null,\n    opts: PrintJoinOptions = {},\n  ) {\n    if (!nodes?.length) return;\n\n    let { indent } = opts;\n\n    if (indent == null && this.format.retainLines) {\n      const startLine = nodes[0].loc?.start.line;\n      if (startLine != null && startLine !== this._buf.getCurrentLine()) {\n        indent = true;\n      }\n    }\n\n    if (indent) this.indent();\n\n    const newlineOpts: AddNewlinesOptions = {\n      addNewlines: opts.addNewlines,\n      nextNodeStartLine: 0,\n    };\n\n    const separator = opts.separator ? opts.separator.bind(this) : null;\n\n    const len = nodes.length;\n    for (let i = 0; i < len; i++) {\n      const node = nodes[i];\n      if (!node) continue;\n\n      if (opts.statement) this._printNewline(i === 0, newlineOpts);\n\n      this.print(node, undefined, opts.trailingCommentsLineOffset || 0);\n\n      opts.iterator?.(node, i);\n\n      if (i < len - 1) separator?.();\n\n      if (opts.statement) {\n        if (!node.trailingComments?.length) {\n          this._lastCommentLine = 0;\n        }\n\n        if (i + 1 === len) {\n          this.newline(1);\n        } else {\n          const nextNode = nodes[i + 1];\n          newlineOpts.nextNodeStartLine = nextNode.loc?.start.line || 0;\n\n          this._printNewline(true, newlineOpts);\n        }\n      }\n    }\n\n    if (indent) this.dedent();\n  }\n\n  printAndIndentOnComments(node: t.Node) {\n    const indent = node.leadingComments && node.leadingComments.length > 0;\n    if (indent) this.indent();\n    this.print(node);\n    if (indent) this.dedent();\n  }\n\n  printBlock(parent: Extract<t.Node, { body: t.Statement }>) {\n    const node = parent.body;\n\n    if (node.type !== \"EmptyStatement\") {\n      this.space();\n    }\n\n    this.print(node);\n  }\n\n  _printTrailingComments(node: t.Node, parent?: t.Node, lineOffset?: number) {\n    const { innerComments, trailingComments } = node;\n    // We print inner comments here, so that if for some reason they couldn't\n    // be printed in earlier locations they are still printed *somewhere*,\n    // even if at the end of the node.\n    if (innerComments?.length) {\n      this._printComments(\n        COMMENT_TYPE.TRAILING,\n        innerComments,\n        node,\n        parent,\n        lineOffset,\n      );\n    }\n    if (trailingComments?.length) {\n      this._printComments(\n        COMMENT_TYPE.TRAILING,\n        trailingComments,\n        node,\n        parent,\n        lineOffset,\n      );\n    }\n  }\n\n  _printLeadingComments(node: t.Node, parent: t.Node) {\n    const comments = node.leadingComments;\n    if (!comments?.length) return;\n    this._printComments(COMMENT_TYPE.LEADING, comments, node, parent);\n  }\n\n  _maybePrintInnerComments() {\n    if (this._endsWithInnerRaw) this.printInnerComments();\n    this._endsWithInnerRaw = true;\n    this._indentInnerComments = true;\n  }\n\n  printInnerComments() {\n    const node = this._currentNode;\n    const comments = node.innerComments;\n    if (!comments?.length) return;\n\n    const hasSpace = this.endsWith(charCodes.space);\n    const indent = this._indentInnerComments;\n    const printedCommentsCount = this._printedComments.size;\n    if (indent) this.indent();\n    this._printComments(COMMENT_TYPE.INNER, comments, node);\n    if (hasSpace && printedCommentsCount !== this._printedComments.size) {\n      this.space();\n    }\n    if (indent) this.dedent();\n  }\n\n  noIndentInnerCommentsHere() {\n    this._indentInnerComments = false;\n  }\n\n  printSequence(nodes: t.Node[], opts: PrintSequenceOptions = {}) {\n    opts.statement = true;\n    opts.indent ??= false;\n    this.printJoin(nodes, opts);\n  }\n\n  printList(items: t.Node[], opts: PrintListOptions = {}) {\n    if (opts.separator == null) {\n      opts.separator = commaSeparator;\n    }\n\n    this.printJoin(items, opts);\n  }\n\n  _printNewline(newLine: boolean, opts: AddNewlinesOptions) {\n    const format = this.format;\n\n    // Fast path since 'this.newline' does nothing when not tracking lines.\n    if (format.retainLines || format.compact) return;\n\n    // Fast path for concise since 'this.newline' just inserts a space when\n    // concise formatting is in use.\n    if (format.concise) {\n      this.space();\n      return;\n    }\n\n    if (!newLine) {\n      return;\n    }\n\n    const startLine = opts.nextNodeStartLine;\n    const lastCommentLine = this._lastCommentLine;\n    if (startLine > 0 && lastCommentLine > 0) {\n      const offset = startLine - lastCommentLine;\n      if (offset >= 0) {\n        this.newline(offset || 1);\n        return;\n      }\n    }\n\n    // don't add newlines at the beginning of the file\n    if (this._buf.hasContent()) {\n      // Here is the logic of the original line wrapping according to the node layout, we are not using it now.\n      // We currently add at most one newline to each node in the list, ignoring `opts.addNewlines`.\n\n      // let lines = 0;\n      // if (!leading) lines++; // always include at least a single line after\n      // if (opts.addNewlines) lines += opts.addNewlines(leading, node) || 0;\n\n      // const needs = leading ? needsWhitespaceBefore : needsWhitespaceAfter;\n      // if (needs(node, parent)) lines++;\n\n      // this.newline(Math.min(2, lines));\n\n      this.newline(1);\n    }\n  }\n\n  // Returns `PRINT_COMMENT_HINT.DEFER` if the comment cannot be printed in this position due to\n  // line terminators, signaling that the print comments loop can stop and\n  // resume printing comments at the next possible position. This happens when\n  // printing inner comments, since if we have an inner comment with a multiline\n  // there is at least one inner position where line terminators are allowed.\n  _shouldPrintComment(comment: t.Comment): PRINT_COMMENT_HINT {\n    // Some plugins (such as flow-strip-types) use this to mark comments as removed using the AST-root 'comments' property,\n    // where they can't manually mutate the AST node comment lists.\n    if (comment.ignore) return PRINT_COMMENT_HINT.SKIP;\n\n    if (this._printedComments.has(comment)) return PRINT_COMMENT_HINT.SKIP;\n\n    if (\n      this._noLineTerminator &&\n      HAS_NEWLINE_OR_BlOCK_COMMENT_END.test(comment.value)\n    ) {\n      return PRINT_COMMENT_HINT.DEFER;\n    }\n\n    this._printedComments.add(comment);\n\n    if (!this.format.shouldPrintComment(comment.value)) {\n      return PRINT_COMMENT_HINT.SKIP;\n    }\n\n    return PRINT_COMMENT_HINT.ALLOW;\n  }\n\n  _printComment(comment: t.Comment, skipNewLines: COMMENT_SKIP_NEWLINE) {\n    const noLineTerminator = this._noLineTerminator;\n    const isBlockComment = comment.type === \"CommentBlock\";\n\n    // Add a newline before and after a block comment, unless explicitly\n    // disallowed\n    const printNewLines =\n      isBlockComment &&\n      skipNewLines !== COMMENT_SKIP_NEWLINE.ALL &&\n      !this._noLineTerminator;\n\n    if (\n      printNewLines &&\n      this._buf.hasContent() &&\n      skipNewLines !== COMMENT_SKIP_NEWLINE.LEADING\n    ) {\n      this.newline(1);\n    }\n\n    const lastCharCode = this.getLastChar();\n    if (\n      lastCharCode !== charCodes.leftSquareBracket &&\n      lastCharCode !== charCodes.leftCurlyBrace &&\n      lastCharCode !== charCodes.leftParenthesis\n    ) {\n      this.space();\n    }\n\n    let val;\n    if (isBlockComment) {\n      const { _parenPushNewlineState } = this;\n      if (\n        _parenPushNewlineState?.printed === false &&\n        HAS_NEWLINE.test(comment.value)\n      ) {\n        this.token(\"(\");\n        this.indent();\n        _parenPushNewlineState.printed = true;\n      }\n      val = `/*${comment.value}*/`;\n      if (this.format.indent.adjustMultilineComment) {\n        const offset = comment.loc?.start.column;\n        if (offset) {\n          const newlineRegex = new RegExp(\"\\\\n\\\\s{1,\" + offset + \"}\", \"g\");\n          val = val.replace(newlineRegex, \"\\n\");\n        }\n        if (this.format.concise) {\n          val = val.replace(/\\n(?!$)/g, `\\n`);\n        } else {\n          let indentSize = this.format.retainLines\n            ? 0\n            : this._buf.getCurrentColumn();\n\n          if (this._shouldIndent(charCodes.slash) || this.format.retainLines) {\n            indentSize += this._getIndent();\n          }\n\n          val = val.replace(/\\n(?!$)/g, `\\n${\" \".repeat(indentSize)}`);\n        }\n      }\n    } else if (!noLineTerminator) {\n      val = `//${comment.value}`;\n    } else {\n      // It was a single-line comment, so it's guaranteed to not\n      // contain newlines and it can be safely printed as a block\n      // comment.\n      val = `/*${comment.value}*/`;\n    }\n\n    // Avoid converting a / operator into a line comment by appending /* to it\n    if (this._endsWithDiv) this._space();\n\n    this.source(\"start\", comment.loc);\n    this._append(val, isBlockComment);\n\n    if (!isBlockComment && !noLineTerminator) {\n      this.newline(1, true);\n    }\n\n    if (printNewLines && skipNewLines !== COMMENT_SKIP_NEWLINE.TRAILING) {\n      this.newline(1);\n    }\n  }\n\n  _printComments(\n    type: COMMENT_TYPE,\n    comments: readonly t.Comment[],\n    node: t.Node,\n    parent?: t.Node,\n    lineOffset: number = 0,\n  ) {\n    const nodeLoc = node.loc;\n    const len = comments.length;\n    let hasLoc = !!nodeLoc;\n    const nodeStartLine = hasLoc ? nodeLoc.start.line : 0;\n    const nodeEndLine = hasLoc ? nodeLoc.end.line : 0;\n    let lastLine = 0;\n    let leadingCommentNewline = 0;\n\n    const maybeNewline = this._noLineTerminator\n      ? function () {}\n      : this.newline.bind(this);\n\n    for (let i = 0; i < len; i++) {\n      const comment = comments[i];\n\n      const shouldPrint = this._shouldPrintComment(comment);\n      if (shouldPrint === PRINT_COMMENT_HINT.DEFER) {\n        hasLoc = false;\n        break;\n      }\n      if (hasLoc && comment.loc && shouldPrint === PRINT_COMMENT_HINT.ALLOW) {\n        const commentStartLine = comment.loc.start.line;\n        const commentEndLine = comment.loc.end.line;\n        if (type === COMMENT_TYPE.LEADING) {\n          let offset = 0;\n          if (i === 0) {\n            // Because currently we cannot handle blank lines before leading comments,\n            // we always wrap before and after multi-line comments.\n            if (\n              this._buf.hasContent() &&\n              (comment.type === \"CommentLine\" ||\n                commentStartLine !== commentEndLine)\n            ) {\n              offset = leadingCommentNewline = 1;\n            }\n          } else {\n            offset = commentStartLine - lastLine;\n          }\n          lastLine = commentEndLine;\n\n          maybeNewline(offset);\n          this._printComment(comment, COMMENT_SKIP_NEWLINE.ALL);\n\n          if (i + 1 === len) {\n            maybeNewline(\n              Math.max(nodeStartLine - lastLine, leadingCommentNewline),\n            );\n            lastLine = nodeStartLine;\n          }\n        } else if (type === COMMENT_TYPE.INNER) {\n          const offset =\n            commentStartLine - (i === 0 ? nodeStartLine : lastLine);\n          lastLine = commentEndLine;\n\n          maybeNewline(offset);\n          this._printComment(comment, COMMENT_SKIP_NEWLINE.ALL);\n\n          if (i + 1 === len) {\n            maybeNewline(Math.min(1, nodeEndLine - lastLine)); // TODO: Improve here when inner comments processing is stronger\n            lastLine = nodeEndLine;\n          }\n        } else {\n          const offset =\n            commentStartLine - (i === 0 ? nodeEndLine - lineOffset : lastLine);\n          lastLine = commentEndLine;\n\n          maybeNewline(offset);\n          this._printComment(comment, COMMENT_SKIP_NEWLINE.ALL);\n        }\n      } else {\n        hasLoc = false;\n        if (shouldPrint !== PRINT_COMMENT_HINT.ALLOW) {\n          continue;\n        }\n\n        if (len === 1) {\n          const singleLine = comment.loc\n            ? comment.loc.start.line === comment.loc.end.line\n            : !HAS_NEWLINE.test(comment.value);\n\n          const shouldSkipNewline =\n            singleLine &&\n            !isStatement(node) &&\n            !isClassBody(parent) &&\n            !isTSInterfaceBody(parent) &&\n            !isTSEnumDeclaration(parent);\n\n          if (type === COMMENT_TYPE.LEADING) {\n            this._printComment(\n              comment,\n              (shouldSkipNewline && node.type !== \"ObjectExpression\") ||\n                (singleLine && isFunction(parent, { body: node }))\n                ? COMMENT_SKIP_NEWLINE.ALL\n                : COMMENT_SKIP_NEWLINE.DEFAULT,\n            );\n          } else if (shouldSkipNewline && type === COMMENT_TYPE.TRAILING) {\n            this._printComment(comment, COMMENT_SKIP_NEWLINE.ALL);\n          } else {\n            this._printComment(comment, COMMENT_SKIP_NEWLINE.DEFAULT);\n          }\n        } else if (\n          type === COMMENT_TYPE.INNER &&\n          !(node.type === \"ObjectExpression\" && node.properties.length > 1) &&\n          node.type !== \"ClassBody\" &&\n          node.type !== \"TSInterfaceBody\"\n        ) {\n          // class X {\n          //   /*:: a: number*/\n          //   /*:: b: ?string*/\n          // }\n\n          this._printComment(\n            comment,\n            i === 0\n              ? COMMENT_SKIP_NEWLINE.LEADING\n              : i === len - 1\n                ? COMMENT_SKIP_NEWLINE.TRAILING\n                : COMMENT_SKIP_NEWLINE.DEFAULT,\n          );\n        } else {\n          this._printComment(comment, COMMENT_SKIP_NEWLINE.DEFAULT);\n        }\n      }\n    }\n\n    if (type === COMMENT_TYPE.TRAILING && hasLoc && lastLine) {\n      this._lastCommentLine = lastLine;\n    }\n  }\n}\n\n// Expose the node type functions and helpers on the prototype for easy usage.\nObject.assign(Printer.prototype, generatorFunctions);\n\nif (!process.env.BABEL_8_BREAKING) {\n  // @ts-ignore(Babel 7 vs Babel 8) Babel 7 has Noop print method\n  Printer.prototype.Noop = function Noop(this: Printer) {};\n}\n\ntype GeneratorFunctions = typeof generatorFunctions;\n// eslint-disable-next-line @typescript-eslint/no-empty-object-type\ninterface Printer extends GeneratorFunctions {}\nexport default Printer;\n\nfunction commaSeparator(this: Printer) {\n  this.token(\",\");\n  this.space();\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,OAAA,GAAAC,OAAA;AAEA,IAAAC,CAAA,GAAAD,OAAA;AAEA,IAAAE,EAAA,GAAAF,OAAA;AAUA,IAAAG,kBAAA,GAAAH,OAAA;AAA4D;EAT1DI,UAAU;EACVC,WAAW;EACXC,WAAW;EACXC,iBAAiB;EACjBC;AAAmB,IAAAN,EAAA;AAarB,MAAMO,mBAAmB,GAAG,IAAI;AAChC,MAAMC,oBAAoB,GAAG,OAAO;AACpC,MAAMC,WAAW,GAAG,oBAAoB;AACxC,MAAMC,gCAAgC,GAAG,yBAAyB;AAElE,MAAM;EAAEC;AAAY,CAAC,GAAGZ,CAAC;AAiFzB,MAAMa,OAAO,CAAC;EACZC,WAAWA,CAACC,MAAc,EAAEC,GAAc,EAAE;IAAA,KAa5CC,kBAAkB,GAAY,KAAK;IAAA,KAUnCC,YAAY,GAAW,CAAC;IAAA,KAGxBC,YAAY,GAAW,IAAI;IAAA,KAC3BC,OAAO,GAAW,CAAC;IAAA,KACnBC,aAAa,GAAW,CAAC;IAAA,KACzBC,UAAU,GAAY,KAAK;IAAA,KAC3BC,sBAAsB,GAAgC,IAAI;IAAA,KAC1DC,iBAAiB,GAAY,KAAK;IAAA,KAClCC,4BAA4B,GAAY,KAAK;IAAA,KAC7CC,gBAAgB,GAAG,IAAIC,GAAG,CAAY,CAAC;IAAA,KACvCC,gBAAgB,GAAG,KAAK;IAAA,KACxBC,aAAa,GAAG,KAAK;IAAA,KACrBC,YAAY,GAAG,KAAK;IAAA,KACpBC,gBAAgB,GAAG,CAAC;IAAA,KACpBC,iBAAiB,GAAY,KAAK;IAAA,KAClCC,oBAAoB,GAAY,IAAI;IAtClC,IAAI,CAAClB,MAAM,GAAGA,MAAM;IAEpB,IAAI,CAACM,aAAa,GAAGN,MAAM,CAACmB,MAAM,CAACC,KAAK,CAACC,MAAM;IAE/C,IAAI,CAACC,SAAS,GAAGrB,GAAG,oBAAHA,GAAG,CAAEqB,SAAS;IAE/B,IAAI,CAACC,IAAI,GAAG,IAAIC,eAAM,CAACvB,GAAG,EAAED,MAAM,CAACmB,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC;EACrD;EAMAK,qBAAqBA,CAACC,GAAY,EAAE;IAClC,MAAMC,GAAG,GAAG,IAAI,CAACzB,kBAAkB;IACnC,IAAIyB,GAAG,KAAKD,GAAG,EAAE,OAAO,MAAM,CAAC,CAAC;IAChC,IAAI,CAACxB,kBAAkB,GAAGwB,GAAG;IAC7B,OAAO,MAAM;MACX,IAAI,CAACxB,kBAAkB,GAAGyB,GAAG;IAC/B,CAAC;EACH;EAoBAC,QAAQA,CAACC,GAAW,EAAE;IACpB,IAAI,CAACC,KAAK,CAACD,GAAG,CAAC;IACf,IAAI,CAACE,mBAAmB,CAAC,CAAC;IAE1B,OAAO,IAAI,CAACR,IAAI,CAACS,GAAG,CAAC,CAAC;EACxB;EAMAb,MAAMA,CAAA,EAAS;IACb,IAAI,IAAI,CAACnB,MAAM,CAACiC,OAAO,IAAI,IAAI,CAACjC,MAAM,CAACkC,OAAO,EAAE;IAEhD,IAAI,CAAC7B,OAAO,EAAE;EAChB;EAMA8B,MAAMA,CAAA,EAAS;IACb,IAAI,IAAI,CAACnC,MAAM,CAACiC,OAAO,IAAI,IAAI,CAACjC,MAAM,CAACkC,OAAO,EAAE;IAEhD,IAAI,CAAC7B,OAAO,EAAE;EAChB;EAMA+B,SAASA,CAACC,KAAc,GAAG,KAAK,EAAQ;IACtC,IAAI,CAACN,mBAAmB,CAAC,CAAC;IAC1B,IAAIM,KAAK,EAAE;MACT,IAAI,CAACC,WAAW,GAAoB,CAAC;IACvC,CAAC,MAAM;MACL,IAAI,CAACC,MAAM,GAAoB,CAAC;IAClC;IACA,IAAI,CAAC9B,iBAAiB,GAAG,KAAK;EAChC;EAMA+B,UAAUA,CAACC,IAAY,EAAQ;IAC7B,IAAI,IAAI,CAACzC,MAAM,CAAC0C,QAAQ,EAAE;MACxB,IAAI,CAACnB,IAAI,CAACoB,mBAAmB,CAAC,CAAC;IACjC;IACA,IAAI,CAACC,gBAAgB,CAAC,KAAK,EAAEH,IAAI,CAACI,GAAG,EAAE,CAAC,CAAC,CAAC;IAC1C,IAAI,CAACC,SAAK,IAAI,CAAC;EACjB;EAEAC,WAAWA,CAACN,IAAY,EAAQ;IAC9B,IAAI,CAACG,gBAAgB,CAAC,KAAK,EAAEH,IAAI,CAACI,GAAG,EAAE,CAAC,CAAC,CAAC;IAC1C,IAAI,CAACC,SAAK,GAAI,CAAC;EACjB;EAMAE,KAAKA,CAACX,KAAc,GAAG,KAAK,EAAQ;IAClC,IAAI,IAAI,CAACrC,MAAM,CAACiC,OAAO,EAAE;IAEzB,IAAII,KAAK,EAAE;MACT,IAAI,CAACY,MAAM,CAAC,CAAC;IACf,CAAC,MAAM,IAAI,IAAI,CAAC1B,IAAI,CAAC2B,UAAU,CAAC,CAAC,EAAE;MACjC,MAAMC,MAAM,GAAG,IAAI,CAACC,WAAW,CAAC,CAAC;MACjC,IAAID,MAAM,OAAoB,IAAIA,MAAM,OAAuB,EAAE;QAC/D,IAAI,CAACF,MAAM,CAAC,CAAC;MACf;IACF;EACF;EAMAI,IAAIA,CAACC,GAAW,EAAEC,qBAA8B,GAAG,KAAK,EAAQ;IAC9D,IAAI,CAACpD,YAAY,GAAG,CAAC;IAErB,IAAI,CAACqD,wBAAwB,CAAC,CAAC;IAG/B,IACE,IAAI,CAAC1C,aAAa,IACjB,IAAI,CAACC,YAAY,IAAIuC,GAAG,CAACG,UAAU,CAAC,CAAC,CAAC,OAAqB,EAC5D;MACA,IAAI,CAACR,MAAM,CAAC,CAAC;IACf;IAEA,IAAI,CAAClB,mBAAmB,CAAC,CAAC;IAC1B,IAAI,CAAC2B,OAAO,CAACJ,GAAG,EAAE,KAAK,CAAC;IAExB,IAAI,CAACxC,aAAa,GAAG,IAAI;IACzB,IAAI,CAACL,iBAAiB,GAAG8C,qBAAqB;EAChD;EAMAI,MAAMA,CAACL,GAAW,EAAEK,MAAe,EAAQ;IAEzC,SAASC,mBAAmBA,CAACN,GAAW,EAAE;MACxC,IAAIA,GAAG,CAACjC,MAAM,GAAG,CAAC,IAAIiC,GAAG,CAACG,UAAU,CAAC,CAAC,CAAC,OAAqB,EAAE;QAC5D,MAAMI,UAAU,GAAGP,GAAG,CAACG,UAAU,CAAC,CAAC,CAAC;QACpC,OACEI,UAAU,OAAyB,IACnCA,UAAU,QAAyB,IACnCA,UAAU,QAAyB;MAEvC;MACA,OAAO,KAAK;IACd;IACA,IAAI,CAACR,IAAI,CAACC,GAAG,CAAC;IAId,IAAI,CAACzC,gBAAgB,GACnBiD,MAAM,CAACC,SAAS,CAACJ,MAAM,CAAC,IACxB,CAACC,mBAAmB,CAACN,GAAG,CAAC,IACzB,CAAC7D,mBAAmB,CAACuE,IAAI,CAACV,GAAG,CAAC,IAC9B,CAAC5D,oBAAoB,CAACsE,IAAI,CAACV,GAAG,CAAC,IAC/BA,GAAG,CAACG,UAAU,CAACH,GAAG,CAACjC,MAAM,GAAG,CAAC,CAAC,OAAkB;EACpD;EAKAyB,KAAKA,CAACQ,GAAW,EAAEW,YAAY,GAAG,KAAK,EAAQ;IAC7C,IAAI,CAAC9D,YAAY,GAAG,CAAC;IAErB,IAAI,CAACqD,wBAAwB,CAAC,CAAC;IAE/B,MAAMU,QAAQ,GAAG,IAAI,CAACd,WAAW,CAAC,CAAC;IACnC,MAAMe,QAAQ,GAAGb,GAAG,CAACG,UAAU,CAAC,CAAC,CAAC;IAClC,IACGS,QAAQ,OAA8B,KAGpCZ,GAAG,KAAK,IAAI,IAEXa,QAAQ,OAAuB,CAAC,IAEnCA,QAAQ,OAAuB,IAAID,QAAQ,OAAwB,IACnEC,QAAQ,OAAmB,IAAID,QAAQ,OAAoB,IAE3DC,QAAQ,OAAkB,IAAI,IAAI,CAACtD,gBAAiB,EACrD;MACA,IAAI,CAACoC,MAAM,CAAC,CAAC;IACf;IAEA,IAAI,CAAClB,mBAAmB,CAAC,CAAC;IAC1B,IAAI,CAAC2B,OAAO,CAACJ,GAAG,EAAEW,YAAY,CAAC;IAC/B,IAAI,CAACxD,iBAAiB,GAAG,KAAK;EAChC;EAEA2D,SAASA,CAACC,IAAY,EAAQ;IAC5B,IAAI,CAAClE,YAAY,GAAG,CAAC;IAErB,IAAI,CAACqD,wBAAwB,CAAC,CAAC;IAE/B,MAAMU,QAAQ,GAAG,IAAI,CAACd,WAAW,CAAC,CAAC;IACnC,IAEGiB,IAAI,OAAuB,IAAIH,QAAQ,OAAuB,IAC9DG,IAAI,OAAmB,IAAIH,QAAQ,OAAoB,IAEvDG,IAAI,OAAkB,IAAI,IAAI,CAACxD,gBAAiB,EACjD;MACA,IAAI,CAACoC,MAAM,CAAC,CAAC;IACf;IAEA,IAAI,CAAClB,mBAAmB,CAAC,CAAC;IAC1B,IAAI,CAACO,WAAW,CAAC+B,IAAI,CAAC;IACtB,IAAI,CAAC5D,iBAAiB,GAAG,KAAK;EAChC;EAQA6D,OAAOA,CAACC,CAAS,GAAG,CAAC,EAAElC,KAAe,EAAQ;IAC5C,IAAIkC,CAAC,IAAI,CAAC,EAAE;IAEZ,IAAI,CAAClC,KAAK,EAAE;MACV,IAAI,IAAI,CAACrC,MAAM,CAACwE,WAAW,IAAI,IAAI,CAACxE,MAAM,CAACiC,OAAO,EAAE;MAEpD,IAAI,IAAI,CAACjC,MAAM,CAACkC,OAAO,EAAE;QACvB,IAAI,CAACc,KAAK,CAAC,CAAC;QACZ;MACF;IACF;IAEA,IAAIuB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC;IAEhBA,CAAC,IAAI,IAAI,CAAChD,IAAI,CAACkD,eAAe,CAAC,CAAC;IAEhC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,CAAC,EAAEG,CAAC,EAAE,EAAE;MAC1B,IAAI,CAACC,QAAQ,CAAC,CAAC;IACjB;IAEA;EACF;EAEAC,QAAQA,CAACP,IAAY,EAAW;IAC9B,OAAO,IAAI,CAACjB,WAAW,CAAC,CAAC,KAAKiB,IAAI;EACpC;EAEAjB,WAAWA,CAAA,EAAW;IACpB,OAAO,IAAI,CAAC7B,IAAI,CAAC6B,WAAW,CAAC,CAAC;EAChC;EAEAyB,sBAAsBA,CAAA,EAAW;IAC/B,OAAO,IAAI,CAACtD,IAAI,CAACsD,sBAAsB,CAAC,CAAC;EAC3C;EAEAC,qBAAqBA,CAAA,EAAS;IAC5B,IAAI,CAACvD,IAAI,CAACuD,qBAAqB,CAAC,CAAC;EACnC;EAEAC,WAAWA,CAAClC,GAAoB,EAAEmC,EAAc,EAAE;IAChD,IAAI,CAACnC,GAAG,EAAE;MACRmC,EAAE,CAAC,CAAC;MACJ;IACF;IAEA,IAAI,CAACC,QAAQ,CAAC,OAAO,EAAEpC,GAAG,CAAC;IAE3B,IAAI,CAACtB,IAAI,CAACwD,WAAW,CAAClC,GAAG,EAAEmC,EAAE,CAAC;EAChC;EAEAE,MAAMA,CAACC,IAAqB,EAAEtC,GAAoB,EAAQ;IACxD,IAAI,CAACA,GAAG,EAAE;IAEV,IAAI,CAACoC,QAAQ,CAACE,IAAI,EAAEtC,GAAG,CAAC;IAExB,IAAI,CAACtB,IAAI,CAAC2D,MAAM,CAACC,IAAI,EAAEtC,GAAG,CAAC;EAC7B;EAEAD,gBAAgBA,CACduC,IAAqB,EACrBtC,GAAoB,EACpBuC,YAAoB,EACd;IACN,IAAI,CAACvC,GAAG,EAAE;IAEV,IAAI,CAACoC,QAAQ,CAACE,IAAI,EAAEtC,GAAG,CAAC;IAExB,IAAI,CAACtB,IAAI,CAACqB,gBAAgB,CAACuC,IAAI,EAAEtC,GAAG,EAAEuC,YAAY,CAAC;EACrD;EAEAC,oBAAoBA,CAACC,cAAsB,EAAEC,GAAS,EAAQ;IAC5D,IAAI,CAAC,IAAI,CAAChE,IAAI,CAACiE,cAAc,EAAE;IAE/B,MAAMC,cAAc,GAAG,IAAI,CAAClE,IAAI,CAACmE,eAAe;IAChDD,cAAc,CAACE,iBAAiB,GAAGJ,GAAG;IACtCE,cAAc,CAACH,cAAc,GAAGA,cAAc;EAChD;EAEArC,MAAMA,CAAA,EAAS;IACb,IAAI,CAACV,MAAM,GAAgB,CAAC;EAC9B;EAEAoC,QAAQA,CAAA,EAAS;IACf,IAAI,CAACpC,MAAM,GAAmB,CAAC;EACjC;EAEAmB,OAAOA,CAACJ,GAAW,EAAEW,YAAqB,EAAQ;IAChD,IAAI,CAAC2B,cAAc,CAACtC,GAAG,CAAC;IACxB,IAAI,CAACuC,YAAY,CAACvC,GAAG,CAACG,UAAU,CAAC,CAAC,CAAC,CAAC;IAEpC,IAAI,CAAClC,IAAI,CAACuE,MAAM,CAACxC,GAAG,EAAEW,YAAY,CAAC;IAGnC,IAAI,CAACnD,aAAa,GAAG,KAAK;IAC1B,IAAI,CAACD,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAACE,YAAY,GAAG,KAAK;EAC3B;EAEAuB,WAAWA,CAAC+B,IAAY,EAAQ;IAC9B,IAAI,CAAC0B,kBAAkB,CAAC1B,IAAI,CAAC;IAC7B,IAAI,CAACwB,YAAY,CAACxB,IAAI,CAAC;IAEvB,IAAI,CAAC9C,IAAI,CAACyE,UAAU,CAAC3B,IAAI,CAAC;IAG1B,IAAI,CAACvD,aAAa,GAAG,KAAK;IAC1B,IAAI,CAACD,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAACE,YAAY,GAAG,KAAK;EAC3B;EAEAwB,MAAMA,CAAC8B,IAAY,EAAE;IACnB,IAAI,CAAC0B,kBAAkB,CAAC1B,IAAI,CAAC;IAC7B,IAAI,CAACwB,YAAY,CAACxB,IAAI,CAAC;IAEvB,IAAI,CAAC9C,IAAI,CAAC0E,KAAK,CAAC5B,IAAI,CAAC;IAErB,IAAI,CAACvD,aAAa,GAAG,KAAK;IAC1B,IAAI,CAACD,gBAAgB,GAAG,KAAK;EAC/B;EAEAgF,YAAYA,CAACK,SAAiB,EAAQ;IAEpC,IACE,IAAI,CAAC7F,OAAO,IACZ6F,SAAS,OAAuB,IAChC,IAAI,CAACtB,QAAQ,GAAmB,CAAC,EACjC;MACA,IAAI,CAACrD,IAAI,CAAC4E,gBAAgB,CAAC,IAAI,CAACC,UAAU,CAAC,CAAC,CAAC;IAC/C;EACF;EAEAC,aAAaA,CAACH,SAAiB,EAAE;IAE/B,IACE,IAAI,CAAC7F,OAAO,IACZ6F,SAAS,OAAuB,IAChC,IAAI,CAACtB,QAAQ,GAAmB,CAAC,EACjC;MACA,OAAO,IAAI;IACb;EACF;EAEAmB,kBAAkBA,CAAC1B,IAAY,EAAQ;IAErC,MAAMiC,qBAAqB,GAAG,IAAI,CAAC9F,sBAAsB;IACzD,IAAI,CAAC8F,qBAAqB,EAAE;IAS5B,IAAIjC,IAAI,OAAoB,EAAE;MAE5B;IACF;IAGA,IAAIA,IAAI,OAAuB,EAAE;MAC/B,IAAI,CAAC7D,sBAAsB,GAAG,IAAI;MAClC;IACF;IAEA,IAAI,CAACsC,SAAK,GAAI,CAAC;IACf,IAAI,CAAC3B,MAAM,CAAC,CAAC;IACbmF,qBAAqB,CAACC,OAAO,GAAG,IAAI;EACtC;EAEAX,cAAcA,CAACtC,GAAW,EAAQ;IAEhC,MAAMgD,qBAAqB,GAAG,IAAI,CAAC9F,sBAAsB;IACzD,IAAI,CAAC8F,qBAAqB,EAAE;IAS5B,MAAME,GAAG,GAAGlD,GAAG,CAACjC,MAAM;IAEtB,IAAIkD,CAAC;IACL,KAAKA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGiC,GAAG,IAAIlD,GAAG,CAACG,UAAU,CAACc,CAAC,CAAC,OAAoB,EAAEA,CAAC,EAAE,EAAE;IACnE,IAAIA,CAAC,KAAKiC,GAAG,EAAE;MAEb;IACF;IAGA,MAAMC,GAAG,GAAGnD,GAAG,CAACG,UAAU,CAACc,CAAC,CAAC;IAC7B,IAAIkC,GAAG,OAAuB,EAAE;MAC9B,IAEEA,GAAG,OAAoB,IAEvBlC,CAAC,GAAG,CAAC,KAAKiC,GAAG,EACb;QAEA,IAAI,CAAChG,sBAAsB,GAAG,IAAI;QAClC;MACF;MAEA,MAAMkG,OAAO,GAAGpD,GAAG,CAACG,UAAU,CAACc,CAAC,GAAG,CAAC,CAAC;MAErC,IAAImC,OAAO,OAAuB,EAAE;QAElC;MACF,CAAC,MAAM,IAAIA,OAAO,OAAoB,EAAE;QAGtC,IAAI,CAAClG,sBAAsB,GAAG,IAAI;QAClC;MACF;IACF;IAEA,IAAI,CAACsC,SAAK,GAAI,CAAC;IACf,IAAI,CAAC3B,MAAM,CAAC,CAAC;IACbmF,qBAAqB,CAACC,OAAO,GAAG,IAAI;EACtC;EAEAI,OAAOA,CAACC,IAAY,EAAE;IACpB,IAAI,CAAC,IAAI,CAAC5G,MAAM,CAACwE,WAAW,EAAE;IAG9B,MAAMqC,KAAK,GAAGD,IAAI,GAAG,IAAI,CAACrF,IAAI,CAACuF,cAAc,CAAC,CAAC;IAE/C,KAAK,IAAIvC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGsC,KAAK,EAAEtC,CAAC,EAAE,EAAE;MAC9B,IAAI,CAACI,QAAQ,CAAC,CAAC;IACjB;EACF;EAEAM,QAAQA,CAACE,IAAqB,EAAEtC,GAAS,EAAE;IAAA,IAAAkE,SAAA;IACzC,IAAI,CAAC,IAAI,CAAC/G,MAAM,CAACwE,WAAW,EAAE;IAG9B,MAAMoC,IAAI,GAAG/D,GAAG,aAAAkE,SAAA,GAAHlE,GAAG,CAAGsC,IAAI,CAAC,qBAAX4B,SAAA,CAAaH,IAAI;IAC9B,IAAIA,IAAI,IAAI,IAAI,EAAE;MAChB,MAAMC,KAAK,GAAGD,IAAI,GAAG,IAAI,CAACrF,IAAI,CAACuF,cAAc,CAAC,CAAC;MAE/C,KAAK,IAAIvC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGsC,KAAK,EAAEtC,CAAC,EAAE,EAAE;QAC9B,IAAI,CAACI,QAAQ,CAAC,CAAC;MACjB;IACF;EACF;EAMAyB,UAAUA,CAAA,EAAW;IACnB,OAAO,IAAI,CAAC9F,aAAa,GAAG,IAAI,CAACD,OAAO;EAC1C;EAEA2G,mBAAmBA,CAACvE,IAAY,EAAEwE,OAAgB,EAAE;IAgBlD,IAAIA,OAAO,EAAE;MACX,IAAI,CAACxG,iBAAiB,GAAG,IAAI;MAC7B,IAAI,CAACqB,KAAK,CAACW,IAAI,CAAC;IAClB,CAAC,MAAM;MACL,MAAMyE,eAAe,GAAG;QACtBX,OAAO,EAAE;MACX,CAAC;MACD,IAAI,CAAC/F,sBAAsB,GAAG0G,eAAe;MAC7C,IAAI,CAACpF,KAAK,CAACW,IAAI,CAAC;MAIhB,IAAIyE,eAAe,CAACX,OAAO,EAAE;QAC3B,IAAI,CAACpE,MAAM,CAAC,CAAC;QACb,IAAI,CAACmC,OAAO,CAAC,CAAC;QACd,IAAI,CAACxB,SAAK,GAAI,CAAC;MACjB;IACF;EACF;EAEAhB,KAAKA,CACHW,IAAmB,EACnBc,qBAA+B,EAG/B4D,0BAAmC,EACnCC,WAAqB,EACrB;IAAA,IAAAC,WAAA,EAAAC,qBAAA;IACA,IAAI,CAAC7E,IAAI,EAAE;IAEX,IAAI,CAACxB,iBAAiB,GAAG,KAAK;IAE9B,MAAMsG,QAAQ,GAAG9E,IAAI,CAAC+E,IAAI;IAC1B,MAAMxH,MAAM,GAAG,IAAI,CAACA,MAAM;IAE1B,MAAMyH,UAAU,GAAGzH,MAAM,CAACkC,OAAO;IACjC,IAEEO,IAAI,CAACiF,QAAQ,EACb;MACA1H,MAAM,CAACkC,OAAO,GAAG,IAAI;IACvB;IAEA,MAAMyF,WAAW,GACf,IAAI,CACFJ,QAAQ,CAOT;IACH,IAAII,WAAW,KAAKC,SAAS,EAAE;MAC7B,MAAM,IAAIC,cAAc,CACtB,wBAAwBC,IAAI,CAACC,SAAS,CACpCR,QACF,CAAC,qBAAqBO,IAAI,CAACC,SAAS,CAACtF,IAAI,CAAC1C,WAAW,CAACiI,IAAI,CAAC,EAC7D,CAAC;IACH;IAEA,MAAMC,MAAM,GAAG,IAAI,CAAC7H,YAAY;IAChC,IAAI,CAACA,YAAY,GAAGqC,IAAI;IAExB,MAAMyF,QAAQ,GAAG,IAAI,CAAC3H,UAAU;IAChC,IAAI,CAACA,UAAU,GAAGkC,IAAI,CAACI,GAAG,IAAI,IAAI;IAClC,IAAI,CAACd,mBAAmB,CAAC,IAAI,CAACxB,UAAU,IAAI,CAAC2H,QAAQ,CAAC;IAEtD,MAAMC,aAAa,IAAAd,WAAA,GAAG5E,IAAI,CAAC2F,KAAK,qBAAVf,WAAA,CAAYc,aAAoC;IACtE,IAAIE,iBAAiB,GACnBjB,WAAW,IACVe,aAAa,IACZnI,MAAM,CAACsI,oBAAoB,IAC3Bf,QAAQ,KAAK,oBAAqB,IACpC1H,WAAW,CAAC4C,IAAI,EAAEwF,MAAM,EAAE,IAAI,CAAC9H,YAAY,EAAE,IAAI,CAACD,kBAAkB,CAAC;IAEvE,IACE,CAACmI,iBAAiB,IAClBF,aAAa,KAAAb,qBAAA,GACb7E,IAAI,CAAC8F,eAAe,aAApBjB,qBAAA,CAAsBjG,MAAM,IAC5BoB,IAAI,CAAC8F,eAAe,CAAC,CAAC,CAAC,CAACf,IAAI,KAAK,cAAc,EAC/C;MACA,MAAMgB,UAAU,GAAGP,MAAM,oBAANA,MAAM,CAAET,IAAI;MAC/B,QAAQgB,UAAU;QAChB,KAAK,qBAAqB;QAC1B,KAAK,oBAAoB;QACzB,KAAK,sBAAsB;QAC3B,KAAK,iBAAiB;UACpB;QACF,KAAK,gBAAgB;QACrB,KAAK,wBAAwB;QAC7B,KAAK,eAAe;UAClB,IAAIP,MAAM,CAACQ,MAAM,KAAKhG,IAAI,EAAE;QAE9B;UACE4F,iBAAiB,GAAG,IAAI;MAC5B;IACF;IAEA,IAAIK,sBAAsB;IAC1B,IAAIL,iBAAiB,EAAE;MACrB,IAAI,CAACvF,SAAK,GAAI,CAAC;MACf,IAAI,CAAC7B,iBAAiB,GAAG,KAAK;MAC9ByH,sBAAsB,GAAG,IAAI,CAACjH,qBAAqB,CAAC,KAAK,CAAC;IAC5D;IAEA,IAAI,CAACT,gBAAgB,GAAG,CAAC;IAEzB,IAAI,CAAC2H,qBAAqB,CAAClG,IAAI,EAAEwF,MAAM,CAAC;IAExC,MAAMpF,GAAG,GAAG0E,QAAQ,KAAK,SAAS,IAAIA,QAAQ,KAAK,MAAM,GAAG,IAAI,GAAG9E,IAAI,CAACI,GAAG;IAE3E,IAAI,CAACkC,WAAW,CACdlC,GAAG,EAKH8E,WAAW,CAACiB,IAAI,CAAC,IAAI,EAAEnG,IAAI,EAAEwF,MAAM,CACrC,CAAC;IAED,IAAII,iBAAiB,EAAE;MACrB,IAAI,CAACQ,sBAAsB,CAACpG,IAAI,EAAEwF,MAAM,CAAC;MACzC,IAAI,CAACnF,SAAK,GAAI,CAAC;MACf,IAAI,CAACrC,iBAAiB,GAAG8C,qBAAqB;MAC9CmF,sBAAsB,CAAC,CAAC;IAC1B,CAAC,MAAM,IAAInF,qBAAqB,IAAI,CAAC,IAAI,CAAC9C,iBAAiB,EAAE;MAC3D,IAAI,CAACA,iBAAiB,GAAG,IAAI;MAC7B,IAAI,CAACoI,sBAAsB,CAACpG,IAAI,EAAEwF,MAAM,CAAC;IAC3C,CAAC,MAAM;MACL,IAAI,CAACY,sBAAsB,CAACpG,IAAI,EAAEwF,MAAM,EAAEd,0BAA0B,CAAC;IACvE;IAGA,IAAI,CAAC/G,YAAY,GAAG6H,MAAM;IAC1BjI,MAAM,CAACkC,OAAO,GAAGuF,UAAU;IAC3B,IAAI,CAAClH,UAAU,GAAG2H,QAAQ;IAE1B,IAAI,CAACjH,iBAAiB,GAAG,KAAK;EAChC;EAEAc,mBAAmBA,CAAC+G,uBAAiC,EAAE;IACrD,IAAIA,uBAAuB,EAAE,IAAI,CAACC,sBAAsB,CAAC,CAAC;IAC1D,IAAI,CAAC,IAAI,CAACxI,UAAU,EAAE,IAAI,CAACyI,qBAAqB,CAAC,CAAC;EACpD;EAEAD,sBAAsBA,CAAA,EAAG;IACvB,IAAI,IAAI,CAACrI,4BAA4B,EAAE;IACvC,IAAI,CAACA,4BAA4B,GAAG,IAAI;IAExC,MAAMuI,OAAO,GAAG,IAAI,CAACjJ,MAAM,CAACkJ,sBAAsB;IAClD,IAAID,OAAO,EAAE;MACX,IAAI,CAACE,aAAa,CAChB;QACE3B,IAAI,EAAE,cAAc;QACpB4B,KAAK,EAAEH;MACT,CAAC,GAEH,CAAC;IACH;EACF;EAEAD,qBAAqBA,CAAA,EAAG;IACtB,IAAI,CAAC,IAAI,CAACtI,4BAA4B,EAAE;IACxC,IAAI,CAACA,4BAA4B,GAAG,KAAK;IAEzC,MAAMuI,OAAO,GAAG,IAAI,CAACjJ,MAAM,CAACqJ,qBAAqB;IACjD,IAAIJ,OAAO,EAAE;MACX,IAAI,CAACE,aAAa,CAChB;QACE3B,IAAI,EAAE,cAAc;QACpB4B,KAAK,EAAEH;MACT,CAAC,GAEH,CAAC;IACH;EACF;EAEAK,cAAcA,CACZ7G,IAMa,EACO;IACpB,MAAM2F,KAAK,GAAG3F,IAAI,CAAC2F,KAAK;IACxB,IACE,CAAAA,KAAK,oBAALA,KAAK,CAAEmB,GAAG,KAAI,IAAI,IAClBnB,KAAK,CAACoB,QAAQ,IAAI,IAAI,IACtB/G,IAAI,CAAC2G,KAAK,KAAKhB,KAAK,CAACoB,QAAQ,EAC7B;MAEA,OAAOpB,KAAK,CAACmB,GAAG;IAClB;EACF;EAEAE,SAASA,CACPC,KAAuC,EACvCC,IAAsB,GAAG,CAAC,CAAC,EAC3B;IACA,IAAI,EAACD,KAAK,YAALA,KAAK,CAAErI,MAAM,GAAE;IAEpB,IAAI;MAAEF;IAAO,CAAC,GAAGwI,IAAI;IAErB,IAAIxI,MAAM,IAAI,IAAI,IAAI,IAAI,CAACnB,MAAM,CAACwE,WAAW,EAAE;MAAA,IAAAoF,YAAA;MAC7C,MAAMC,SAAS,IAAAD,YAAA,GAAGF,KAAK,CAAC,CAAC,CAAC,CAAC7G,GAAG,qBAAZ+G,YAAA,CAAcE,KAAK,CAAClD,IAAI;MAC1C,IAAIiD,SAAS,IAAI,IAAI,IAAIA,SAAS,KAAK,IAAI,CAACtI,IAAI,CAACuF,cAAc,CAAC,CAAC,EAAE;QACjE3F,MAAM,GAAG,IAAI;MACf;IACF;IAEA,IAAIA,MAAM,EAAE,IAAI,CAACA,MAAM,CAAC,CAAC;IAEzB,MAAM4I,WAA+B,GAAG;MACtCC,WAAW,EAAEL,IAAI,CAACK,WAAW;MAC7BC,iBAAiB,EAAE;IACrB,CAAC;IAED,MAAMC,SAAS,GAAGP,IAAI,CAACO,SAAS,GAAGP,IAAI,CAACO,SAAS,CAACtB,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI;IAEnE,MAAMpC,GAAG,GAAGkD,KAAK,CAACrI,MAAM;IACxB,KAAK,IAAIkD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGiC,GAAG,EAAEjC,CAAC,EAAE,EAAE;MAC5B,MAAM9B,IAAI,GAAGiH,KAAK,CAACnF,CAAC,CAAC;MACrB,IAAI,CAAC9B,IAAI,EAAE;MAEX,IAAIkH,IAAI,CAACQ,SAAS,EAAE,IAAI,CAACC,aAAa,CAAC7F,CAAC,KAAK,CAAC,EAAEwF,WAAW,CAAC;MAE5D,IAAI,CAACjI,KAAK,CAACW,IAAI,EAAEmF,SAAS,EAAE+B,IAAI,CAACxC,0BAA0B,IAAI,CAAC,CAAC;MAEjEwC,IAAI,CAACU,QAAQ,YAAbV,IAAI,CAACU,QAAQ,CAAG5H,IAAI,EAAE8B,CAAC,CAAC;MAExB,IAAIA,CAAC,GAAGiC,GAAG,GAAG,CAAC,EAAE0D,SAAS,YAATA,SAAS,CAAG,CAAC;MAE9B,IAAIP,IAAI,CAACQ,SAAS,EAAE;QAAA,IAAAG,qBAAA;QAClB,IAAI,GAAAA,qBAAA,GAAC7H,IAAI,CAAC8H,gBAAgB,aAArBD,qBAAA,CAAuBjJ,MAAM,GAAE;UAClC,IAAI,CAACL,gBAAgB,GAAG,CAAC;QAC3B;QAEA,IAAIuD,CAAC,GAAG,CAAC,KAAKiC,GAAG,EAAE;UACjB,IAAI,CAAClC,OAAO,CAAC,CAAC,CAAC;QACjB,CAAC,MAAM;UAAA,IAAAkG,aAAA;UACL,MAAMC,QAAQ,GAAGf,KAAK,CAACnF,CAAC,GAAG,CAAC,CAAC;UAC7BwF,WAAW,CAACE,iBAAiB,GAAG,EAAAO,aAAA,GAAAC,QAAQ,CAAC5H,GAAG,qBAAZ2H,aAAA,CAAcV,KAAK,CAAClD,IAAI,KAAI,CAAC;UAE7D,IAAI,CAACwD,aAAa,CAAC,IAAI,EAAEL,WAAW,CAAC;QACvC;MACF;IACF;IAEA,IAAI5I,MAAM,EAAE,IAAI,CAACgB,MAAM,CAAC,CAAC;EAC3B;EAEAuI,wBAAwBA,CAACjI,IAAY,EAAE;IACrC,MAAMtB,MAAM,GAAGsB,IAAI,CAAC8F,eAAe,IAAI9F,IAAI,CAAC8F,eAAe,CAAClH,MAAM,GAAG,CAAC;IACtE,IAAIF,MAAM,EAAE,IAAI,CAACA,MAAM,CAAC,CAAC;IACzB,IAAI,CAACW,KAAK,CAACW,IAAI,CAAC;IAChB,IAAItB,MAAM,EAAE,IAAI,CAACgB,MAAM,CAAC,CAAC;EAC3B;EAEAwI,UAAUA,CAAC1C,MAA8C,EAAE;IACzD,MAAMxF,IAAI,GAAGwF,MAAM,CAAC2C,IAAI;IAExB,IAAInI,IAAI,CAAC+E,IAAI,KAAK,gBAAgB,EAAE;MAClC,IAAI,CAACxE,KAAK,CAAC,CAAC;IACd;IAEA,IAAI,CAAClB,KAAK,CAACW,IAAI,CAAC;EAClB;EAEAoG,sBAAsBA,CAACpG,IAAY,EAAEwF,MAAe,EAAE4C,UAAmB,EAAE;IACzE,MAAM;MAAEC,aAAa;MAAEP;IAAiB,CAAC,GAAG9H,IAAI;IAIhD,IAAIqI,aAAa,YAAbA,aAAa,CAAEzJ,MAAM,EAAE;MACzB,IAAI,CAAC0J,cAAc,IAEjBD,aAAa,EACbrI,IAAI,EACJwF,MAAM,EACN4C,UACF,CAAC;IACH;IACA,IAAIN,gBAAgB,YAAhBA,gBAAgB,CAAElJ,MAAM,EAAE;MAC5B,IAAI,CAAC0J,cAAc,IAEjBR,gBAAgB,EAChB9H,IAAI,EACJwF,MAAM,EACN4C,UACF,CAAC;IACH;EACF;EAEAlC,qBAAqBA,CAAClG,IAAY,EAAEwF,MAAc,EAAE;IAClD,MAAM+C,QAAQ,GAAGvI,IAAI,CAAC8F,eAAe;IACrC,IAAI,EAACyC,QAAQ,YAARA,QAAQ,CAAE3J,MAAM,GAAE;IACvB,IAAI,CAAC0J,cAAc,IAAuBC,QAAQ,EAAEvI,IAAI,EAAEwF,MAAM,CAAC;EACnE;EAEAzE,wBAAwBA,CAAA,EAAG;IACzB,IAAI,IAAI,CAACvC,iBAAiB,EAAE,IAAI,CAACgK,kBAAkB,CAAC,CAAC;IACrD,IAAI,CAAChK,iBAAiB,GAAG,IAAI;IAC7B,IAAI,CAACC,oBAAoB,GAAG,IAAI;EAClC;EAEA+J,kBAAkBA,CAAA,EAAG;IACnB,MAAMxI,IAAI,GAAG,IAAI,CAACrC,YAAY;IAC9B,MAAM4K,QAAQ,GAAGvI,IAAI,CAACqI,aAAa;IACnC,IAAI,EAACE,QAAQ,YAARA,QAAQ,CAAE3J,MAAM,GAAE;IAEvB,MAAM6J,QAAQ,GAAG,IAAI,CAACtG,QAAQ,GAAgB,CAAC;IAC/C,MAAMzD,MAAM,GAAG,IAAI,CAACD,oBAAoB;IACxC,MAAMiK,oBAAoB,GAAG,IAAI,CAACxK,gBAAgB,CAACyK,IAAI;IACvD,IAAIjK,MAAM,EAAE,IAAI,CAACA,MAAM,CAAC,CAAC;IACzB,IAAI,CAAC4J,cAAc,IAAqBC,QAAQ,EAAEvI,IAAI,CAAC;IACvD,IAAIyI,QAAQ,IAAIC,oBAAoB,KAAK,IAAI,CAACxK,gBAAgB,CAACyK,IAAI,EAAE;MACnE,IAAI,CAACpI,KAAK,CAAC,CAAC;IACd;IACA,IAAI7B,MAAM,EAAE,IAAI,CAACgB,MAAM,CAAC,CAAC;EAC3B;EAEAkJ,yBAAyBA,CAAA,EAAG;IAC1B,IAAI,CAACnK,oBAAoB,GAAG,KAAK;EACnC;EAEAoK,aAAaA,CAAC5B,KAAe,EAAEC,IAA0B,GAAG,CAAC,CAAC,EAAE;IAAA,IAAA4B,YAAA;IAC9D5B,IAAI,CAACQ,SAAS,GAAG,IAAI;IACrB,CAAAoB,YAAA,GAAA5B,IAAI,CAACxI,MAAM,YAAAoK,YAAA,GAAX5B,IAAI,CAACxI,MAAM,GAAK,KAAK;IACrB,IAAI,CAACsI,SAAS,CAACC,KAAK,EAAEC,IAAI,CAAC;EAC7B;EAEA6B,SAASA,CAACC,KAAe,EAAE9B,IAAsB,GAAG,CAAC,CAAC,EAAE;IACtD,IAAIA,IAAI,CAACO,SAAS,IAAI,IAAI,EAAE;MAC1BP,IAAI,CAACO,SAAS,GAAGwB,cAAc;IACjC;IAEA,IAAI,CAACjC,SAAS,CAACgC,KAAK,EAAE9B,IAAI,CAAC;EAC7B;EAEAS,aAAaA,CAACuB,OAAgB,EAAEhC,IAAwB,EAAE;IACxD,MAAM3J,MAAM,GAAG,IAAI,CAACA,MAAM;IAG1B,IAAIA,MAAM,CAACwE,WAAW,IAAIxE,MAAM,CAACiC,OAAO,EAAE;IAI1C,IAAIjC,MAAM,CAACkC,OAAO,EAAE;MAClB,IAAI,CAACc,KAAK,CAAC,CAAC;MACZ;IACF;IAEA,IAAI,CAAC2I,OAAO,EAAE;MACZ;IACF;IAEA,MAAM9B,SAAS,GAAGF,IAAI,CAACM,iBAAiB;IACxC,MAAM2B,eAAe,GAAG,IAAI,CAAC5K,gBAAgB;IAC7C,IAAI6I,SAAS,GAAG,CAAC,IAAI+B,eAAe,GAAG,CAAC,EAAE;MACxC,MAAMC,MAAM,GAAGhC,SAAS,GAAG+B,eAAe;MAC1C,IAAIC,MAAM,IAAI,CAAC,EAAE;QACf,IAAI,CAACvH,OAAO,CAACuH,MAAM,IAAI,CAAC,CAAC;QACzB;MACF;IACF;IAGA,IAAI,IAAI,CAACtK,IAAI,CAAC2B,UAAU,CAAC,CAAC,EAAE;MAa1B,IAAI,CAACoB,OAAO,CAAC,CAAC,CAAC;IACjB;EACF;EAOAwH,mBAAmBA,CAAC7C,OAAkB,EAAsB;IAG1D,IAAIA,OAAO,CAAC8C,MAAM,EAAE;IAEpB,IAAI,IAAI,CAACpL,gBAAgB,CAACqL,GAAG,CAAC/C,OAAO,CAAC,EAAE;IAExC,IACE,IAAI,CAACxI,iBAAiB,IACtBb,gCAAgC,CAACoE,IAAI,CAACiF,OAAO,CAACG,KAAK,CAAC,EACpD;MACA;IACF;IAEA,IAAI,CAACzI,gBAAgB,CAACsL,GAAG,CAAChD,OAAO,CAAC;IAElC,IAAI,CAAC,IAAI,CAACjJ,MAAM,CAACkM,kBAAkB,CAACjD,OAAO,CAACG,KAAK,CAAC,EAAE;MAClD;IACF;IAEA;EACF;EAEAD,aAAaA,CAACF,OAAkB,EAAEkD,YAAkC,EAAE;IACpE,MAAMC,gBAAgB,GAAG,IAAI,CAAC3L,iBAAiB;IAC/C,MAAM4L,cAAc,GAAGpD,OAAO,CAACzB,IAAI,KAAK,cAAc;IAItD,MAAM8E,aAAa,GACjBD,cAAc,IACdF,YAAY,MAA6B,IACzC,CAAC,IAAI,CAAC1L,iBAAiB;IAEzB,IACE6L,aAAa,IACb,IAAI,CAAC/K,IAAI,CAAC2B,UAAU,CAAC,CAAC,IACtBiJ,YAAY,MAAiC,EAC7C;MACA,IAAI,CAAC7H,OAAO,CAAC,CAAC,CAAC;IACjB;IAEA,MAAMiI,YAAY,GAAG,IAAI,CAACnJ,WAAW,CAAC,CAAC;IACvC,IACEmJ,YAAY,OAAgC,IAC5CA,YAAY,QAA6B,IACzCA,YAAY,OAA8B,EAC1C;MACA,IAAI,CAACvJ,KAAK,CAAC,CAAC;IACd;IAEA,IAAItB,GAAG;IACP,IAAI2K,cAAc,EAAE;MAClB,MAAM;QAAE7L;MAAuB,CAAC,GAAG,IAAI;MACvC,IACE,CAAAA,sBAAsB,oBAAtBA,sBAAsB,CAAE+F,OAAO,MAAK,KAAK,IACzC5G,WAAW,CAACqE,IAAI,CAACiF,OAAO,CAACG,KAAK,CAAC,EAC/B;QACA,IAAI,CAACtG,SAAK,GAAI,CAAC;QACf,IAAI,CAAC3B,MAAM,CAAC,CAAC;QACbX,sBAAsB,CAAC+F,OAAO,GAAG,IAAI;MACvC;MACA7E,GAAG,GAAG,KAAKuH,OAAO,CAACG,KAAK,IAAI;MAC5B,IAAI,IAAI,CAACpJ,MAAM,CAACmB,MAAM,CAACqL,sBAAsB,EAAE;QAAA,IAAAC,YAAA;QAC7C,MAAMZ,MAAM,IAAAY,YAAA,GAAGxD,OAAO,CAACpG,GAAG,qBAAX4J,YAAA,CAAa3C,KAAK,CAAC4C,MAAM;QACxC,IAAIb,MAAM,EAAE;UACV,MAAMc,YAAY,GAAG,IAAIC,MAAM,CAAC,WAAW,GAAGf,MAAM,GAAG,GAAG,EAAE,GAAG,CAAC;UAChEnK,GAAG,GAAGA,GAAG,CAACmL,OAAO,CAACF,YAAY,EAAE,IAAI,CAAC;QACvC;QACA,IAAI,IAAI,CAAC3M,MAAM,CAACkC,OAAO,EAAE;UACvBR,GAAG,GAAGA,GAAG,CAACmL,OAAO,CAAC,UAAU,EAAE,IAAI,CAAC;QACrC,CAAC,MAAM;UACL,IAAIC,UAAU,GAAG,IAAI,CAAC9M,MAAM,CAACwE,WAAW,GACpC,CAAC,GACD,IAAI,CAACjD,IAAI,CAACwL,gBAAgB,CAAC,CAAC;UAEhC,IAAI,IAAI,CAAC1G,aAAa,GAAgB,CAAC,IAAI,IAAI,CAACrG,MAAM,CAACwE,WAAW,EAAE;YAClEsI,UAAU,IAAI,IAAI,CAAC1G,UAAU,CAAC,CAAC;UACjC;UAEA1E,GAAG,GAAGA,GAAG,CAACmL,OAAO,CAAC,UAAU,EAAE,KAAK,GAAG,CAACG,MAAM,CAACF,UAAU,CAAC,EAAE,CAAC;QAC9D;MACF;IACF,CAAC,MAAM,IAAI,CAACV,gBAAgB,EAAE;MAC5B1K,GAAG,GAAG,KAAKuH,OAAO,CAACG,KAAK,EAAE;IAC5B,CAAC,MAAM;MAIL1H,GAAG,GAAG,KAAKuH,OAAO,CAACG,KAAK,IAAI;IAC9B;IAGA,IAAI,IAAI,CAACrI,YAAY,EAAE,IAAI,CAACkC,MAAM,CAAC,CAAC;IAEpC,IAAI,CAACiC,MAAM,CAAC,OAAO,EAAE+D,OAAO,CAACpG,GAAG,CAAC;IACjC,IAAI,CAACa,OAAO,CAAChC,GAAG,EAAE2K,cAAc,CAAC;IAEjC,IAAI,CAACA,cAAc,IAAI,CAACD,gBAAgB,EAAE;MACxC,IAAI,CAAC9H,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC;IACvB;IAEA,IAAIgI,aAAa,IAAIH,YAAY,MAAkC,EAAE;MACnE,IAAI,CAAC7H,OAAO,CAAC,CAAC,CAAC;IACjB;EACF;EAEAyG,cAAcA,CACZvD,IAAkB,EAClBwD,QAA8B,EAC9BvI,IAAY,EACZwF,MAAe,EACf4C,UAAkB,GAAG,CAAC,EACtB;IACA,MAAMoC,OAAO,GAAGxK,IAAI,CAACI,GAAG;IACxB,MAAM2D,GAAG,GAAGwE,QAAQ,CAAC3J,MAAM;IAC3B,IAAI6L,MAAM,GAAG,CAAC,CAACD,OAAO;IACtB,MAAME,aAAa,GAAGD,MAAM,GAAGD,OAAO,CAACnD,KAAK,CAAClD,IAAI,GAAG,CAAC;IACrD,MAAMwG,WAAW,GAAGF,MAAM,GAAGD,OAAO,CAACI,GAAG,CAACzG,IAAI,GAAG,CAAC;IACjD,IAAI0G,QAAQ,GAAG,CAAC;IAChB,IAAIC,qBAAqB,GAAG,CAAC;IAE7B,MAAMtJ,YAAY,GAAG,IAAI,CAACxD,iBAAiB,GACvC,YAAY,CAAC,CAAC,GACd,IAAI,CAAC6D,OAAO,CAACsE,IAAI,CAAC,IAAI,CAAC;IAE3B,KAAK,IAAIrE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGiC,GAAG,EAAEjC,CAAC,EAAE,EAAE;MAC5B,MAAM0E,OAAO,GAAG+B,QAAQ,CAACzG,CAAC,CAAC;MAE3B,MAAMiJ,WAAW,GAAG,IAAI,CAAC1B,mBAAmB,CAAC7C,OAAO,CAAC;MACrD,IAAIuE,WAAW,MAA6B,EAAE;QAC5CN,MAAM,GAAG,KAAK;QACd;MACF;MACA,IAAIA,MAAM,IAAIjE,OAAO,CAACpG,GAAG,IAAI2K,WAAW,MAA6B,EAAE;QACrE,MAAMC,gBAAgB,GAAGxE,OAAO,CAACpG,GAAG,CAACiH,KAAK,CAAClD,IAAI;QAC/C,MAAM8G,cAAc,GAAGzE,OAAO,CAACpG,GAAG,CAACwK,GAAG,CAACzG,IAAI;QAC3C,IAAIY,IAAI,MAAyB,EAAE;UACjC,IAAIqE,MAAM,GAAG,CAAC;UACd,IAAItH,CAAC,KAAK,CAAC,EAAE;YAGX,IACE,IAAI,CAAChD,IAAI,CAAC2B,UAAU,CAAC,CAAC,KACrB+F,OAAO,CAACzB,IAAI,KAAK,aAAa,IAC7BiG,gBAAgB,KAAKC,cAAc,CAAC,EACtC;cACA7B,MAAM,GAAG0B,qBAAqB,GAAG,CAAC;YACpC;UACF,CAAC,MAAM;YACL1B,MAAM,GAAG4B,gBAAgB,GAAGH,QAAQ;UACtC;UACAA,QAAQ,GAAGI,cAAc;UAEzBzJ,YAAY,CAAC4H,MAAM,CAAC;UACpB,IAAI,CAAC1C,aAAa,CAACF,OAAO,GAA0B,CAAC;UAErD,IAAI1E,CAAC,GAAG,CAAC,KAAKiC,GAAG,EAAE;YACjBvC,YAAY,CACV0J,IAAI,CAACC,GAAG,CAACT,aAAa,GAAGG,QAAQ,EAAEC,qBAAqB,CAC1D,CAAC;YACDD,QAAQ,GAAGH,aAAa;UAC1B;QACF,CAAC,MAAM,IAAI3F,IAAI,MAAuB,EAAE;UACtC,MAAMqE,MAAM,GACV4B,gBAAgB,IAAIlJ,CAAC,KAAK,CAAC,GAAG4I,aAAa,GAAGG,QAAQ,CAAC;UACzDA,QAAQ,GAAGI,cAAc;UAEzBzJ,YAAY,CAAC4H,MAAM,CAAC;UACpB,IAAI,CAAC1C,aAAa,CAACF,OAAO,GAA0B,CAAC;UAErD,IAAI1E,CAAC,GAAG,CAAC,KAAKiC,GAAG,EAAE;YACjBvC,YAAY,CAAC0J,IAAI,CAACE,GAAG,CAAC,CAAC,EAAET,WAAW,GAAGE,QAAQ,CAAC,CAAC;YACjDA,QAAQ,GAAGF,WAAW;UACxB;QACF,CAAC,MAAM;UACL,MAAMvB,MAAM,GACV4B,gBAAgB,IAAIlJ,CAAC,KAAK,CAAC,GAAG6I,WAAW,GAAGvC,UAAU,GAAGyC,QAAQ,CAAC;UACpEA,QAAQ,GAAGI,cAAc;UAEzBzJ,YAAY,CAAC4H,MAAM,CAAC;UACpB,IAAI,CAAC1C,aAAa,CAACF,OAAO,GAA0B,CAAC;QACvD;MACF,CAAC,MAAM;QACLiE,MAAM,GAAG,KAAK;QACd,IAAIM,WAAW,MAA6B,EAAE;UAC5C;QACF;QAEA,IAAIhH,GAAG,KAAK,CAAC,EAAE;UACb,MAAMsH,UAAU,GAAG7E,OAAO,CAACpG,GAAG,GAC1BoG,OAAO,CAACpG,GAAG,CAACiH,KAAK,CAAClD,IAAI,KAAKqC,OAAO,CAACpG,GAAG,CAACwK,GAAG,CAACzG,IAAI,GAC/C,CAACjH,WAAW,CAACqE,IAAI,CAACiF,OAAO,CAACG,KAAK,CAAC;UAEpC,MAAM2E,iBAAiB,GACrBD,UAAU,IACV,CAACzO,WAAW,CAACoD,IAAI,CAAC,IAClB,CAACnD,WAAW,CAAC2I,MAAM,CAAC,IACpB,CAAC1I,iBAAiB,CAAC0I,MAAM,CAAC,IAC1B,CAACzI,mBAAmB,CAACyI,MAAM,CAAC;UAE9B,IAAIT,IAAI,MAAyB,EAAE;YACjC,IAAI,CAAC2B,aAAa,CAChBF,OAAO,EACN8E,iBAAiB,IAAItL,IAAI,CAAC+E,IAAI,KAAK,kBAAkB,IACnDsG,UAAU,IAAI1O,UAAU,CAAC6I,MAAM,EAAE;cAAE2C,IAAI,EAAEnI;YAAK,CAAC,CAAE,QAGtD,CAAC;UACH,CAAC,MAAM,IAAIsL,iBAAiB,IAAIvG,IAAI,MAA0B,EAAE;YAC9D,IAAI,CAAC2B,aAAa,CAACF,OAAO,GAA0B,CAAC;UACvD,CAAC,MAAM;YACL,IAAI,CAACE,aAAa,CAACF,OAAO,GAA8B,CAAC;UAC3D;QACF,CAAC,MAAM,IACLzB,IAAI,MAAuB,IAC3B,EAAE/E,IAAI,CAAC+E,IAAI,KAAK,kBAAkB,IAAI/E,IAAI,CAACuL,UAAU,CAAC3M,MAAM,GAAG,CAAC,CAAC,IACjEoB,IAAI,CAAC+E,IAAI,KAAK,WAAW,IACzB/E,IAAI,CAAC+E,IAAI,KAAK,iBAAiB,EAC/B;UAMA,IAAI,CAAC2B,aAAa,CAChBF,OAAO,EACP1E,CAAC,KAAK,CAAC,OAEHA,CAAC,KAAKiC,GAAG,GAAG,CAAC,QAGnB,CAAC;QACH,CAAC,MAAM;UACL,IAAI,CAAC2C,aAAa,CAACF,OAAO,GAA8B,CAAC;QAC3D;MACF;IACF;IAEA,IAAIzB,IAAI,MAA0B,IAAI0F,MAAM,IAAII,QAAQ,EAAE;MACxD,IAAI,CAACtM,gBAAgB,GAAGsM,QAAQ;IAClC;EACF;AACF;AAGAW,MAAM,CAACC,MAAM,CAACpO,OAAO,CAACqO,SAAS,EAAEhP,kBAAkB,CAAC;AAEjB;EAEjCW,OAAO,CAACqO,SAAS,CAACC,IAAI,GAAG,SAASA,IAAIA,CAAA,EAAgB,CAAC,CAAC;AAC1D;AAAC,IAAAC,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAKczO,OAAO;AAEtB,SAAS4L,cAAcA,CAAA,EAAgB;EACrC,IAAI,CAAC5I,SAAK,GAAI,CAAC;EACf,IAAI,CAACE,KAAK,CAAC,CAAC;AACd", "ignoreList": []}