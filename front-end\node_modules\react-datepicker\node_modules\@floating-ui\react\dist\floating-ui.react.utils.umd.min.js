!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports,require("react")):"function"==typeof define&&define.amd?define(["exports","react"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).FloatingUIReactUtils={},e.React)}(this,(function(e,t){"use strict";function n(e){var t=Object.create(null);return e&&Object.keys(e).forEach((function(n){if("default"!==n){var r=Object.getOwnPropertyDescriptor(e,n);Object.defineProperty(t,n,r.get?r:{enumerable:!0,get:function(){return e[n]}})}})),t.default=e,Object.freeze(t)}var r=n(t);function o(){return"undefined"!=typeof window}function i(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function a(){const e=navigator.userAgentData;return null!=e&&e.platform?e.platform:navigator.platform}function u(){const e=navigator.userAgentData;return e&&Array.isArray(e.brands)?e.brands.map((e=>{let{brand:t,version:n}=e;return t+"/"+n})).join(" "):navigator.userAgent}function d(){const e=/android/i;return e.test(a())||e.test(u())}function c(){return u().includes("jsdom/")}const l="data-floating-ui-focusable",s="ArrowLeft",f="ArrowRight";function p(e){let t=e.activeElement;for(;null!=(null==(n=t)||null==(n=n.shadowRoot)?void 0:n.activeElement);){var n;t=t.shadowRoot.activeElement}return t}function h(e,t){if(!e||!t)return!1;const n=null==t.getRootNode?void 0:t.getRootNode();if(e.contains(t))return!0;if(n&&(r=n,o()&&"undefined"!=typeof ShadowRoot&&(r instanceof ShadowRoot||r instanceof i(r).ShadowRoot))){let n=t;for(;n;){if(e===n)return!0;n=n.parentNode||n.host}}var r;return!1}function g(e){return(null==e?void 0:e.ownerDocument)||document}function v(e){return t=e,!!o()&&(t instanceof HTMLElement||t instanceof i(t).HTMLElement)&&e.matches("input:not([type='hidden']):not([disabled]),[contenteditable]:not([contenteditable='false']),textarea:not([disabled])");var t}function b(e,t,n){void 0===n&&(n=!0);let r=e.filter((e=>{var n;return e.parentId===t&&(null==(n=e.context)?void 0:n.open)})),o=r;for(;o.length;)o=n?e.filter((e=>{var t;return null==(t=o)?void 0:t.some((t=>{var n;return e.parentId===t.id&&(null==(n=e.context)?void 0:n.open)}))})):e,r=r.concat(o);return r}function m(e){e.preventDefault(),e.stopPropagation()}var y="undefined"!=typeof document?t.useLayoutEffect:t.useEffect;const I={...r}.useInsertionEffect||(e=>e());const w=Math.floor;function x(e,t,n){return Math.floor(e/t)!==n}function E(e,t){return t<0||t>=e.current.length}function S(e,t){let{startingIndex:n=-1,decrement:r=!1,disabledIndices:o,amount:i=1}=void 0===t?{}:t,a=n;do{a+=r?-i:i}while(a>=0&&a<=e.current.length-1&&A(e,a,o));return a}function A(e,t,n){if(n)return n.includes(t);const r=e.current[t];return null==r||r.hasAttribute("disabled")||"true"===r.getAttribute("aria-disabled")}
/*!
  * tabbable 6.2.0
  * @license MIT, https://github.com/focus-trap/tabbable/blob/master/LICENSE
  */var R=["input:not([inert])","select:not([inert])","textarea:not([inert])","a[href]:not([inert])","button:not([inert])","[tabindex]:not(slot):not([inert])","audio[controls]:not([inert])","video[controls]:not([inert])",'[contenteditable]:not([contenteditable="false"]):not([inert])',"details>summary:first-of-type:not([inert])","details:not([inert])"].join(","),T="undefined"==typeof Element,N=T?function(){}:Element.prototype.matches||Element.prototype.msMatchesSelector||Element.prototype.webkitMatchesSelector,D=!T&&Element.prototype.getRootNode?function(e){var t;return null==e||null===(t=e.getRootNode)||void 0===t?void 0:t.call(e)}:function(e){return null==e?void 0:e.ownerDocument},O=function e(t,n){var r;void 0===n&&(n=!0);var o=null==t||null===(r=t.getAttribute)||void 0===r?void 0:r.call(t,"inert");return""===o||"true"===o||n&&t&&e(t.parentNode)},C=function e(t,n,r){for(var o=[],i=Array.from(t);i.length;){var a=i.shift();if(!O(a,!1))if("SLOT"===a.tagName){var u=a.assignedElements(),d=e(u.length?u:a.children,!0,r);r.flatten?o.push.apply(o,d):o.push({scopeParent:a,candidates:d})}else{N.call(a,R)&&r.filter(a)&&(n||!t.includes(a))&&o.push(a);var c=a.shadowRoot||"function"==typeof r.getShadowRoot&&r.getShadowRoot(a),l=!O(c,!1)&&(!r.shadowRootFilter||r.shadowRootFilter(a));if(c&&l){var s=e(!0===c?a.children:c.children,!0,r);r.flatten?o.push.apply(o,s):o.push({scopeParent:a,candidates:s})}else i.unshift.apply(i,a.children)}}return o},L=function(e){return!isNaN(parseInt(e.getAttribute("tabindex"),10))},P=function(e){if(!e)throw new Error("No node provided");return e.tabIndex<0&&(/^(AUDIO|VIDEO|DETAILS)$/.test(e.tagName)||function(e){var t,n=null==e||null===(t=e.getAttribute)||void 0===t?void 0:t.call(e,"contenteditable");return""===n||"true"===n}(e))&&!L(e)?0:e.tabIndex},k=function(e,t){return e.tabIndex===t.tabIndex?e.documentOrder-t.documentOrder:e.tabIndex-t.tabIndex},M=function(e){return"INPUT"===e.tagName},F=function(e){return function(e){return M(e)&&"radio"===e.type}(e)&&!function(e){if(!e.name)return!0;var t,n=e.form||D(e),r=function(e){return n.querySelectorAll('input[type="radio"][name="'+e+'"]')};if("undefined"!=typeof window&&void 0!==window.CSS&&"function"==typeof window.CSS.escape)t=r(window.CSS.escape(e.name));else try{t=r(e.name)}catch(e){return console.error("Looks like you have a radio button with a name attribute containing invalid CSS selector characters and need the CSS.escape polyfill: %s",e.message),!1}var o=function(e,t){for(var n=0;n<e.length;n++)if(e[n].checked&&e[n].form===t)return e[n]}(t,e.form);return!o||o===e}(e)},j=function(e){var t=e.getBoundingClientRect(),n=t.width,r=t.height;return 0===n&&0===r},U=function(e,t){var n=t.displayCheck,r=t.getShadowRoot;if("hidden"===getComputedStyle(e).visibility)return!0;var o=N.call(e,"details>summary:first-of-type")?e.parentElement:e;if(N.call(o,"details:not([open]) *"))return!0;if(n&&"full"!==n&&"legacy-full"!==n){if("non-zero-area"===n)return j(e)}else{if("function"==typeof r){for(var i=e;e;){var a=e.parentElement,u=D(e);if(a&&!a.shadowRoot&&!0===r(a))return j(e);e=e.assignedSlot?e.assignedSlot:a||u===e.ownerDocument?a:u.host}e=i}if(function(e){var t,n,r,o,i=e&&D(e),a=null===(t=i)||void 0===t?void 0:t.host,u=!1;if(i&&i!==e)for(u=!!(null!==(n=a)&&void 0!==n&&null!==(r=n.ownerDocument)&&void 0!==r&&r.contains(a)||null!=e&&null!==(o=e.ownerDocument)&&void 0!==o&&o.contains(e));!u&&a;){var d,c,l;u=!(null===(c=a=null===(d=i=D(a))||void 0===d?void 0:d.host)||void 0===c||null===(l=c.ownerDocument)||void 0===l||!l.contains(a))}return u}(e))return!e.getClientRects().length;if("legacy-full"!==n)return!0}return!1},G=function(e,t){return!(t.disabled||O(t)||function(e){return M(e)&&"hidden"===e.type}(t)||U(t,e)||function(e){return"DETAILS"===e.tagName&&Array.prototype.slice.apply(e.children).some((function(e){return"SUMMARY"===e.tagName}))}(t)||function(e){if(/^(INPUT|BUTTON|SELECT|TEXTAREA)$/.test(e.tagName))for(var t=e.parentElement;t;){if("FIELDSET"===t.tagName&&t.disabled){for(var n=0;n<t.children.length;n++){var r=t.children.item(n);if("LEGEND"===r.tagName)return!!N.call(t,"fieldset[disabled] *")||!r.contains(e)}return!0}t=t.parentElement}return!1}(t))},q=function(e,t){return!(F(t)||P(t)<0||!G(e,t))},z=function(e){var t=parseInt(e.getAttribute("tabindex"),10);return!!(isNaN(t)||t>=0)},V=function e(t){var n=[],r=[];return t.forEach((function(t,o){var i=!!t.scopeParent,a=i?t.scopeParent:t,u=function(e,t){var n=P(e);return n<0&&t&&!L(e)?0:n}(a,i),d=i?e(t.candidates):a;0===u?i?n.push.apply(n,d):n.push(a):r.push({documentOrder:o,tabIndex:u,item:t,isScope:i,content:d})})),r.sort(k).reduce((function(e,t){return t.isScope?e.push.apply(e,t.content):e.push(t.content),e}),[]).concat(n)},B=function(e,t){var n;return n=(t=t||{}).getShadowRoot?C([e],t.includeContainer,{filter:q.bind(null,t),flatten:!1,getShadowRoot:t.getShadowRoot,shadowRootFilter:z}):function(e,t,n){if(O(e))return[];var r=Array.prototype.slice.apply(e.querySelectorAll(R));return t&&N.call(e,R)&&r.unshift(e),r.filter(n)}(e,t.includeContainer,q.bind(null,t)),V(n)};const H=()=>({getShadowRoot:!0,displayCheck:"function"==typeof ResizeObserver&&ResizeObserver.toString().includes("[native code]")?"full":"none"});function W(e,t){const n=B(e,H()),r=n.length;if(0===r)return;const o=p(g(e)),i=n.indexOf(o);return n[-1===i?1===t?0:r-1:i+t]}e.activeElement=p,e.contains=h,e.createGridCellMap=function(e,t,n){const r=[];let o=0;return e.forEach(((e,i)=>{let{width:a,height:u}=e,d=!1;for(n&&(o=0);!d;){const e=[];for(let n=0;n<a;n++)for(let r=0;r<u;r++)e.push(o+n+r*t);o%t+a<=t&&e.every((e=>null==r[e]))?(e.forEach((e=>{r[e]=i})),d=!0):o++}})),[...r]},e.disableFocusInside=function(e){B(e,H()).forEach((e=>{e.dataset.tabindex=e.getAttribute("tabindex")||"",e.setAttribute("tabindex","-1")}))},e.enableFocusInside=function(e){e.querySelectorAll("[data-tabindex]").forEach((e=>{const t=e.dataset.tabindex;delete e.dataset.tabindex,t?e.setAttribute("tabindex",t):e.removeAttribute("tabindex")}))},e.findNonDisabledListIndex=S,e.getDeepestNode=function(e,t){let n,r=-1;return function t(o,i){i>r&&(n=o,r=i),b(e,o).forEach((e=>{t(e.id,i+1)}))}(t,0),e.find((e=>e.id===n))},e.getDocument=g,e.getFloatingFocusElement=function(e){return e?e.hasAttribute(l)?e:e.querySelector("["+l+"]")||e:null},e.getGridCellIndexOfCorner=function(e,t,n,r,o){if(-1===e)return-1;const i=n.indexOf(e),a=t[e];switch(o){case"tl":return i;case"tr":return a?i+a.width-1:i;case"bl":return a?i+(a.height-1)*r:i;case"br":return n.lastIndexOf(e)}},e.getGridCellIndices=function(e,t){return t.flatMap(((t,n)=>e.includes(t)?[n]:[]))},e.getGridNavigatedIndex=function(e,t){let{event:n,orientation:r,loop:o,rtl:i,cols:a,disabledIndices:u,minIndex:d,maxIndex:c,prevIndex:l,stopEvent:p=!1}=t,h=l;if("ArrowUp"===n.key){if(p&&m(n),-1===l)h=c;else if(h=S(e,{startingIndex:h,amount:a,decrement:!0,disabledIndices:u}),o&&(l-a<d||h<0)){const e=l%a,t=c%a,n=c-(t-e);h=t===e?c:t>e?n:n-a}E(e,h)&&(h=l)}if("ArrowDown"===n.key&&(p&&m(n),-1===l?h=d:(h=S(e,{startingIndex:l,amount:a,disabledIndices:u}),o&&l+a>c&&(h=S(e,{startingIndex:l%a-a,amount:a,disabledIndices:u}))),E(e,h)&&(h=l)),"both"===r){const t=w(l/a);n.key===(i?s:f)&&(p&&m(n),l%a!=a-1?(h=S(e,{startingIndex:l,disabledIndices:u}),o&&x(h,a,t)&&(h=S(e,{startingIndex:l-l%a-1,disabledIndices:u}))):o&&(h=S(e,{startingIndex:l-l%a-1,disabledIndices:u})),x(h,a,t)&&(h=l)),n.key===(i?f:s)&&(p&&m(n),l%a!=0?(h=S(e,{startingIndex:l,decrement:!0,disabledIndices:u}),o&&x(h,a,t)&&(h=S(e,{startingIndex:l+(a-l%a),decrement:!0,disabledIndices:u}))):o&&(h=S(e,{startingIndex:l+(a-l%a),decrement:!0,disabledIndices:u})),x(h,a,t)&&(h=l));const r=w(c/a)===t;E(e,h)&&(h=o&&r?n.key===(i?f:s)?c:S(e,{startingIndex:l-l%a-1,disabledIndices:u}):l)}return h},e.getMaxListIndex=function(e,t){return S(e,{decrement:!0,startingIndex:e.current.length,disabledIndices:t})},e.getMinListIndex=function(e,t){return S(e,{disabledIndices:t})},e.getNextTabbable=function(e){return W(g(e).body,1)||e},e.getNodeAncestors=function(e,t){var n;let r=[],o=null==(n=e.find((e=>e.id===t)))?void 0:n.parentId;for(;o;){const t=e.find((e=>e.id===o));o=null==t?void 0:t.parentId,t&&(r=r.concat(t))}return r},e.getNodeChildren=b,e.getPlatform=a,e.getPreviousTabbable=function(e){return W(g(e).body,-1)||e},e.getTabbableOptions=H,e.getTarget=function(e){return"composedPath"in e?e.composedPath()[0]:e.target},e.getUserAgent=u,e.isAndroid=d,e.isDifferentGridRow=x,e.isEventTargetWithin=function(e,t){if(null==t)return!1;if("composedPath"in e)return e.composedPath().includes(t);const n=e;return null!=n.target&&t.contains(n.target)},e.isIndexOutOfListBounds=E,e.isJSDOM=c,e.isListIndexDisabled=A,e.isMac=function(){return a().toLowerCase().startsWith("mac")&&!navigator.maxTouchPoints},e.isMouseLikePointerType=function(e,t){const n=["mouse","pen"];return t||n.push("",void 0),n.includes(e)},e.isOutsideEvent=function(e,t){const n=t||e.currentTarget,r=e.relatedTarget;return!r||!h(n,r)},e.isReactEvent=function(e){return"nativeEvent"in e},e.isRootElement=function(e){return e.matches("html,body")},e.isSafari=function(){return/apple/i.test(navigator.vendor)},e.isTypeableCombobox=function(e){return!!e&&("combobox"===e.getAttribute("role")&&v(e))},e.isTypeableElement=v,e.isVirtualClick=function(e){return!(0!==e.mozInputSource||!e.isTrusted)||(d()&&e.pointerType?"click"===e.type&&1===e.buttons:0===e.detail&&!e.pointerType)},e.isVirtualPointerEvent=function(e){return!c()&&(!d()&&0===e.width&&0===e.height||d()&&1===e.width&&1===e.height&&0===e.pressure&&0===e.detail&&"mouse"===e.pointerType||e.width<1&&e.height<1&&0===e.pressure&&0===e.detail&&"touch"===e.pointerType)},e.matchesFocusVisible=function(e){if(!e||c())return!0;try{return e.matches(":focus-visible")}catch(e){return!0}},e.stopEvent=m,e.useEffectEvent=function(e){const t=r.useRef((()=>{}));return I((()=>{t.current=e})),r.useCallback((function(){for(var e=arguments.length,n=new Array(e),r=0;r<e;r++)n[r]=arguments[r];return null==t.current?void 0:t.current(...n)}),[])},e.useLatestRef=function(e){const t=r.useRef(e);return y((()=>{t.current=e})),t},e.useModernLayoutEffect=y}));
