var A=function(J){return A=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(G){return typeof G}:function(G){return G&&typeof Symbol=="function"&&G.constructor===Symbol&&G!==Symbol.prototype?"symbol":typeof G},A(J)},W=function(J,G){var X=Object.keys(J);if(Object.getOwnPropertySymbols){var I=Object.getOwnPropertySymbols(J);G&&(I=I.filter(function(x){return Object.getOwnPropertyDescriptor(J,x).enumerable})),X.push.apply(X,I)}return X},N=function(J){for(var G=1;G<arguments.length;G++){var X=arguments[G]!=null?arguments[G]:{};G%2?W(Object(X),!0).forEach(function(I){CC(J,I,X[I])}):Object.getOwnPropertyDescriptors?Object.defineProperties(J,Object.getOwnPropertyDescriptors(X)):W(Object(X)).forEach(function(I){Object.defineProperty(J,I,Object.getOwnPropertyDescriptor(X,I))})}return J},CC=function(J,G,X){if(G=HC(G),G in J)Object.defineProperty(J,G,{value:X,enumerable:!0,configurable:!0,writable:!0});else J[G]=X;return J},HC=function(J){var G=UC(J,"string");return A(G)=="symbol"?G:String(G)},UC=function(J,G){if(A(J)!="object"||!J)return J;var X=J[Symbol.toPrimitive];if(X!==void 0){var I=X.call(J,G||"default");if(A(I)!="object")return I;throw new TypeError("@@toPrimitive must return a primitive value.")}return(G==="string"?String:Number)(J)};(function(J){var G=Object.defineProperty,X=function C(B,H){for(var U in H)G(B,U,{get:H[U],enumerable:!0,configurable:!0,set:function Y(Z){return H[U]=function(){return Z}}})},I={lessThanXSeconds:{one:"minder as 'n sekonde",other:"minder as {{count}} sekondes"},xSeconds:{one:"1 sekonde",other:"{{count}} sekondes"},halfAMinute:"'n halwe minuut",lessThanXMinutes:{one:"minder as 'n minuut",other:"minder as {{count}} minute"},xMinutes:{one:"'n minuut",other:"{{count}} minute"},aboutXHours:{one:"ongeveer 1 uur",other:"ongeveer {{count}} ure"},xHours:{one:"1 uur",other:"{{count}} ure"},xDays:{one:"1 dag",other:"{{count}} dae"},aboutXWeeks:{one:"ongeveer 1 week",other:"ongeveer {{count}} weke"},xWeeks:{one:"1 week",other:"{{count}} weke"},aboutXMonths:{one:"ongeveer 1 maand",other:"ongeveer {{count}} maande"},xMonths:{one:"1 maand",other:"{{count}} maande"},aboutXYears:{one:"ongeveer 1 jaar",other:"ongeveer {{count}} jaar"},xYears:{one:"1 jaar",other:"{{count}} jaar"},overXYears:{one:"meer as 1 jaar",other:"meer as {{count}} jaar"},almostXYears:{one:"byna 1 jaar",other:"byna {{count}} jaar"}},x=function C(B,H,U){var Y,Z=I[B];if(typeof Z==="string")Y=Z;else if(H===1)Y=Z.one;else Y=Z.other.replace("{{count}}",String(H));if(U!==null&&U!==void 0&&U.addSuffix)if(U.comparison&&U.comparison>0)return"oor "+Y;else return Y+" gelede";return Y};function S(C){return function(){var B=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},H=B.width?String(B.width):C.defaultWidth,U=C.formats[H]||C.formats[C.defaultWidth];return U}}var $={full:"EEEE, d MMMM yyyy",long:"d MMMM yyyy",medium:"d MMM yyyy",short:"yyyy/MM/dd"},D={full:"HH:mm:ss zzzz",long:"HH:mm:ss z",medium:"HH:mm:ss",short:"HH:mm"},M={full:"{{date}} 'om' {{time}}",long:"{{date}} 'om' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},R={date:S({formats:$,defaultWidth:"full"}),time:S({formats:D,defaultWidth:"full"}),dateTime:S({formats:M,defaultWidth:"full"})},L={lastWeek:"'verlede' eeee 'om' p",yesterday:"'gister om' p",today:"'vandag om' p",tomorrow:"'m\xF4re om' p",nextWeek:"eeee 'om' p",other:"P"},V=function C(B,H,U,Y){return L[B]};function Q(C){return function(B,H){var U=H!==null&&H!==void 0&&H.context?String(H.context):"standalone",Y;if(U==="formatting"&&C.formattingValues){var Z=C.defaultFormattingWidth||C.defaultWidth,T=H!==null&&H!==void 0&&H.width?String(H.width):Z;Y=C.formattingValues[T]||C.formattingValues[Z]}else{var E=C.defaultWidth,K=H!==null&&H!==void 0&&H.width?String(H.width):C.defaultWidth;Y=C.values[K]||C.values[E]}var O=C.argumentCallback?C.argumentCallback(B):B;return Y[O]}}var j={narrow:["vC","nC"],abbreviated:["vC","nC"],wide:["voor Christus","na Christus"]},f={narrow:["1","2","3","4"],abbreviated:["K1","K2","K3","K4"],wide:["1ste kwartaal","2de kwartaal","3de kwartaal","4de kwartaal"]},v={narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],abbreviated:["Jan","Feb","Mrt","Apr","Mei","Jun","Jul","Aug","Sep","Okt","Nov","Des"],wide:["Januarie","Februarie","Maart","April","Mei","Junie","Julie","Augustus","September","Oktober","November","Desember"]},w={narrow:["S","M","D","W","D","V","S"],short:["So","Ma","Di","Wo","Do","Vr","Sa"],abbreviated:["Son","Maa","Din","Woe","Don","Vry","Sat"],wide:["Sondag","Maandag","Dinsdag","Woensdag","Donderdag","Vrydag","Saterdag"]},_={narrow:{am:"vm",pm:"nm",midnight:"middernag",noon:"middaguur",morning:"oggend",afternoon:"middag",evening:"laat middag",night:"aand"},abbreviated:{am:"vm",pm:"nm",midnight:"middernag",noon:"middaguur",morning:"oggend",afternoon:"middag",evening:"laat middag",night:"aand"},wide:{am:"vm",pm:"nm",midnight:"middernag",noon:"middaguur",morning:"oggend",afternoon:"middag",evening:"laat middag",night:"aand"}},P={narrow:{am:"vm",pm:"nm",midnight:"middernag",noon:"uur die middag",morning:"uur die oggend",afternoon:"uur die middag",evening:"uur die aand",night:"uur die aand"},abbreviated:{am:"vm",pm:"nm",midnight:"middernag",noon:"uur die middag",morning:"uur die oggend",afternoon:"uur die middag",evening:"uur die aand",night:"uur die aand"},wide:{am:"vm",pm:"nm",midnight:"middernag",noon:"uur die middag",morning:"uur die oggend",afternoon:"uur die middag",evening:"uur die aand",night:"uur die aand"}},F=function C(B){var H=Number(B),U=H%100;if(U<20)switch(U){case 1:case 8:return H+"ste";default:return H+"de"}return H+"ste"},h={ordinalNumber:F,era:Q({values:j,defaultWidth:"wide"}),quarter:Q({values:f,defaultWidth:"wide",argumentCallback:function C(B){return B-1}}),month:Q({values:v,defaultWidth:"wide"}),day:Q({values:w,defaultWidth:"wide"}),dayPeriod:Q({values:_,defaultWidth:"wide",formattingValues:P,defaultFormattingWidth:"wide"})};function q(C){return function(B){var H=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},U=H.width,Y=U&&C.matchPatterns[U]||C.matchPatterns[C.defaultMatchWidth],Z=B.match(Y);if(!Z)return null;var T=Z[0],E=U&&C.parsePatterns[U]||C.parsePatterns[C.defaultParseWidth],K=Array.isArray(E)?b(E,function(z){return z.test(T)}):k(E,function(z){return z.test(T)}),O;O=C.valueCallback?C.valueCallback(K):K,O=H.valueCallback?H.valueCallback(O):O;var t=B.slice(T.length);return{value:O,rest:t}}}var k=function C(B,H){for(var U in B)if(Object.prototype.hasOwnProperty.call(B,U)&&H(B[U]))return U;return},b=function C(B,H){for(var U=0;U<B.length;U++)if(H(B[U]))return U;return};function m(C){return function(B){var H=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},U=B.match(C.matchPattern);if(!U)return null;var Y=U[0],Z=B.match(C.parsePattern);if(!Z)return null;var T=C.valueCallback?C.valueCallback(Z[0]):Z[0];T=H.valueCallback?H.valueCallback(T):T;var E=B.slice(Y.length);return{value:T,rest:E}}}var c=/^(\d+)(ste|de)?/i,y=/\d+/i,d={narrow:/^([vn]\.? ?C\.?)/,abbreviated:/^([vn]\. ?C\.?)/,wide:/^((voor|na) Christus)/},g={any:[/^v/,/^n/]},p={narrow:/^[1234]/i,abbreviated:/^K[1234]/i,wide:/^[1234](st|d)e kwartaal/i},u={any:[/1/i,/2/i,/3/i,/4/i]},l={narrow:/^[jfmasond]/i,abbreviated:/^(Jan|Feb|Mrt|Apr|Mei|Jun|Jul|Aug|Sep|Okt|Nov|Dec)\.?/i,wide:/^(Januarie|Februarie|Maart|April|Mei|Junie|Julie|Augustus|September|Oktober|November|Desember)/i},i={narrow:[/^J/i,/^F/i,/^M/i,/^A/i,/^M/i,/^J/i,/^J/i,/^A/i,/^S/i,/^O/i,/^N/i,/^D/i],any:[/^Jan/i,/^Feb/i,/^Mrt/i,/^Apr/i,/^Mei/i,/^Jun/i,/^Jul/i,/^Aug/i,/^Sep/i,/^Okt/i,/^Nov/i,/^Dec/i]},n={narrow:/^[smdwv]/i,short:/^(So|Ma|Di|Wo|Do|Vr|Sa)/i,abbreviated:/^(Son|Maa|Din|Woe|Don|Vry|Sat)/i,wide:/^(Sondag|Maandag|Dinsdag|Woensdag|Donderdag|Vrydag|Saterdag)/i},s={narrow:[/^S/i,/^M/i,/^D/i,/^W/i,/^D/i,/^V/i,/^S/i],any:[/^So/i,/^Ma/i,/^Di/i,/^Wo/i,/^Do/i,/^Vr/i,/^Sa/i]},o={any:/^(vm|nm|middernag|(?:uur )?die (oggend|middag|aand))/i},r={any:{am:/^vm/i,pm:/^nm/i,midnight:/^middernag/i,noon:/^middaguur/i,morning:/oggend/i,afternoon:/middag/i,evening:/laat middag/i,night:/aand/i}},e={ordinalNumber:m({matchPattern:c,parsePattern:y,valueCallback:function C(B){return parseInt(B,10)}}),era:q({matchPatterns:d,defaultMatchWidth:"wide",parsePatterns:g,defaultParseWidth:"any"}),quarter:q({matchPatterns:p,defaultMatchWidth:"wide",parsePatterns:u,defaultParseWidth:"any",valueCallback:function C(B){return B+1}}),month:q({matchPatterns:l,defaultMatchWidth:"wide",parsePatterns:i,defaultParseWidth:"any"}),day:q({matchPatterns:n,defaultMatchWidth:"wide",parsePatterns:s,defaultParseWidth:"any"}),dayPeriod:q({matchPatterns:o,defaultMatchWidth:"any",parsePatterns:r,defaultParseWidth:"any"})},a={code:"af",formatDistance:x,formatLong:R,formatRelative:V,localize:h,match:e,options:{weekStartsOn:0,firstWeekContainsDate:1}};window.dateFns=N(N({},window.dateFns),{},{locale:N(N({},(J=window.dateFns)===null||J===void 0?void 0:J.locale),{},{af:a})})})();

//# debugId=BD70B1C468EEEF0464756e2164756e21
