{"version": 3, "file": "no-use-before-define.js", "sourceRoot": "", "sources": ["../../src/rules/no-use-before-define.ts"], "names": [], "mappings": ";;AAAA,oEAAkE;AAElE,oDAAoE;AAEpE,kCAAqC;AACrC,mFAAgF;AAEhF,MAAM,aAAa,GACjB,iIAAiI,CAAC;AAEpI;;GAEG;AACH,SAAS,YAAY,CAAC,OAA+B;IACnD,IAAI,SAAS,GAAG,IAAI,CAAC;IACrB,IAAI,OAAO,GAAG,IAAI,CAAC;IACnB,IAAI,KAAK,GAAG,IAAI,CAAC;IACjB,IAAI,SAAS,GAAG,IAAI,CAAC;IACrB,IAAI,QAAQ,GAAG,IAAI,CAAC;IACpB,IAAI,oBAAoB,GAAG,IAAI,CAAC;IAChC,IAAI,iBAAiB,GAAG,KAAK,CAAC;IAE9B,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE,CAAC;QAChC,SAAS,GAAG,OAAO,KAAK,QAAQ,CAAC;IACnC,CAAC;SAAM,IAAI,OAAO,OAAO,KAAK,QAAQ,IAAI,OAAO,IAAI,IAAI,EAAE,CAAC;QAC1D,SAAS,GAAG,OAAO,CAAC,SAAS,KAAK,KAAK,CAAC;QACxC,OAAO,GAAG,OAAO,CAAC,OAAO,KAAK,KAAK,CAAC;QACpC,KAAK,GAAG,OAAO,CAAC,KAAK,KAAK,KAAK,CAAC;QAChC,SAAS,GAAG,OAAO,CAAC,SAAS,KAAK,KAAK,CAAC;QACxC,QAAQ,GAAG,OAAO,CAAC,QAAQ,KAAK,KAAK,CAAC;QACtC,oBAAoB,GAAG,OAAO,CAAC,oBAAoB,KAAK,KAAK,CAAC;QAC9D,iBAAiB,GAAG,OAAO,CAAC,iBAAiB,KAAK,KAAK,CAAC;IAC1D,CAAC;IAED,OAAO;QACL,SAAS;QACT,OAAO;QACP,KAAK;QACL,SAAS;QACT,QAAQ;QACR,oBAAoB;QACpB,iBAAiB;KAClB,CAAC;AACJ,CAAC;AAED;;GAEG;AACH,SAAS,UAAU,CAAC,QAAiC;IACnD,OAAO,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,8BAAc,CAAC,YAAY,CAAC;AAC/D,CAAC;AAED;;GAEG;AACH,SAAS,SAAS,CAAC,QAAiC;IAClD,OAAO,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,8BAAc,CAAC,IAAI,CAAC;AACvD,CAAC;AAED;;GAEG;AACH,SAAS,WAAW,CAClB,QAAiC,EACjC,SAAmC;IAEnC,OAAO,CACL,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,8BAAc,CAAC,UAAU;QACnD,QAAQ,CAAC,KAAK,CAAC,aAAa,KAAK,SAAS,CAAC,IAAI,CAAC,aAAa,CAC9D,CAAC;AACJ,CAAC;AAED;;GAEG;AACH,SAAS,YAAY,CACnB,QAAiC,EACjC,SAAmC;IAEnC,OAAO,CACL,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,8BAAc,CAAC,SAAS;QAClD,QAAQ,CAAC,KAAK,CAAC,aAAa,KAAK,SAAS,CAAC,IAAI,CAAC,aAAa,CAC9D,CAAC;AACJ,CAAC;AAED;;GAEG;AACH,SAAS,eAAe,CACtB,QAAiC,EACjC,SAAmC;IAEnC,OAAO,CACL,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,8BAAc,CAAC,QAAQ;QACjD,QAAQ,CAAC,KAAK,CAAC,aAAa,KAAK,SAAS,CAAC,IAAI,CAAC,aAAa,CAC9D,CAAC;AACJ,CAAC;AAED;;GAEG;AACH,SAAS,cAAc,CAAC,SAAmC;IACzD,MAAM,EAAE,UAAU,EAAE,GAAG,SAAS,CAAC;IACjC,OAAO,CACL,UAAU,CAAC,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,eAAe;QACzD,UAAU,CAAC,MAAM,CAAC,KAAK,KAAK,UAAU,CACvC,CAAC;AACJ,CAAC;AAED;;GAEG;AACH,SAAS,eAAe,CAAC,SAAmC;IAC1D,OAAO,CACL,SAAS,CAAC,eAAe;QACzB,IAAA,uDAA0B,EAAC,SAAS,CAAC,UAAU,CAAC,CACjD,CAAC;AACJ,CAAC;AAED;;GAEG;AACH,SAAS,SAAS,CAChB,IAA4C,EAC5C,QAAgB;IAEhB,OAAO,CAAC,CAAC,IAAI,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,QAAQ,IAAI,QAAQ,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AAC1E,CAAC;AAED;;;GAGG;AACH,SAAS,0BAA0B,CACjC,QAAiC,EACjC,SAAmC;IAEnC,IACE,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,8BAAc,CAAC,SAAS;QAClD,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,KAAK,CAAC,EAC7C,CAAC;QACD,OAAO,KAAK,CAAC;IACf,CAAC;IAED,KAAK,MAAM,IAAI,IAAI,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;QACpD,IACE,SAAS,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;YAC9C,SAAS,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,EAC9C,CAAC;YACD,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;IAED,OAAO,KAAK,CAAC;AACf,CAAC;AAED;;;;;;;;;GASG;AACH,SAAS,eAAe,CACtB,QAAiC,EACjC,SAAmC;IAEnC,IAAI,QAAQ,CAAC,KAAK,KAAK,SAAS,CAAC,IAAI,EAAE,CAAC;QACtC,OAAO,KAAK,CAAC;IACf,CAAC;IAED,IAAI,IAAI,GAA8B,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;IACrE,MAAM,QAAQ,GAAG,SAAS,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;IAE/C,OAAO,IAAI,EAAE,CAAC;QACZ,IAAI,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,kBAAkB,EAAE,CAAC;YACpD,IAAI,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,QAAQ,CAAC,EAAE,CAAC;gBACnC,OAAO,IAAI,CAAC;YACd,CAAC;YACD,IACE,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,cAAc;gBACxD,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,cAAc,CAAC;gBAC5D,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,EAAE,QAAQ,CAAC,EAC7C,CAAC;gBACD,OAAO,IAAI,CAAC;YACd,CAAC;YACD,MAAM;QACR,CAAC;aAAM,IAAI,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,iBAAiB,EAAE,CAAC;YAC1D,IAAI,SAAS,CAAC,IAAI,CAAC,KAAK,EAAE,QAAQ,CAAC,EAAE,CAAC;gBACpC,OAAO,IAAI,CAAC;YACd,CAAC;QACH,CAAC;aAAM,IAAI,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;YACzC,MAAM;QACR,CAAC;QAED,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC;IACrB,CAAC;IAED,OAAO,KAAK,CAAC;AACf,CAAC;AAcD,kBAAe,IAAA,iBAAU,EAAsB;IAC7C,IAAI,EAAE,sBAAsB;IAC5B,IAAI,EAAE;QACJ,IAAI,EAAE,SAAS;QACf,IAAI,EAAE;YACJ,WAAW,EAAE,uDAAuD;YACpE,eAAe,EAAE,IAAI;SACtB;QACD,QAAQ,EAAE;YACR,iBAAiB,EAAE,4CAA4C;SAChE;QACD,MAAM,EAAE;YACN;gBACE,KAAK,EAAE;oBACL;wBACE,IAAI,EAAE,QAAQ;wBACd,IAAI,EAAE,CAAC,QAAQ,CAAC;qBACjB;oBACD;wBACE,IAAI,EAAE,QAAQ;wBACd,UAAU,EAAE;4BACV,SAAS,EAAE;gCACT,WAAW,EACT,wDAAwD;gCAC1D,IAAI,EAAE,SAAS;6BAChB;4BACD,OAAO,EAAE;gCACP,WAAW,EACT,qDAAqD;gCACvD,IAAI,EAAE,SAAS;6BAChB;4BACD,KAAK,EAAE;gCACL,WAAW,EAAE,uCAAuC;gCACpD,IAAI,EAAE,SAAS;6BAChB;4BACD,SAAS,EAAE;gCACT,WAAW,EAAE,4CAA4C;gCACzD,IAAI,EAAE,SAAS;6BAChB;4BACD,QAAQ,EAAE;gCACR,WAAW,EAAE,uCAAuC;gCACpD,IAAI,EAAE,SAAS;6BAChB;4BACD,oBAAoB,EAAE;gCACpB,WAAW,EACT,gFAAgF;gCAClF,IAAI,EAAE,SAAS;6BAChB;4BACD,iBAAiB,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE;yBACvC;wBACD,oBAAoB,EAAE,KAAK;qBAC5B;iBACF;aACF;SACF;KACF;IACD,cAAc,EAAE;QACd;YACE,SAAS,EAAE,IAAI;YACf,OAAO,EAAE,IAAI;YACb,KAAK,EAAE,IAAI;YACX,SAAS,EAAE,IAAI;YACf,QAAQ,EAAE,IAAI;YACd,oBAAoB,EAAE,IAAI;YAC1B,iBAAiB,EAAE,KAAK;SACzB;KACF;IACD,MAAM,CAAC,OAAO,EAAE,kBAAkB;QAChC,MAAM,OAAO,GAAG,YAAY,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,CAAC;QAEpD;;;;WAIG;QACH,SAAS,WAAW,CAClB,QAAiC,EACjC,SAAmC;YAEnC,IAAI,OAAO,CAAC,oBAAoB,IAAI,eAAe,CAAC,SAAS,CAAC,EAAE,CAAC;gBAC/D,OAAO,KAAK,CAAC;YACf,CAAC;YACD,IAAI,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC;gBACzB,OAAO,OAAO,CAAC,SAAS,CAAC;YAC3B,CAAC;YACD,IAAI,YAAY,CAAC,QAAQ,EAAE,SAAS,CAAC,EAAE,CAAC;gBACtC,OAAO,OAAO,CAAC,OAAO,CAAC;YACzB,CAAC;YACD,IAAI,eAAe,CAAC,QAAQ,EAAE,SAAS,CAAC,EAAE,CAAC;gBACzC,OAAO,OAAO,CAAC,SAAS,CAAC;YAC3B,CAAC;YACD,IAAI,WAAW,CAAC,QAAQ,EAAE,SAAS,CAAC,EAAE,CAAC;gBACrC,OAAO,OAAO,CAAC,KAAK,CAAC;YACvB,CAAC;YACD,IAAI,SAAS,CAAC,QAAQ,CAAC,EAAE,CAAC;gBACxB,OAAO,OAAO,CAAC,QAAQ,CAAC;YAC1B,CAAC;YAED,OAAO,IAAI,CAAC;QACd,CAAC;QAED,SAAS,kBAAkB,CACzB,QAAiC,EACjC,SAAmC;YAEnC,OAAO,CACL,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,SAAS,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC;gBACjE,CAAC,CAAC,SAAS,CAAC,gBAAgB,IAAI,eAAe,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC,CACtE,CAAC;QACJ,CAAC;QAED;;WAEG;QACH,SAAS,oBAAoB,CAAC,KAA2B;YACvD,KAAK,CAAC,UAAU,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE;gBACnC,MAAM,QAAQ,GAAG,SAAS,CAAC,QAAQ,CAAC;gBAEpC,SAAS,MAAM;oBACb,OAAO,CAAC,MAAM,CAAC;wBACb,IAAI,EAAE,SAAS,CAAC,UAAU;wBAC1B,SAAS,EAAE,mBAAmB;wBAC9B,IAAI,EAAE;4BACJ,IAAI,EAAE,SAAS,CAAC,UAAU,CAAC,IAAI;yBAChC;qBACF,CAAC,CAAC;gBACL,CAAC;gBAED,+BAA+B;gBAC/B,qBAAqB;gBACrB,wCAAwC;gBACxC,0EAA0E;gBAC1E,+DAA+D;gBAC/D,wBAAwB;gBACxB,IAAI,SAAS,CAAC,IAAI,EAAE,CAAC;oBACnB,OAAO;gBACT,CAAC;gBAED,IAAI,CAAC,OAAO,CAAC,iBAAiB,IAAI,cAAc,CAAC,SAAS,CAAC,EAAE,CAAC;oBAC5D,IAAI,CAAC,QAAQ,IAAI,CAAC,kBAAkB,CAAC,QAAQ,EAAE,SAAS,CAAC,EAAE,CAAC;wBAC1D,MAAM,EAAE,CAAC;oBACX,CAAC;oBACD,OAAO;gBACT,CAAC;gBAED,IAAI,CAAC,QAAQ,EAAE,CAAC;oBACd,OAAO;gBACT,CAAC;gBAED,IACE,QAAQ,CAAC,WAAW,CAAC,MAAM,KAAK,CAAC;oBACjC,kBAAkB,CAAC,QAAQ,EAAE,SAAS,CAAC;oBACvC,CAAC,WAAW,CAAC,QAAQ,EAAE,SAAS,CAAC;oBACjC,0BAA0B,CAAC,QAAQ,EAAE,SAAS,CAAC;oBAC/C,SAAS,CAAC,IAAI,CAAC,IAAI,KAAK,gBAAQ,CAAC,KAAK,CAAC,SAAS,CAAC,YAAY,EAC7D,CAAC;oBACD,OAAO;gBACT,CAAC;gBAED,WAAW;gBACX,MAAM,EAAE,CAAC;YACX,CAAC,CAAC,CAAC;YAEH,KAAK,CAAC,WAAW,CAAC,OAAO,CAAC,oBAAoB,CAAC,CAAC;QAClD,CAAC;QAED,OAAO;YACL,OAAO,CAAC,IAAI;gBACV,oBAAoB,CAAC,OAAO,CAAC,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;YAC1D,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC,CAAC"}