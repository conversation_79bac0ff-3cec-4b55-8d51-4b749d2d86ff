var A=function(Z){return A=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(Y){return typeof Y}:function(Y){return Y&&typeof Symbol=="function"&&Y.constructor===Symbol&&Y!==Symbol.prototype?"symbol":typeof Y},A(Z)},N=function(Z,Y){var I=Object.keys(Z);if(Object.getOwnPropertySymbols){var U=Object.getOwnPropertySymbols(Z);Y&&(U=U.filter(function(K){return Object.getOwnPropertyDescriptor(Z,K).enumerable})),I.push.apply(I,U)}return I},D=function(Z){for(var Y=1;Y<arguments.length;Y++){var I=arguments[Y]!=null?arguments[Y]:{};Y%2?N(Object(I),!0).forEach(function(U){CC(Z,U,I[U])}):Object.getOwnPropertyDescriptors?Object.defineProperties(Z,Object.getOwnPropertyDescriptors(I)):N(Object(I)).forEach(function(U){Object.defineProperty(Z,U,Object.getOwnPropertyDescriptor(I,U))})}return Z},CC=function(Z,Y,I){if(Y=BC(Y),Y in Z)Object.defineProperty(Z,Y,{value:I,enumerable:!0,configurable:!0,writable:!0});else Z[Y]=I;return Z},BC=function(Z){var Y=JC(Z,"string");return A(Y)=="symbol"?Y:String(Y)},JC=function(Z,Y){if(A(Z)!="object"||!Z)return Z;var I=Z[Symbol.toPrimitive];if(I!==void 0){var U=I.call(Z,Y||"default");if(A(U)!="object")return U;throw new TypeError("@@toPrimitive must return a primitive value.")}return(Y==="string"?String:Number)(Z)};(function(Z){var Y=Object.defineProperty,I=function B(X,C){for(var J in C)Y(X,J,{get:C[J],enumerable:!0,configurable:!0,set:function G(H){return C[J]=function(){return H}}})},U={lessThanXSeconds:{one:"moins d\u2019une seconde",other:"moins de {{count}} secondes"},xSeconds:{one:"1 seconde",other:"{{count}} secondes"},halfAMinute:"30 secondes",lessThanXMinutes:{one:"moins d\u2019une minute",other:"moins de {{count}} minutes"},xMinutes:{one:"1 minute",other:"{{count}} minutes"},aboutXHours:{one:"environ 1 heure",other:"environ {{count}} heures"},xHours:{one:"1 heure",other:"{{count}} heures"},xDays:{one:"1 jour",other:"{{count}} jours"},aboutXWeeks:{one:"environ 1 semaine",other:"environ {{count}} semaines"},xWeeks:{one:"1 semaine",other:"{{count}} semaines"},aboutXMonths:{one:"environ 1 mois",other:"environ {{count}} mois"},xMonths:{one:"1 mois",other:"{{count}} mois"},aboutXYears:{one:"environ 1 an",other:"environ {{count}} ans"},xYears:{one:"1 an",other:"{{count}} ans"},overXYears:{one:"plus d\u2019un an",other:"plus de {{count}} ans"},almostXYears:{one:"presqu\u2019un an",other:"presque {{count}} ans"}},K=function B(X,C,J){var G,H=U[X];if(typeof H==="string")G=H;else if(C===1)G=H.one;else G=H.other.replace("{{count}}",String(C));if(J!==null&&J!==void 0&&J.addSuffix)if(J.comparison&&J.comparison>0)return"dans "+G;else return"il y a "+G;return G},$={lastWeek:"eeee 'dernier \xE0' p",yesterday:"'hier \xE0' p",today:"'aujourd\u2019hui \xE0' p",tomorrow:"'demain \xE0' p'",nextWeek:"eeee 'prochain \xE0' p",other:"P"},V=function B(X,C,J,G){return $[X]};function E(B){return function(X,C){var J=C!==null&&C!==void 0&&C.context?String(C.context):"standalone",G;if(J==="formatting"&&B.formattingValues){var H=B.defaultFormattingWidth||B.defaultWidth,Q=C!==null&&C!==void 0&&C.width?String(C.width):H;G=B.formattingValues[Q]||B.formattingValues[H]}else{var W=B.defaultWidth,z=C!==null&&C!==void 0&&C.width?String(C.width):B.defaultWidth;G=B.values[z]||B.values[W]}var q=B.argumentCallback?B.argumentCallback(X):X;return G[q]}}var M={narrow:["av. J.-C","ap. J.-C"],abbreviated:["av. J.-C","ap. J.-C"],wide:["avant J\xE9sus-Christ","apr\xE8s J\xE9sus-Christ"]},S={narrow:["T1","T2","T3","T4"],abbreviated:["1er trim.","2\xE8me trim.","3\xE8me trim.","4\xE8me trim."],wide:["1er trimestre","2\xE8me trimestre","3\xE8me trimestre","4\xE8me trimestre"]},R={narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],abbreviated:["janv.","f\xE9vr.","mars","avr.","mai","juin","juil.","ao\xFBt","sept.","oct.","nov.","d\xE9c."],wide:["janvier","f\xE9vrier","mars","avril","mai","juin","juillet","ao\xFBt","septembre","octobre","novembre","d\xE9cembre"]},L={narrow:["D","L","M","M","J","V","S"],short:["di","lu","ma","me","je","ve","sa"],abbreviated:["dim.","lun.","mar.","mer.","jeu.","ven.","sam."],wide:["dimanche","lundi","mardi","mercredi","jeudi","vendredi","samedi"]},j={narrow:{am:"AM",pm:"PM",midnight:"minuit",noon:"midi",morning:"mat.",afternoon:"ap.m.",evening:"soir",night:"mat."},abbreviated:{am:"AM",pm:"PM",midnight:"minuit",noon:"midi",morning:"matin",afternoon:"apr\xE8s-midi",evening:"soir",night:"matin"},wide:{am:"AM",pm:"PM",midnight:"minuit",noon:"midi",morning:"du matin",afternoon:"de l\u2019apr\xE8s-midi",evening:"du soir",night:"du matin"}},v=function B(X,C){var J=Number(X),G=C===null||C===void 0?void 0:C.unit;if(J===0)return"0";var H=["year","week","hour","minute","second"],Q;if(J===1)Q=G&&H.includes(G)?"\xE8re":"er";else Q="\xE8me";return J+Q},w=["MMM","MMMM"],P={preprocessor:function B(X,C){if(X.getDate()===1)return C;var J=C.some(function(G){return G.isToken&&w.includes(G.value)});if(!J)return C;return C.map(function(G){return G.isToken&&G.value==="do"?{isToken:!0,value:"d"}:G})},ordinalNumber:v,era:E({values:M,defaultWidth:"wide"}),quarter:E({values:S,defaultWidth:"wide",argumentCallback:function B(X){return X-1}}),month:E({values:R,defaultWidth:"wide"}),day:E({values:L,defaultWidth:"wide"}),dayPeriod:E({values:j,defaultWidth:"wide"})};function T(B){return function(X){var C=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},J=C.width,G=J&&B.matchPatterns[J]||B.matchPatterns[B.defaultMatchWidth],H=X.match(G);if(!H)return null;var Q=H[0],W=J&&B.parsePatterns[J]||B.parsePatterns[B.defaultParseWidth],z=Array.isArray(W)?_(W,function(x){return x.test(Q)}):F(W,function(x){return x.test(Q)}),q;q=B.valueCallback?B.valueCallback(z):z,q=C.valueCallback?C.valueCallback(q):q;var t=X.slice(Q.length);return{value:q,rest:t}}}var F=function B(X,C){for(var J in X)if(Object.prototype.hasOwnProperty.call(X,J)&&C(X[J]))return J;return},_=function B(X,C){for(var J=0;J<X.length;J++)if(C(X[J]))return J;return};function f(B){return function(X){var C=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},J=X.match(B.matchPattern);if(!J)return null;var G=J[0],H=X.match(B.parsePattern);if(!H)return null;var Q=B.valueCallback?B.valueCallback(H[0]):H[0];Q=C.valueCallback?C.valueCallback(Q):Q;var W=X.slice(G.length);return{value:Q,rest:W}}}var k=/^(\d+)(ième|ère|ème|er|e)?/i,b=/\d+/i,h={narrow:/^(av\.J\.C|ap\.J\.C|ap\.J\.-C)/i,abbreviated:/^(av\.J\.-C|av\.J-C|apr\.J\.-C|apr\.J-C|ap\.J-C)/i,wide:/^(avant Jésus-Christ|après Jésus-Christ)/i},m={any:[/^av/i,/^ap/i]},c={narrow:/^T?[1234]/i,abbreviated:/^[1234](er|ème|e)? trim\.?/i,wide:/^[1234](er|ème|e)? trimestre/i},y={any:[/1/i,/2/i,/3/i,/4/i]},g={narrow:/^[jfmasond]/i,abbreviated:/^(janv|févr|mars|avr|mai|juin|juill|juil|août|sept|oct|nov|déc)\.?/i,wide:/^(janvier|février|mars|avril|mai|juin|juillet|août|septembre|octobre|novembre|décembre)/i},d={narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^mar/i,/^av/i,/^ma/i,/^juin/i,/^juil/i,/^ao/i,/^s/i,/^o/i,/^n/i,/^d/i]},u={narrow:/^[lmjvsd]/i,short:/^(di|lu|ma|me|je|ve|sa)/i,abbreviated:/^(dim|lun|mar|mer|jeu|ven|sam)\.?/i,wide:/^(dimanche|lundi|mardi|mercredi|jeudi|vendredi|samedi)/i},l={narrow:[/^d/i,/^l/i,/^m/i,/^m/i,/^j/i,/^v/i,/^s/i],any:[/^di/i,/^lu/i,/^ma/i,/^me/i,/^je/i,/^ve/i,/^sa/i]},p={narrow:/^(a|p|minuit|midi|mat\.?|ap\.?m\.?|soir|nuit)/i,any:/^([ap]\.?\s?m\.?|du matin|de l'après[-\s]midi|du soir|de la nuit)/i},i={any:{am:/^a/i,pm:/^p/i,midnight:/^min/i,noon:/^mid/i,morning:/mat/i,afternoon:/ap/i,evening:/soir/i,night:/nuit/i}},n={ordinalNumber:f({matchPattern:k,parsePattern:b,valueCallback:function B(X){return parseInt(X)}}),era:T({matchPatterns:h,defaultMatchWidth:"wide",parsePatterns:m,defaultParseWidth:"any"}),quarter:T({matchPatterns:c,defaultMatchWidth:"wide",parsePatterns:y,defaultParseWidth:"any",valueCallback:function B(X){return X+1}}),month:T({matchPatterns:g,defaultMatchWidth:"wide",parsePatterns:d,defaultParseWidth:"any"}),day:T({matchPatterns:u,defaultMatchWidth:"wide",parsePatterns:l,defaultParseWidth:"any"}),dayPeriod:T({matchPatterns:p,defaultMatchWidth:"any",parsePatterns:i,defaultParseWidth:"any"})};function O(B){return function(){var X=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},C=X.width?String(X.width):B.defaultWidth,J=B.formats[C]||B.formats[B.defaultWidth];return J}}var s={full:"EEEE d MMMM y",long:"d MMMM y",medium:"d MMM y",short:"yy-MM-dd"},o={full:"HH:mm:ss zzzz",long:"HH:mm:ss z",medium:"HH:mm:ss",short:"HH:mm"},e={full:"{{date}} '\xE0' {{time}}",long:"{{date}} '\xE0' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},r={date:O({formats:s,defaultWidth:"full"}),time:O({formats:o,defaultWidth:"full"}),dateTime:O({formats:e,defaultWidth:"full"})},a={code:"fr-CA",formatDistance:K,formatLong:r,formatRelative:V,localize:P,match:n,options:{weekStartsOn:0,firstWeekContainsDate:1}};window.dateFns=D(D({},window.dateFns),{},{locale:D(D({},(Z=window.dateFns)===null||Z===void 0?void 0:Z.locale),{},{frCA:a})})})();

//# debugId=235E69DC2533E05064756e2164756e21
