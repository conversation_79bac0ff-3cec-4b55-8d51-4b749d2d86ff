(function (root, factory) {
  if (typeof define === "function" && define.amd) {
    // AMD. Register as an anonymous module.
    define([], factory);
  } else if (typeof module === "object" && module.exports) {
    // Node. Does not work with strict CommonJS, but
    // only CommonJS-like environments that support module.exports,
    // like Node.
    module.exports = factory();
  } else {
    // Browser globals (root is window)
    root.hyphenationPatternsId = factory();
  }
})(this, function () {
  return [
    "01,21,201,232,0023,00203,2,021,02,10102,3,0202,00304",
    '{"a":[{"i":{"r":8},"n":{".":10}},0],"e":0,"i":[{"o":{"n":7}},0],"o":0,"u":0,"b":{"d":1,"j":1,"k":1,"n":1,"s":1,"t":1,"a":{"g":{"a":{"i":9}},"n":{".":1}}},"c":{"k":1,"n":1,"a":{"n":{".":1}}},"d":{"k":1,"n":1,"p":1,"a":{"n":{".":1}}},"f":{"d":1,"k":1,"n":1,"t":1,"a":{"n":{".":1}}},"g":{"g":1,"k":1,"n":1,"a":{"n":{".":1}}},"h":{"k":1,"l":1,"m":1,"n":1,"w":1,"a":{"n":{".":1}}},"j":{"k":1,"n":1,"a":{"n":{".":1}}},"k":{"b":1,"k":1,"m":1,"n":1,"r":1,"s":1,"t":1,"a":{"n":{".":1}}},"l":{"b":1,"f":1,"g":1,"h":1,"k":1,"m":1,"n":1,"s":1,"t":1,"q":1,"a":{"n":{".":1}}},"m":{"b":1,"k":1,"l":1,"m":1,"n":1,"p":1,"r":1,"s":1,"a":{"n":{".":1}}},"n":{"c":1,"d":1,"f":1,"j":1,"k":1,"n":1,"p":1,"s":[{"t":3},1],"t":1,"v":1,"g":{"g":2,"h":2,"k":2,"n":2,"s":2,".":6,"a":{"n":{".":2}}},"y":{".":6},"a":{"n":{".":1}}},"p":{"k":1,"n":1,"p":1,"r":1,"t":1,"a":{"n":{".":1}}},"r":{"b":1,"c":1,"f":1,"g":1,"h":1,"j":1,"k":1,"l":1,"m":1,"n":1,"p":1,"r":1,"s":1,"t":1,"w":1,"y":1,"a":{"n":{".":1}}},"s":{"b":1,"k":1,"l":1,"m":1,"n":1,"p":1,"r":1,"s":1,"t":1,"w":1,"a":{"n":{".":1}}},"t":{"k":1,"l":1,"n":1,"t":1,"a":{"n":{".":1}}},"w":{"t":1},".":{"b":{"e":{"r":4}},"t":{"e":{"r":4},"a":{"n":{"g":{"a":{"n":{".":12}}}}}},"m":{"e":{"n":{"g":5}},"a":{"n":{"g":{"a":{"n":{".":12}}}}}},"p":{"e":{"r":4},"a":{"n":{"g":{"a":{"n":{".":12}}}}}},"a":{"t":{"a":{"u":11}}},"l":{"e":{"n":{"g":{"a":{"n":{".":12}}}}}},"j":{"a":{"n":{"g":{"a":{"n":{".":12}}}}}},"r":{"i":{"n":{"g":{"a":{"n":{".":12}}}}}},"d":{"e":{"n":{"g":{"a":{"n":{".":12}}}}}}},"v":{"a":{"n":{".":1}}},"z":{"a":{"n":{".":1}}}}',
    [
      "be-ra-be be-ra-hi be-rak be-ran-da be-ran-dal be-rang",
      "be-ra-ngas-an",
      "be-rang-sang",
      "be-ra-ngus",
      "be-ra-ni",
      "be-ran-tak-an",
      "be-ran-tam",
      "be-ran-tas",
      "be-ra-pa",
      "be-ras",
      "be-ren-deng",
      "be-re-ngut",
      "be-re-rot",
      "be-res",
      "be-re-wok",
      "be-ri",
      "be-ri-ngas",
      "be-ri-sik",
      "be-ri-ta",
      "be-rok",
      "be-ron-dong",
      "be-ron-tak",
      "be-ru-du",
      "be-ruk",
      "be-run-tun",
      "peng-eks-por",
      "peng-im-por",
      "te-ra",
      "te-rang",
      "te-ras",
      "te-ra-si",
      "te-ra-tai",
      "te-ra-wang",
      "te-ra-weh",
      "te-ri-ak",
      "te-ri-gu",
      "te-rik",
      "te-ri-ma",
      "te-ri-pang",
      "te-ro-bos",
      "te-ro-bos-an",
      "te-ro-mol",
      "te-rom-pah",
      "te-rom-pet",
      "te-ro-pong",
      "             te-ro-wong-an te-ru-buk te-ru-na te-rus te-ru-si"
    ]
  ];
});
