(()=>{var $;function I(B){return I=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(C){return typeof C}:function(C){return C&&typeof Symbol=="function"&&C.constructor===Symbol&&C!==Symbol.prototype?"symbol":typeof C},I(B)}function A(B,C){var G=Object.keys(B);if(Object.getOwnPropertySymbols){var H=Object.getOwnPropertySymbols(B);C&&(H=H.filter(function(X){return Object.getOwnPropertyDescriptor(B,X).enumerable})),G.push.apply(G,H)}return G}function T(B){for(var C=1;C<arguments.length;C++){var G=arguments[C]!=null?arguments[C]:{};C%2?A(Object(G),!0).forEach(function(H){E(B,H,G[H])}):Object.getOwnPropertyDescriptors?Object.defineProperties(B,Object.getOwnPropertyDescriptors(G)):A(Object(G)).forEach(function(H){Object.defineProperty(B,H,Object.getOwnPropertyDescriptor(G,H))})}return B}function E(B,C,G){if(C=N(C),C in B)Object.defineProperty(B,C,{value:G,enumerable:!0,configurable:!0,writable:!0});else B[C]=G;return B}function N(B){var C=z(B,"string");return I(C)=="symbol"?C:String(C)}function z(B,C){if(I(B)!="object"||!B)return B;var G=B[Symbol.toPrimitive];if(G!==void 0){var H=G.call(B,C||"default");if(I(H)!="object")return H;throw new TypeError("@@toPrimitive must return a primitive value.")}return(C==="string"?String:Number)(B)}var W=Object.defineProperty,HB=function B(C,G){for(var H in G)W(C,H,{get:G[H],enumerable:!0,configurable:!0,set:function X(J){return G[H]=function(){return J}}})};function D(B){return B.one!==void 0}var S={lessThanXSeconds:{one:{default:"\u0B92\u0BB0\u0BC1 \u0BB5\u0BBF\u0BA9\u0BBE\u0B9F\u0BBF\u0B95\u0BCD\u0B95\u0BC1 \u0B95\u0BC1\u0BB1\u0BC8\u0BB5\u0BBE\u0B95",in:"\u0B92\u0BB0\u0BC1 \u0BB5\u0BBF\u0BA9\u0BBE\u0B9F\u0BBF\u0B95\u0BCD\u0B95\u0BC1\u0BB3\u0BCD",ago:"\u0B92\u0BB0\u0BC1 \u0BB5\u0BBF\u0BA9\u0BBE\u0B9F\u0BBF\u0B95\u0BCD\u0B95\u0BC1 \u0BAE\u0BC1\u0BA9\u0BCD\u0BAA\u0BC1"},other:{default:"{{count}} \u0BB5\u0BBF\u0BA9\u0BBE\u0B9F\u0BBF\u0B95\u0BB3\u0BC1\u0B95\u0BCD\u0B95\u0BC1 \u0B95\u0BC1\u0BB1\u0BC8\u0BB5\u0BBE\u0B95",in:"{{count}} \u0BB5\u0BBF\u0BA9\u0BBE\u0B9F\u0BBF\u0B95\u0BB3\u0BC1\u0B95\u0BCD\u0B95\u0BC1\u0BB3\u0BCD",ago:"{{count}} \u0BB5\u0BBF\u0BA9\u0BBE\u0B9F\u0BBF\u0B95\u0BB3\u0BC1\u0B95\u0BCD\u0B95\u0BC1 \u0BAE\u0BC1\u0BA9\u0BCD\u0BAA\u0BC1"}},xSeconds:{one:{default:"1 \u0BB5\u0BBF\u0BA9\u0BBE\u0B9F\u0BBF",in:"1 \u0BB5\u0BBF\u0BA9\u0BBE\u0B9F\u0BBF\u0BAF\u0BBF\u0BB2\u0BCD",ago:"1 \u0BB5\u0BBF\u0BA9\u0BBE\u0B9F\u0BBF \u0BAE\u0BC1\u0BA9\u0BCD\u0BAA\u0BC1"},other:{default:"{{count}} \u0BB5\u0BBF\u0BA8\u0BBE\u0B9F\u0BBF\u0B95\u0BB3\u0BCD",in:"{{count}} \u0BB5\u0BBF\u0BA9\u0BBE\u0B9F\u0BBF\u0B95\u0BB3\u0BBF\u0BB2\u0BCD",ago:"{{count}} \u0BB5\u0BBF\u0BA8\u0BBE\u0B9F\u0BBF\u0B95\u0BB3\u0BC1\u0B95\u0BCD\u0B95\u0BC1 \u0BAE\u0BC1\u0BA9\u0BCD\u0BAA\u0BC1"}},halfAMinute:{default:"\u0B85\u0BB0\u0BC8 \u0BA8\u0BBF\u0BAE\u0BBF\u0B9F\u0BAE\u0BCD",in:"\u0B85\u0BB0\u0BC8 \u0BA8\u0BBF\u0BAE\u0BBF\u0B9F\u0BA4\u0BCD\u0BA4\u0BBF\u0BB2\u0BCD",ago:"\u0B85\u0BB0\u0BC8 \u0BA8\u0BBF\u0BAE\u0BBF\u0B9F\u0BAE\u0BCD \u0BAE\u0BC1\u0BA9\u0BCD\u0BAA\u0BC1"},lessThanXMinutes:{one:{default:"\u0B92\u0BB0\u0BC1 \u0BA8\u0BBF\u0BAE\u0BBF\u0B9F\u0BA4\u0BCD\u0BA4\u0BBF\u0BB1\u0BCD\u0B95\u0BC1\u0BAE\u0BCD \u0B95\u0BC1\u0BB1\u0BC8\u0BB5\u0BBE\u0B95",in:"\u0B92\u0BB0\u0BC1 \u0BA8\u0BBF\u0BAE\u0BBF\u0B9F\u0BA4\u0BCD\u0BA4\u0BBF\u0BB1\u0BCD\u0B95\u0BC1\u0BB3\u0BCD",ago:"\u0B92\u0BB0\u0BC1 \u0BA8\u0BBF\u0BAE\u0BBF\u0B9F\u0BA4\u0BCD\u0BA4\u0BBF\u0BB1\u0BCD\u0B95\u0BC1 \u0BAE\u0BC1\u0BA9\u0BCD\u0BAA\u0BC1"},other:{default:"{{count}} \u0BA8\u0BBF\u0BAE\u0BBF\u0B9F\u0B99\u0BCD\u0B95\u0BB3\u0BC1\u0B95\u0BCD\u0B95\u0BC1\u0BAE\u0BCD \u0B95\u0BC1\u0BB1\u0BC8\u0BB5\u0BBE\u0B95",in:"{{count}} \u0BA8\u0BBF\u0BAE\u0BBF\u0B9F\u0B99\u0BCD\u0B95\u0BB3\u0BC1\u0B95\u0BCD\u0B95\u0BC1\u0BB3\u0BCD",ago:"{{count}} \u0BA8\u0BBF\u0BAE\u0BBF\u0B9F\u0B99\u0BCD\u0B95\u0BB3\u0BC1\u0B95\u0BCD\u0B95\u0BC1 \u0BAE\u0BC1\u0BA9\u0BCD\u0BAA\u0BC1"}},xMinutes:{one:{default:"1 \u0BA8\u0BBF\u0BAE\u0BBF\u0B9F\u0BAE\u0BCD",in:"1 \u0BA8\u0BBF\u0BAE\u0BBF\u0B9F\u0BA4\u0BCD\u0BA4\u0BBF\u0BB2\u0BCD",ago:"1 \u0BA8\u0BBF\u0BAE\u0BBF\u0B9F\u0BAE\u0BCD \u0BAE\u0BC1\u0BA9\u0BCD\u0BAA\u0BC1"},other:{default:"{{count}} \u0BA8\u0BBF\u0BAE\u0BBF\u0B9F\u0B99\u0BCD\u0B95\u0BB3\u0BCD",in:"{{count}} \u0BA8\u0BBF\u0BAE\u0BBF\u0B9F\u0B99\u0BCD\u0B95\u0BB3\u0BBF\u0BB2\u0BCD",ago:"{{count}} \u0BA8\u0BBF\u0BAE\u0BBF\u0B9F\u0B99\u0BCD\u0B95\u0BB3\u0BC1\u0B95\u0BCD\u0B95\u0BC1 \u0BAE\u0BC1\u0BA9\u0BCD\u0BAA\u0BC1"}},aboutXHours:{one:{default:"\u0B9A\u0BC1\u0BAE\u0BBE\u0BB0\u0BCD 1 \u0BAE\u0BA3\u0BBF \u0BA8\u0BC7\u0BB0\u0BAE\u0BCD",in:"\u0B9A\u0BC1\u0BAE\u0BBE\u0BB0\u0BCD 1 \u0BAE\u0BA3\u0BBF \u0BA8\u0BC7\u0BB0\u0BA4\u0BCD\u0BA4\u0BBF\u0BB2\u0BCD",ago:"\u0B9A\u0BC1\u0BAE\u0BBE\u0BB0\u0BCD 1 \u0BAE\u0BA3\u0BBF \u0BA8\u0BC7\u0BB0\u0BA4\u0BCD\u0BA4\u0BBF\u0BB1\u0BCD\u0B95\u0BC1 \u0BAE\u0BC1\u0BA9\u0BCD\u0BAA\u0BC1"},other:{default:"\u0B9A\u0BC1\u0BAE\u0BBE\u0BB0\u0BCD {{count}} \u0BAE\u0BA3\u0BBF \u0BA8\u0BC7\u0BB0\u0BAE\u0BCD",in:"\u0B9A\u0BC1\u0BAE\u0BBE\u0BB0\u0BCD {{count}} \u0BAE\u0BA3\u0BBF \u0BA8\u0BC7\u0BB0\u0BA4\u0BCD\u0BA4\u0BBF\u0BB1\u0BCD\u0B95\u0BC1 \u0BAE\u0BC1\u0BA9\u0BCD\u0BAA\u0BC1",ago:"\u0B9A\u0BC1\u0BAE\u0BBE\u0BB0\u0BCD {{count}} \u0BAE\u0BA3\u0BBF \u0BA8\u0BC7\u0BB0\u0BA4\u0BCD\u0BA4\u0BBF\u0BB2\u0BCD"}},xHours:{one:{default:"1 \u0BAE\u0BA3\u0BBF \u0BA8\u0BC7\u0BB0\u0BAE\u0BCD",in:"1 \u0BAE\u0BA3\u0BBF \u0BA8\u0BC7\u0BB0\u0BA4\u0BCD\u0BA4\u0BBF\u0BB2\u0BCD",ago:"1 \u0BAE\u0BA3\u0BBF \u0BA8\u0BC7\u0BB0\u0BA4\u0BCD\u0BA4\u0BBF\u0BB1\u0BCD\u0B95\u0BC1 \u0BAE\u0BC1\u0BA9\u0BCD\u0BAA\u0BC1"},other:{default:"{{count}} \u0BAE\u0BA3\u0BBF \u0BA8\u0BC7\u0BB0\u0BAE\u0BCD",in:"{{count}} \u0BAE\u0BA3\u0BBF \u0BA8\u0BC7\u0BB0\u0BA4\u0BCD\u0BA4\u0BBF\u0BB2\u0BCD",ago:"{{count}} \u0BAE\u0BA3\u0BBF \u0BA8\u0BC7\u0BB0\u0BA4\u0BCD\u0BA4\u0BBF\u0BB1\u0BCD\u0B95\u0BC1 \u0BAE\u0BC1\u0BA9\u0BCD\u0BAA\u0BC1"}},xDays:{one:{default:"1 \u0BA8\u0BBE\u0BB3\u0BCD",in:"1 \u0BA8\u0BBE\u0BB3\u0BBF\u0BB2\u0BCD",ago:"1 \u0BA8\u0BBE\u0BB3\u0BCD \u0BAE\u0BC1\u0BA9\u0BCD\u0BAA\u0BC1"},other:{default:"{{count}} \u0BA8\u0BBE\u0B9F\u0BCD\u0B95\u0BB3\u0BCD",in:"{{count}} \u0BA8\u0BBE\u0B9F\u0BCD\u0B95\u0BB3\u0BBF\u0BB2\u0BCD",ago:"{{count}} \u0BA8\u0BBE\u0B9F\u0BCD\u0B95\u0BB3\u0BC1\u0B95\u0BCD\u0B95\u0BC1 \u0BAE\u0BC1\u0BA9\u0BCD\u0BAA\u0BC1"}},aboutXWeeks:{one:{default:"\u0B9A\u0BC1\u0BAE\u0BBE\u0BB0\u0BCD 1 \u0BB5\u0BBE\u0BB0\u0BAE\u0BCD",in:"\u0B9A\u0BC1\u0BAE\u0BBE\u0BB0\u0BCD 1 \u0BB5\u0BBE\u0BB0\u0BA4\u0BCD\u0BA4\u0BBF\u0BB2\u0BCD",ago:"\u0B9A\u0BC1\u0BAE\u0BBE\u0BB0\u0BCD 1 \u0BB5\u0BBE\u0BB0\u0BAE\u0BCD \u0BAE\u0BC1\u0BA9\u0BCD\u0BAA\u0BC1"},other:{default:"\u0B9A\u0BC1\u0BAE\u0BBE\u0BB0\u0BCD {{count}} \u0BB5\u0BBE\u0BB0\u0B99\u0BCD\u0B95\u0BB3\u0BCD",in:"\u0B9A\u0BC1\u0BAE\u0BBE\u0BB0\u0BCD {{count}} \u0BB5\u0BBE\u0BB0\u0B99\u0BCD\u0B95\u0BB3\u0BBF\u0BB2\u0BCD",ago:"\u0B9A\u0BC1\u0BAE\u0BBE\u0BB0\u0BCD {{count}} \u0BB5\u0BBE\u0BB0\u0B99\u0BCD\u0B95\u0BB3\u0BC1\u0B95\u0BCD\u0B95\u0BC1 \u0BAE\u0BC1\u0BA9\u0BCD\u0BAA\u0BC1"}},xWeeks:{one:{default:"1 \u0BB5\u0BBE\u0BB0\u0BAE\u0BCD",in:"1 \u0BB5\u0BBE\u0BB0\u0BA4\u0BCD\u0BA4\u0BBF\u0BB2\u0BCD",ago:"1 \u0BB5\u0BBE\u0BB0\u0BAE\u0BCD \u0BAE\u0BC1\u0BA9\u0BCD\u0BAA\u0BC1"},other:{default:"{{count}} \u0BB5\u0BBE\u0BB0\u0B99\u0BCD\u0B95\u0BB3\u0BCD",in:"{{count}} \u0BB5\u0BBE\u0BB0\u0B99\u0BCD\u0B95\u0BB3\u0BBF\u0BB2\u0BCD",ago:"{{count}} \u0BB5\u0BBE\u0BB0\u0B99\u0BCD\u0B95\u0BB3\u0BC1\u0B95\u0BCD\u0B95\u0BC1 \u0BAE\u0BC1\u0BA9\u0BCD\u0BAA\u0BC1"}},aboutXMonths:{one:{default:"\u0B9A\u0BC1\u0BAE\u0BBE\u0BB0\u0BCD 1 \u0BAE\u0BBE\u0BA4\u0BAE\u0BCD",in:"\u0B9A\u0BC1\u0BAE\u0BBE\u0BB0\u0BCD 1 \u0BAE\u0BBE\u0BA4\u0BA4\u0BCD\u0BA4\u0BBF\u0BB2\u0BCD",ago:"\u0B9A\u0BC1\u0BAE\u0BBE\u0BB0\u0BCD 1 \u0BAE\u0BBE\u0BA4\u0BA4\u0BCD\u0BA4\u0BBF\u0BB1\u0BCD\u0B95\u0BC1 \u0BAE\u0BC1\u0BA9\u0BCD\u0BAA\u0BC1"},other:{default:"\u0B9A\u0BC1\u0BAE\u0BBE\u0BB0\u0BCD {{count}} \u0BAE\u0BBE\u0BA4\u0B99\u0BCD\u0B95\u0BB3\u0BCD",in:"\u0B9A\u0BC1\u0BAE\u0BBE\u0BB0\u0BCD {{count}} \u0BAE\u0BBE\u0BA4\u0B99\u0BCD\u0B95\u0BB3\u0BBF\u0BB2\u0BCD",ago:"\u0B9A\u0BC1\u0BAE\u0BBE\u0BB0\u0BCD {{count}} \u0BAE\u0BBE\u0BA4\u0B99\u0BCD\u0B95\u0BB3\u0BC1\u0B95\u0BCD\u0B95\u0BC1 \u0BAE\u0BC1\u0BA9\u0BCD\u0BAA\u0BC1"}},xMonths:{one:{default:"1 \u0BAE\u0BBE\u0BA4\u0BAE\u0BCD",in:"1 \u0BAE\u0BBE\u0BA4\u0BA4\u0BCD\u0BA4\u0BBF\u0BB2\u0BCD",ago:"1 \u0BAE\u0BBE\u0BA4\u0BAE\u0BCD \u0BAE\u0BC1\u0BA9\u0BCD\u0BAA\u0BC1"},other:{default:"{{count}} \u0BAE\u0BBE\u0BA4\u0B99\u0BCD\u0B95\u0BB3\u0BCD",in:"{{count}} \u0BAE\u0BBE\u0BA4\u0B99\u0BCD\u0B95\u0BB3\u0BBF\u0BB2\u0BCD",ago:"{{count}} \u0BAE\u0BBE\u0BA4\u0B99\u0BCD\u0B95\u0BB3\u0BC1\u0B95\u0BCD\u0B95\u0BC1 \u0BAE\u0BC1\u0BA9\u0BCD\u0BAA\u0BC1"}},aboutXYears:{one:{default:"\u0B9A\u0BC1\u0BAE\u0BBE\u0BB0\u0BCD 1 \u0BB5\u0BB0\u0BC1\u0B9F\u0BAE\u0BCD",in:"\u0B9A\u0BC1\u0BAE\u0BBE\u0BB0\u0BCD 1 \u0B86\u0BA3\u0BCD\u0B9F\u0BBF\u0BB2\u0BCD",ago:"\u0B9A\u0BC1\u0BAE\u0BBE\u0BB0\u0BCD 1 \u0BB5\u0BB0\u0BC1\u0B9F\u0BAE\u0BCD \u0BAE\u0BC1\u0BA9\u0BCD\u0BAA\u0BC1"},other:{default:"\u0B9A\u0BC1\u0BAE\u0BBE\u0BB0\u0BCD {{count}} \u0B86\u0BA3\u0BCD\u0B9F\u0BC1\u0B95\u0BB3\u0BCD",in:"\u0B9A\u0BC1\u0BAE\u0BBE\u0BB0\u0BCD {{count}} \u0B86\u0BA3\u0BCD\u0B9F\u0BC1\u0B95\u0BB3\u0BBF\u0BB2\u0BCD",ago:"\u0B9A\u0BC1\u0BAE\u0BBE\u0BB0\u0BCD {{count}} \u0B86\u0BA3\u0BCD\u0B9F\u0BC1\u0B95\u0BB3\u0BC1\u0B95\u0BCD\u0B95\u0BC1 \u0BAE\u0BC1\u0BA9\u0BCD\u0BAA\u0BC1"}},xYears:{one:{default:"1 \u0BB5\u0BB0\u0BC1\u0B9F\u0BAE\u0BCD",in:"1 \u0B86\u0BA3\u0BCD\u0B9F\u0BBF\u0BB2\u0BCD",ago:"1 \u0BB5\u0BB0\u0BC1\u0B9F\u0BAE\u0BCD \u0BAE\u0BC1\u0BA9\u0BCD\u0BAA\u0BC1"},other:{default:"{{count}} \u0B86\u0BA3\u0BCD\u0B9F\u0BC1\u0B95\u0BB3\u0BCD",in:"{{count}} \u0B86\u0BA3\u0BCD\u0B9F\u0BC1\u0B95\u0BB3\u0BBF\u0BB2\u0BCD",ago:"{{count}} \u0B86\u0BA3\u0BCD\u0B9F\u0BC1\u0B95\u0BB3\u0BC1\u0B95\u0BCD\u0B95\u0BC1 \u0BAE\u0BC1\u0BA9\u0BCD\u0BAA\u0BC1"}},overXYears:{one:{default:"1 \u0BB5\u0BB0\u0BC1\u0B9F\u0BA4\u0BCD\u0BA4\u0BBF\u0BB1\u0BCD\u0B95\u0BC1 \u0BAE\u0BC7\u0BB2\u0BCD",in:"1 \u0BB5\u0BB0\u0BC1\u0B9F\u0BA4\u0BCD\u0BA4\u0BBF\u0BB1\u0BCD\u0B95\u0BC1\u0BAE\u0BCD \u0BAE\u0BC7\u0BB2\u0BBE\u0B95",ago:"1 \u0BB5\u0BB0\u0BC1\u0B9F\u0BAE\u0BCD \u0BAE\u0BC1\u0BA9\u0BCD\u0BAA\u0BC1"},other:{default:"{{count}} \u0B86\u0BA3\u0BCD\u0B9F\u0BC1\u0B95\u0BB3\u0BC1\u0B95\u0BCD\u0B95\u0BC1\u0BAE\u0BCD \u0BAE\u0BC7\u0BB2\u0BBE\u0B95",in:"{{count}} \u0B86\u0BA3\u0BCD\u0B9F\u0BC1\u0B95\u0BB3\u0BBF\u0BB2\u0BCD",ago:"{{count}} \u0B86\u0BA3\u0BCD\u0B9F\u0BC1\u0B95\u0BB3\u0BC1\u0B95\u0BCD\u0B95\u0BC1 \u0BAE\u0BC1\u0BA9\u0BCD\u0BAA\u0BC1"}},almostXYears:{one:{default:"\u0B95\u0BBF\u0B9F\u0BCD\u0B9F\u0BA4\u0BCD\u0BA4\u0B9F\u0BCD\u0B9F 1 \u0BB5\u0BB0\u0BC1\u0B9F\u0BAE\u0BCD",in:"\u0B95\u0BBF\u0B9F\u0BCD\u0B9F\u0BA4\u0BCD\u0BA4\u0B9F\u0BCD\u0B9F 1 \u0B86\u0BA3\u0BCD\u0B9F\u0BBF\u0BB2\u0BCD",ago:"\u0B95\u0BBF\u0B9F\u0BCD\u0B9F\u0BA4\u0BCD\u0BA4\u0B9F\u0BCD\u0B9F 1 \u0BB5\u0BB0\u0BC1\u0B9F\u0BAE\u0BCD \u0BAE\u0BC1\u0BA9\u0BCD\u0BAA\u0BC1"},other:{default:"\u0B95\u0BBF\u0B9F\u0BCD\u0B9F\u0BA4\u0BCD\u0BA4\u0B9F\u0BCD\u0B9F {{count}} \u0B86\u0BA3\u0BCD\u0B9F\u0BC1\u0B95\u0BB3\u0BCD",in:"\u0B95\u0BBF\u0B9F\u0BCD\u0B9F\u0BA4\u0BCD\u0BA4\u0B9F\u0BCD\u0B9F {{count}} \u0B86\u0BA3\u0BCD\u0B9F\u0BC1\u0B95\u0BB3\u0BBF\u0BB2\u0BCD",ago:"\u0B95\u0BBF\u0B9F\u0BCD\u0B9F\u0BA4\u0BCD\u0BA4\u0B9F\u0BCD\u0B9F {{count}} \u0B86\u0BA3\u0BCD\u0B9F\u0BC1\u0B95\u0BB3\u0BC1\u0B95\u0BCD\u0B95\u0BC1 \u0BAE\u0BC1\u0BA9\u0BCD\u0BAA\u0BC1"}}},M=function B(C,G,H){var X=H!==null&&H!==void 0&&H.addSuffix?H.comparison&&H.comparison>0?"in":"ago":"default",J=S[C];if(!D(J))return J[X];if(G===1)return J.one[X];else return J.other[X].replace("{{count}}",String(G))};function K(B){return function(){var C=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},G=C.width?String(C.width):B.defaultWidth,H=B.formats[G]||B.formats[B.defaultWidth];return H}}var R={full:"EEEE, d MMMM, y",long:"d MMMM, y",medium:"d MMM, y",short:"d/M/yy"},L={full:"a h:mm:ss zzzz",long:"a h:mm:ss z",medium:"a h:mm:ss",short:"a h:mm"},V={full:"{{date}} {{time}}",long:"{{date}} {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},j={date:K({formats:R,defaultWidth:"full"}),time:K({formats:L,defaultWidth:"full"}),dateTime:K({formats:V,defaultWidth:"full"})},w={lastWeek:"'\u0B95\u0B9F\u0BA8\u0BCD\u0BA4' eeee p '\u0BAE\u0BA3\u0BBF\u0B95\u0BCD\u0B95\u0BC1'",yesterday:"'\u0BA8\u0BC7\u0BB1\u0BCD\u0BB1\u0BC1 ' p '\u0BAE\u0BA3\u0BBF\u0B95\u0BCD\u0B95\u0BC1'",today:"'\u0B87\u0BA9\u0BCD\u0BB1\u0BC1 ' p '\u0BAE\u0BA3\u0BBF\u0B95\u0BCD\u0B95\u0BC1'",tomorrow:"'\u0BA8\u0BBE\u0BB3\u0BC8 ' p '\u0BAE\u0BA3\u0BBF\u0B95\u0BCD\u0B95\u0BC1'",nextWeek:"eeee p '\u0BAE\u0BA3\u0BBF\u0B95\u0BCD\u0B95\u0BC1'",other:"P"},_=function B(C,G,H,X){return w[C]};function O(B){return function(C,G){var H=G!==null&&G!==void 0&&G.context?String(G.context):"standalone",X;if(H==="formatting"&&B.formattingValues){var J=B.defaultFormattingWidth||B.defaultWidth,Y=G!==null&&G!==void 0&&G.width?String(G.width):J;X=B.formattingValues[Y]||B.formattingValues[J]}else{var Z=B.defaultWidth,q=G!==null&&G!==void 0&&G.width?String(G.width):B.defaultWidth;X=B.values[q]||B.values[Z]}var U=B.argumentCallback?B.argumentCallback(C):C;return X[U]}}var f={narrow:["\u0B95\u0BBF.\u0BAE\u0BC1.","\u0B95\u0BBF.\u0BAA\u0BBF."],abbreviated:["\u0B95\u0BBF.\u0BAE\u0BC1.","\u0B95\u0BBF.\u0BAA\u0BBF."],wide:["\u0B95\u0BBF\u0BB1\u0BBF\u0BB8\u0BCD\u0BA4\u0BC1\u0BB5\u0BC1\u0B95\u0BCD\u0B95\u0BC1 \u0BAE\u0BC1\u0BA9\u0BCD","\u0B85\u0BA9\u0BCD\u0BA9\u0BCB \u0B9F\u0BCB\u0BAE\u0BBF\u0BA9\u0BBF"]},F={narrow:["1","2","3","4"],abbreviated:["\u0B95\u0BBE\u0BB2\u0BBE.1","\u0B95\u0BBE\u0BB2\u0BBE.2","\u0B95\u0BBE\u0BB2\u0BBE.3","\u0B95\u0BBE\u0BB2\u0BBE.4"],wide:["\u0B92\u0BA9\u0BCD\u0BB1\u0BBE\u0BAE\u0BCD \u0B95\u0BBE\u0BB2\u0BBE\u0BA3\u0BCD\u0B9F\u0BC1","\u0B87\u0BB0\u0BA3\u0BCD\u0B9F\u0BBE\u0BAE\u0BCD \u0B95\u0BBE\u0BB2\u0BBE\u0BA3\u0BCD\u0B9F\u0BC1","\u0BAE\u0BC2\u0BA9\u0BCD\u0BB1\u0BBE\u0BAE\u0BCD \u0B95\u0BBE\u0BB2\u0BBE\u0BA3\u0BCD\u0B9F\u0BC1","\u0BA8\u0BBE\u0BA9\u0BCD\u0B95\u0BBE\u0BAE\u0BCD \u0B95\u0BBE\u0BB2\u0BBE\u0BA3\u0BCD\u0B9F\u0BC1"]},P={narrow:["\u0B9C","\u0BAA\u0BBF","\u0BAE\u0BBE","\u0B8F","\u0BAE\u0BC7","\u0B9C\u0BC2","\u0B9C\u0BC2","\u0B86","\u0B9A\u0BC6","\u0B85","\u0BA8","\u0B9F\u0BBF"],abbreviated:["\u0B9C\u0BA9.","\u0BAA\u0BBF\u0BAA\u0BCD.","\u0BAE\u0BBE\u0BB0\u0BCD.","\u0B8F\u0BAA\u0BCD.","\u0BAE\u0BC7","\u0B9C\u0BC2\u0BA9\u0BCD","\u0B9C\u0BC2\u0BB2\u0BC8","\u0B86\u0B95.","\u0B9A\u0BC6\u0BAA\u0BCD.","\u0B85\u0B95\u0BCD.","\u0BA8\u0BB5.","\u0B9F\u0BBF\u0B9A."],wide:["\u0B9C\u0BA9\u0BB5\u0BB0\u0BBF","\u0BAA\u0BBF\u0BAA\u0BCD\u0BB0\u0BB5\u0BB0\u0BBF","\u0BAE\u0BBE\u0BB0\u0BCD\u0B9A\u0BCD","\u0B8F\u0BAA\u0BCD\u0BB0\u0BB2\u0BCD","\u0BAE\u0BC7","\u0B9C\u0BC2\u0BA9\u0BCD","\u0B9C\u0BC2\u0BB2\u0BC8","\u0B86\u0B95\u0BB8\u0BCD\u0B9F\u0BCD","\u0B9A\u0BC6\u0BAA\u0BCD\u0B9F\u0BAE\u0BCD\u0BAA\u0BB0\u0BCD","\u0B85\u0B95\u0BCD\u0B9F\u0BCB\u0BAA\u0BB0\u0BCD","\u0BA8\u0BB5\u0BAE\u0BCD\u0BAA\u0BB0\u0BCD","\u0B9F\u0BBF\u0B9A\u0BAE\u0BCD\u0BAA\u0BB0\u0BCD"]},v={narrow:["\u0B9E\u0BBE","\u0BA4\u0BBF","\u0B9A\u0BC6","\u0BAA\u0BC1","\u0BB5\u0BBF","\u0BB5\u0BC6","\u0B9A"],short:["\u0B9E\u0BBE","\u0BA4\u0BBF","\u0B9A\u0BC6","\u0BAA\u0BC1","\u0BB5\u0BBF","\u0BB5\u0BC6","\u0B9A"],abbreviated:["\u0B9E\u0BBE\u0BAF\u0BBF.","\u0BA4\u0BBF\u0B99\u0BCD.","\u0B9A\u0BC6\u0BB5\u0BCD.","\u0BAA\u0BC1\u0BA4.","\u0BB5\u0BBF\u0BAF\u0BBE.","\u0BB5\u0BC6\u0BB3\u0BCD.","\u0B9A\u0BA9\u0BBF"],wide:["\u0B9E\u0BBE\u0BAF\u0BBF\u0BB1\u0BC1","\u0BA4\u0BBF\u0B99\u0BCD\u0B95\u0BB3\u0BCD","\u0B9A\u0BC6\u0BB5\u0BCD\u0BB5\u0BBE\u0BAF\u0BCD","\u0BAA\u0BC1\u0BA4\u0BA9\u0BCD","\u0BB5\u0BBF\u0BAF\u0BBE\u0BB4\u0BA9\u0BCD","\u0BB5\u0BC6\u0BB3\u0BCD\u0BB3\u0BBF","\u0B9A\u0BA9\u0BBF"]},k={narrow:{am:"\u0BAE\u0BC1.\u0BAA",pm:"\u0BAA\u0BBF.\u0BAA",midnight:"\u0BA8\u0BB3\u0BCD.",noon:"\u0BA8\u0BA3\u0BCD.",morning:"\u0B95\u0BBE.",afternoon:"\u0BAE\u0BA4\u0BBF.",evening:"\u0BAE\u0BBE.",night:"\u0B87\u0BB0."},abbreviated:{am:"\u0BAE\u0BC1\u0BB1\u0BCD\u0BAA\u0B95\u0BB2\u0BCD",pm:"\u0BAA\u0BBF\u0BB1\u0BCD\u0BAA\u0B95\u0BB2\u0BCD",midnight:"\u0BA8\u0BB3\u0BCD\u0BB3\u0BBF\u0BB0\u0BB5\u0BC1",noon:"\u0BA8\u0BA3\u0BCD\u0BAA\u0B95\u0BB2\u0BCD",morning:"\u0B95\u0BBE\u0BB2\u0BC8",afternoon:"\u0BAE\u0BA4\u0BBF\u0BAF\u0BAE\u0BCD",evening:"\u0BAE\u0BBE\u0BB2\u0BC8",night:"\u0B87\u0BB0\u0BB5\u0BC1"},wide:{am:"\u0BAE\u0BC1\u0BB1\u0BCD\u0BAA\u0B95\u0BB2\u0BCD",pm:"\u0BAA\u0BBF\u0BB1\u0BCD\u0BAA\u0B95\u0BB2\u0BCD",midnight:"\u0BA8\u0BB3\u0BCD\u0BB3\u0BBF\u0BB0\u0BB5\u0BC1",noon:"\u0BA8\u0BA3\u0BCD\u0BAA\u0B95\u0BB2\u0BCD",morning:"\u0B95\u0BBE\u0BB2\u0BC8",afternoon:"\u0BAE\u0BA4\u0BBF\u0BAF\u0BAE\u0BCD",evening:"\u0BAE\u0BBE\u0BB2\u0BC8",night:"\u0B87\u0BB0\u0BB5\u0BC1"}},b={narrow:{am:"\u0BAE\u0BC1.\u0BAA",pm:"\u0BAA\u0BBF.\u0BAA",midnight:"\u0BA8\u0BB3\u0BCD.",noon:"\u0BA8\u0BA3\u0BCD.",morning:"\u0B95\u0BBE.",afternoon:"\u0BAE\u0BA4\u0BBF.",evening:"\u0BAE\u0BBE.",night:"\u0B87\u0BB0."},abbreviated:{am:"\u0BAE\u0BC1\u0BB1\u0BCD\u0BAA\u0B95\u0BB2\u0BCD",pm:"\u0BAA\u0BBF\u0BB1\u0BCD\u0BAA\u0B95\u0BB2\u0BCD",midnight:"\u0BA8\u0BB3\u0BCD\u0BB3\u0BBF\u0BB0\u0BB5\u0BC1",noon:"\u0BA8\u0BA3\u0BCD\u0BAA\u0B95\u0BB2\u0BCD",morning:"\u0B95\u0BBE\u0BB2\u0BC8",afternoon:"\u0BAE\u0BA4\u0BBF\u0BAF\u0BAE\u0BCD",evening:"\u0BAE\u0BBE\u0BB2\u0BC8",night:"\u0B87\u0BB0\u0BB5\u0BC1"},wide:{am:"\u0BAE\u0BC1\u0BB1\u0BCD\u0BAA\u0B95\u0BB2\u0BCD",pm:"\u0BAA\u0BBF\u0BB1\u0BCD\u0BAA\u0B95\u0BB2\u0BCD",midnight:"\u0BA8\u0BB3\u0BCD\u0BB3\u0BBF\u0BB0\u0BB5\u0BC1",noon:"\u0BA8\u0BA3\u0BCD\u0BAA\u0B95\u0BB2\u0BCD",morning:"\u0B95\u0BBE\u0BB2\u0BC8",afternoon:"\u0BAE\u0BA4\u0BBF\u0BAF\u0BAE\u0BCD",evening:"\u0BAE\u0BBE\u0BB2\u0BC8",night:"\u0B87\u0BB0\u0BB5\u0BC1"}},h=function B(C,G){return String(C)},m={ordinalNumber:h,era:O({values:f,defaultWidth:"wide"}),quarter:O({values:F,defaultWidth:"wide",argumentCallback:function B(C){return C-1}}),month:O({values:P,defaultWidth:"wide"}),day:O({values:v,defaultWidth:"wide"}),dayPeriod:O({values:k,defaultWidth:"wide",formattingValues:b,defaultFormattingWidth:"wide"})};function Q(B){return function(C){var G=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},H=G.width,X=H&&B.matchPatterns[H]||B.matchPatterns[B.defaultMatchWidth],J=C.match(X);if(!J)return null;var Y=J[0],Z=H&&B.parsePatterns[H]||B.parsePatterns[B.defaultParseWidth],q=Array.isArray(Z)?y(Z,function(x){return x.test(Y)}):c(Z,function(x){return x.test(Y)}),U;U=B.valueCallback?B.valueCallback(q):q,U=G.valueCallback?G.valueCallback(U):U;var GB=C.slice(Y.length);return{value:U,rest:GB}}}function c(B,C){for(var G in B)if(Object.prototype.hasOwnProperty.call(B,G)&&C(B[G]))return G;return}function y(B,C){for(var G=0;G<B.length;G++)if(C(B[G]))return G;return}function d(B){return function(C){var G=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},H=C.match(B.matchPattern);if(!H)return null;var X=H[0],J=C.match(B.parsePattern);if(!J)return null;var Y=B.valueCallback?B.valueCallback(J[0]):J[0];Y=G.valueCallback?G.valueCallback(Y):Y;var Z=C.slice(X.length);return{value:Y,rest:Z}}}var g=/^(\d+)(வது)?/i,p=/\d+/i,u={narrow:/^(கி.மு.|கி.பி.)/i,abbreviated:/^(கி\.?\s?மு\.?|கி\.?\s?பி\.?)/,wide:/^(கிறிஸ்துவுக்கு\sமுன்|அன்னோ\sடோமினி)/i},l={any:[/கி\.?\s?மு\.?/,/கி\.?\s?பி\.?/]},i={narrow:/^[1234]/i,abbreviated:/^காலா.[1234]/i,wide:/^(ஒன்றாம்|இரண்டாம்|மூன்றாம்|நான்காம்) காலாண்டு/i},n={narrow:[/1/i,/2/i,/3/i,/4/i],any:[/(1|காலா.1|ஒன்றாம்)/i,/(2|காலா.2|இரண்டாம்)/i,/(3|காலா.3|மூன்றாம்)/i,/(4|காலா.4|நான்காம்)/i]},s={narrow:/^(ஜ|பி|மா|ஏ|மே|ஜூ|ஆ|செ|அ|ந|டி)$/i,abbreviated:/^(ஜன.|பிப்.|மார்.|ஏப்.|மே|ஜூன்|ஜூலை|ஆக.|செப்.|அக்.|நவ.|டிச.)/i,wide:/^(ஜனவரி|பிப்ரவரி|மார்ச்|ஏப்ரல்|மே|ஜூன்|ஜூலை|ஆகஸ்ட்|செப்டம்பர்|அக்டோபர்|நவம்பர்|டிசம்பர்)/i},o={narrow:[/^ஜ$/i,/^பி/i,/^மா/i,/^ஏ/i,/^மே/i,/^ஜூ/i,/^ஜூ/i,/^ஆ/i,/^செ/i,/^அ/i,/^ந/i,/^டி/i],any:[/^ஜன/i,/^பி/i,/^மா/i,/^ஏ/i,/^மே/i,/^ஜூன்/i,/^ஜூலை/i,/^ஆ/i,/^செ/i,/^அ/i,/^ந/i,/^டி/i]},r={narrow:/^(ஞா|தி|செ|பு|வி|வெ|ச)/i,short:/^(ஞா|தி|செ|பு|வி|வெ|ச)/i,abbreviated:/^(ஞாயி.|திங்.|செவ்.|புத.|வியா.|வெள்.|சனி)/i,wide:/^(ஞாயிறு|திங்கள்|செவ்வாய்|புதன்|வியாழன்|வெள்ளி|சனி)/i},e={narrow:[/^ஞா/i,/^தி/i,/^செ/i,/^பு/i,/^வி/i,/^வெ/i,/^ச/i],any:[/^ஞா/i,/^தி/i,/^செ/i,/^பு/i,/^வி/i,/^வெ/i,/^ச/i]},a={narrow:/^(மு.ப|பி.ப|நள்|நண்|காலை|மதியம்|மாலை|இரவு)/i,any:/^(மு.ப|பி.ப|முற்பகல்|பிற்பகல்|நள்ளிரவு|நண்பகல்|காலை|மதியம்|மாலை|இரவு)/i},t={any:{am:/^மு/i,pm:/^பி/i,midnight:/^நள்/i,noon:/^நண்/i,morning:/காலை/i,afternoon:/மதியம்/i,evening:/மாலை/i,night:/இரவு/i}},BB={ordinalNumber:d({matchPattern:g,parsePattern:p,valueCallback:function B(C){return parseInt(C,10)}}),era:Q({matchPatterns:u,defaultMatchWidth:"wide",parsePatterns:l,defaultParseWidth:"any"}),quarter:Q({matchPatterns:i,defaultMatchWidth:"wide",parsePatterns:n,defaultParseWidth:"any",valueCallback:function B(C){return C+1}}),month:Q({matchPatterns:s,defaultMatchWidth:"wide",parsePatterns:o,defaultParseWidth:"any"}),day:Q({matchPatterns:r,defaultMatchWidth:"wide",parsePatterns:e,defaultParseWidth:"any"}),dayPeriod:Q({matchPatterns:a,defaultMatchWidth:"any",parsePatterns:t,defaultParseWidth:"any"})},CB={code:"ta",formatDistance:M,formatLong:j,formatRelative:_,localize:m,match:BB,options:{weekStartsOn:1,firstWeekContainsDate:4}};window.dateFns=T(T({},window.dateFns),{},{locale:T(T({},($=window.dateFns)===null||$===void 0?void 0:$.locale),{},{ta:CB})})})();

//# debugId=576FF273D438B73664756E2164756E21
