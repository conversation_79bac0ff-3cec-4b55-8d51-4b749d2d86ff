var O=function(U){return O=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(A){return typeof A}:function(A){return A&&typeof Symbol=="function"&&A.constructor===Symbol&&A!==Symbol.prototype?"symbol":typeof A},O(U)},W=function(U,A){var H=Object.keys(U);if(Object.getOwnPropertySymbols){var X=Object.getOwnPropertySymbols(U);A&&(X=X.filter(function(x){return Object.getOwnPropertyDescriptor(U,x).enumerable})),H.push.apply(H,X)}return H},K=function(U){for(var A=1;A<arguments.length;A++){var H=arguments[A]!=null?arguments[A]:{};A%2?W(Object(H),!0).forEach(function(X){D0(U,X,H[X])}):Object.getOwnPropertyDescriptors?Object.defineProperties(U,Object.getOwnPropertyDescriptors(H)):W(Object(H)).forEach(function(X){Object.defineProperty(U,X,Object.getOwnPropertyDescriptor(H,X))})}return U},D0=function(U,A,H){if(A=E0(A),A in U)Object.defineProperty(U,A,{value:H,enumerable:!0,configurable:!0,writable:!0});else U[A]=H;return U},E0=function(U){var A=C0(U,"string");return O(A)=="symbol"?A:String(A)},C0=function(U,A){if(O(U)!="object"||!U)return U;var H=U[Symbol.toPrimitive];if(H!==void 0){var X=H.call(U,A||"default");if(O(X)!="object")return X;throw new TypeError("@@toPrimitive must return a primitive value.")}return(A==="string"?String:Number)(U)};(function(U){var A=Object.defineProperty,H=function D(B,E){for(var C in E)A(B,C,{get:E[C],enumerable:!0,configurable:!0,set:function J(G){return E[C]=function(){return G}}})},X={lessThanXSeconds:{past:"{{count}} \u10EC\u10D0\u10DB\u10D6\u10D4 \u10DC\u10D0\u10D9\u10DA\u10D4\u10D1\u10D8 \u10EE\u10DC\u10D8\u10E1 \u10EC\u10D8\u10DC",present:"{{count}} \u10EC\u10D0\u10DB\u10D6\u10D4 \u10DC\u10D0\u10D9\u10DA\u10D4\u10D1\u10D8",future:"{{count}} \u10EC\u10D0\u10DB\u10D6\u10D4 \u10DC\u10D0\u10D9\u10DA\u10D4\u10D1\u10E8\u10D8"},xSeconds:{past:"{{count}} \u10EC\u10D0\u10DB\u10D8\u10E1 \u10EC\u10D8\u10DC",present:"{{count}} \u10EC\u10D0\u10DB\u10D8",future:"{{count}} \u10EC\u10D0\u10DB\u10E8\u10D8"},halfAMinute:{past:"\u10DC\u10D0\u10EE\u10D4\u10D5\u10D0\u10E0\u10D8 \u10EC\u10E3\u10D7\u10D8\u10E1 \u10EC\u10D8\u10DC",present:"\u10DC\u10D0\u10EE\u10D4\u10D5\u10D0\u10E0\u10D8 \u10EC\u10E3\u10D7\u10D8",future:"\u10DC\u10D0\u10EE\u10D4\u10D5\u10D0\u10E0\u10D8 \u10EC\u10E3\u10D7\u10E8\u10D8"},lessThanXMinutes:{past:"{{count}} \u10EC\u10E3\u10D7\u10D6\u10D4 \u10DC\u10D0\u10D9\u10DA\u10D4\u10D1\u10D8 \u10EE\u10DC\u10D8\u10E1 \u10EC\u10D8\u10DC",present:"{{count}} \u10EC\u10E3\u10D7\u10D6\u10D4 \u10DC\u10D0\u10D9\u10DA\u10D4\u10D1\u10D8",future:"{{count}} \u10EC\u10E3\u10D7\u10D6\u10D4 \u10DC\u10D0\u10D9\u10DA\u10D4\u10D1\u10E8\u10D8"},xMinutes:{past:"{{count}} \u10EC\u10E3\u10D7\u10D8\u10E1 \u10EC\u10D8\u10DC",present:"{{count}} \u10EC\u10E3\u10D7\u10D8",future:"{{count}} \u10EC\u10E3\u10D7\u10E8\u10D8"},aboutXHours:{past:"\u10D3\u10D0\u10D0\u10EE\u10DA\u10DD\u10D4\u10D1\u10D8\u10D7 {{count}} \u10E1\u10D0\u10D0\u10D7\u10D8\u10E1 \u10EC\u10D8\u10DC",present:"\u10D3\u10D0\u10D0\u10EE\u10DA\u10DD\u10D4\u10D1\u10D8\u10D7 {{count}} \u10E1\u10D0\u10D0\u10D7\u10D8",future:"\u10D3\u10D0\u10D0\u10EE\u10DA\u10DD\u10D4\u10D1\u10D8\u10D7 {{count}} \u10E1\u10D0\u10D0\u10D7\u10E8\u10D8"},xHours:{past:"{{count}} \u10E1\u10D0\u10D0\u10D7\u10D8\u10E1 \u10EC\u10D8\u10DC",present:"{{count}} \u10E1\u10D0\u10D0\u10D7\u10D8",future:"{{count}} \u10E1\u10D0\u10D0\u10D7\u10E8\u10D8"},xDays:{past:"{{count}} \u10D3\u10E6\u10D8\u10E1 \u10EC\u10D8\u10DC",present:"{{count}} \u10D3\u10E6\u10D4",future:"{{count}} \u10D3\u10E6\u10D4\u10E8\u10D8"},aboutXWeeks:{past:"\u10D3\u10D0\u10D0\u10EE\u10DA\u10DD\u10D4\u10D1\u10D8\u10D7 {{count}} \u10D9\u10D5\u10D8\u10E0\u10D0\u10E1 \u10EC\u10D8\u10DC",present:"\u10D3\u10D0\u10D0\u10EE\u10DA\u10DD\u10D4\u10D1\u10D8\u10D7 {{count}} \u10D9\u10D5\u10D8\u10E0\u10D0",future:"\u10D3\u10D0\u10D0\u10EE\u10DA\u10DD\u10D4\u10D1\u10D8\u10D7 {{count}} \u10D9\u10D5\u10D8\u10E0\u10D0\u10E8\u10D8"},xWeeks:{past:"{{count}} \u10D9\u10D5\u10D8\u10E0\u10D0\u10E1 \u10D9\u10D5\u10D8\u10E0\u10D0",present:"{{count}} \u10D9\u10D5\u10D8\u10E0\u10D0",future:"{{count}} \u10D9\u10D5\u10D8\u10E0\u10D0\u10E8\u10D8"},aboutXMonths:{past:"\u10D3\u10D0\u10D0\u10EE\u10DA\u10DD\u10D4\u10D1\u10D8\u10D7 {{count}} \u10D7\u10D5\u10D8\u10E1 \u10EC\u10D8\u10DC",present:"\u10D3\u10D0\u10D0\u10EE\u10DA\u10DD\u10D4\u10D1\u10D8\u10D7 {{count}} \u10D7\u10D5\u10D4",future:"\u10D3\u10D0\u10D0\u10EE\u10DA\u10DD\u10D4\u10D1\u10D8\u10D7 {{count}} \u10D7\u10D5\u10D4\u10E8\u10D8"},xMonths:{past:"{{count}} \u10D7\u10D5\u10D8\u10E1 \u10EC\u10D8\u10DC",present:"{{count}} \u10D7\u10D5\u10D4",future:"{{count}} \u10D7\u10D5\u10D4\u10E8\u10D8"},aboutXYears:{past:"\u10D3\u10D0\u10D0\u10EE\u10DA\u10DD\u10D4\u10D1\u10D8\u10D7 {{count}} \u10EC\u10DA\u10D8\u10E1 \u10EC\u10D8\u10DC",present:"\u10D3\u10D0\u10D0\u10EE\u10DA\u10DD\u10D4\u10D1\u10D8\u10D7 {{count}} \u10EC\u10D4\u10DA\u10D8",future:"\u10D3\u10D0\u10D0\u10EE\u10DA\u10DD\u10D4\u10D1\u10D8\u10D7 {{count}} \u10EC\u10D4\u10DA\u10E8\u10D8"},xYears:{past:"{{count}} \u10EC\u10DA\u10D8\u10E1 \u10EC\u10D8\u10DC",present:"{{count}} \u10EC\u10D4\u10DA\u10D8",future:"{{count}} \u10EC\u10D4\u10DA\u10E8\u10D8"},overXYears:{past:"{{count}} \u10EC\u10D4\u10DA\u10D6\u10D4 \u10DB\u10D4\u10E2\u10D8 \u10EE\u10DC\u10D8\u10E1 \u10EC\u10D8\u10DC",present:"{{count}} \u10EC\u10D4\u10DA\u10D6\u10D4 \u10DB\u10D4\u10E2\u10D8",future:"{{count}} \u10EC\u10D4\u10DA\u10D6\u10D4 \u10DB\u10D4\u10E2\u10D8 \u10EE\u10DC\u10D8\u10E1 \u10E8\u10D4\u10DB\u10D3\u10D4\u10D2"},almostXYears:{past:"\u10D7\u10D8\u10D7\u10E5\u10DB\u10D8\u10E1 {{count}} \u10EC\u10DA\u10D8\u10E1 \u10EC\u10D8\u10DC",present:"\u10D7\u10D8\u10D7\u10E5\u10DB\u10D8\u10E1 {{count}} \u10EC\u10D4\u10DA\u10D8",future:"\u10D7\u10D8\u10D7\u10E5\u10DB\u10D8\u10E1 {{count}} \u10EC\u10D4\u10DA\u10E8\u10D8"}},x=function D(B,E,C){var J,G=X[B];if(typeof G==="string")J=G;else if(C!==null&&C!==void 0&&C.addSuffix&&C.comparison&&C.comparison>0)J=G.future.replace("{{count}}",String(E));else if(C!==null&&C!==void 0&&C.addSuffix)J=G.past.replace("{{count}}",String(E));else J=G.present.replace("{{count}}",String(E));return J};function N(D){return function(){var B=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},E=B.width?String(B.width):D.defaultWidth,C=D.formats[E]||D.formats[D.defaultWidth];return C}}var $={full:"EEEE, do MMMM, y",long:"do, MMMM, y",medium:"d, MMM, y",short:"dd/MM/yyyy"},M={full:"h:mm:ss a zzzz",long:"h:mm:ss a z",medium:"h:mm:ss a",short:"h:mm a"},S={full:"{{date}} {{time}}'-\u10D6\u10D4'",long:"{{date}} {{time}}'-\u10D6\u10D4'",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},R={date:N({formats:$,defaultWidth:"full"}),time:N({formats:M,defaultWidth:"full"}),dateTime:N({formats:S,defaultWidth:"full"})},L={lastWeek:"'\u10EC\u10D8\u10DC\u10D0' eeee p'-\u10D6\u10D4'",yesterday:"'\u10D2\u10E3\u10E8\u10D8\u10DC' p'-\u10D6\u10D4'",today:"'\u10D3\u10E6\u10D4\u10E1' p'-\u10D6\u10D4'",tomorrow:"'\u10EE\u10D5\u10D0\u10DA' p'-\u10D6\u10D4'",nextWeek:"'\u10E8\u10D4\u10DB\u10D3\u10D4\u10D2\u10D8' eeee p'-\u10D6\u10D4'",other:"P"},f=function D(B,E,C,J){return L[B]};function T(D){return function(B,E){var C=E!==null&&E!==void 0&&E.context?String(E.context):"standalone",J;if(C==="formatting"&&D.formattingValues){var G=D.defaultFormattingWidth||D.defaultWidth,Y=E!==null&&E!==void 0&&E.width?String(E.width):G;J=D.formattingValues[Y]||D.formattingValues[G]}else{var Z=D.defaultWidth,Q=E!==null&&E!==void 0&&E.width?String(E.width):D.defaultWidth;J=D.values[Q]||D.values[Z]}var I=D.argumentCallback?D.argumentCallback(B):B;return J[I]}}var V={narrow:["\u10E9.\u10EC-\u10DB\u10D3\u10D4","\u10E9.\u10EC"],abbreviated:["\u10E9\u10D5.\u10EC-\u10DB\u10D3\u10D4","\u10E9\u10D5.\u10EC"],wide:["\u10E9\u10D5\u10D4\u10DC\u10E1 \u10EC\u10D4\u10DA\u10D7\u10D0\u10E6\u10E0\u10D8\u10EA\u10EE\u10D5\u10D0\u10DB\u10D3\u10D4","\u10E9\u10D5\u10D4\u10DC\u10D8 \u10EC\u10D4\u10DA\u10D7\u10D0\u10E6\u10E0\u10D8\u10EA\u10EE\u10D5\u10D8\u10D7"]},j={narrow:["1","2","3","4"],abbreviated:["1-\u10DA\u10D8 \u10D9\u10D5","2-\u10D4 \u10D9\u10D5","3-\u10D4 \u10D9\u10D5","4-\u10D4 \u10D9\u10D5"],wide:["1-\u10DA\u10D8 \u10D9\u10D5\u10D0\u10E0\u10E2\u10D0\u10DA\u10D8","2-\u10D4 \u10D9\u10D5\u10D0\u10E0\u10E2\u10D0\u10DA\u10D8","3-\u10D4 \u10D9\u10D5\u10D0\u10E0\u10E2\u10D0\u10DA\u10D8","4-\u10D4 \u10D9\u10D5\u10D0\u10E0\u10E2\u10D0\u10DA\u10D8"]},v={narrow:["\u10D8\u10D0","\u10D7\u10D4","\u10DB\u10D0","\u10D0\u10DE","\u10DB\u10E1","\u10D5\u10DC","\u10D5\u10DA","\u10D0\u10D2","\u10E1\u10D4","\u10DD\u10E5","\u10DC\u10DD","\u10D3\u10D4"],abbreviated:["\u10D8\u10D0\u10DC","\u10D7\u10D4\u10D1","\u10DB\u10D0\u10E0","\u10D0\u10DE\u10E0","\u10DB\u10D0\u10D8","\u10D8\u10D5\u10DC","\u10D8\u10D5\u10DA","\u10D0\u10D2\u10D5","\u10E1\u10D4\u10E5","\u10DD\u10E5\u10E2","\u10DC\u10DD\u10D4","\u10D3\u10D4\u10D9"],wide:["\u10D8\u10D0\u10DC\u10D5\u10D0\u10E0\u10D8","\u10D7\u10D4\u10D1\u10D4\u10E0\u10D5\u10D0\u10DA\u10D8","\u10DB\u10D0\u10E0\u10E2\u10D8","\u10D0\u10DE\u10E0\u10D8\u10DA\u10D8","\u10DB\u10D0\u10D8\u10E1\u10D8","\u10D8\u10D5\u10DC\u10D8\u10E1\u10D8","\u10D8\u10D5\u10DA\u10D8\u10E1\u10D8","\u10D0\u10D2\u10D5\u10D8\u10E1\u10E2\u10DD","\u10E1\u10D4\u10E5\u10E2\u10D4\u10DB\u10D1\u10D4\u10E0\u10D8","\u10DD\u10E5\u10E2\u10DD\u10DB\u10D1\u10D4\u10E0\u10D8","\u10DC\u10DD\u10D4\u10DB\u10D1\u10D4\u10E0\u10D8","\u10D3\u10D4\u10D9\u10D4\u10DB\u10D1\u10D4\u10E0\u10D8"]},w={narrow:["\u10D9\u10D5","\u10DD\u10E0","\u10E1\u10D0","\u10DD\u10D7","\u10EE\u10E3","\u10DE\u10D0","\u10E8\u10D0"],short:["\u10D9\u10D5\u10D8","\u10DD\u10E0\u10E8","\u10E1\u10D0\u10DB","\u10DD\u10D7\u10EE","\u10EE\u10E3\u10D7","\u10DE\u10D0\u10E0","\u10E8\u10D0\u10D1"],abbreviated:["\u10D9\u10D5\u10D8","\u10DD\u10E0\u10E8","\u10E1\u10D0\u10DB","\u10DD\u10D7\u10EE","\u10EE\u10E3\u10D7","\u10DE\u10D0\u10E0","\u10E8\u10D0\u10D1"],wide:["\u10D9\u10D5\u10D8\u10E0\u10D0","\u10DD\u10E0\u10E8\u10D0\u10D1\u10D0\u10D7\u10D8","\u10E1\u10D0\u10DB\u10E8\u10D0\u10D1\u10D0\u10D7\u10D8","\u10DD\u10D7\u10EE\u10E8\u10D0\u10D1\u10D0\u10D7\u10D8","\u10EE\u10E3\u10D7\u10E8\u10D0\u10D1\u10D0\u10D7\u10D8","\u10DE\u10D0\u10E0\u10D0\u10E1\u10D9\u10D4\u10D5\u10D8","\u10E8\u10D0\u10D1\u10D0\u10D7\u10D8"]},_={narrow:{am:"a",pm:"p",midnight:"\u10E8\u10E3\u10D0\u10E6\u10D0\u10DB\u10D4",noon:"\u10E8\u10E3\u10D0\u10D3\u10E6\u10D4",morning:"\u10D3\u10D8\u10DA\u10D0",afternoon:"\u10E1\u10D0\u10E6\u10D0\u10DB\u10DD",evening:"\u10E1\u10D0\u10E6\u10D0\u10DB\u10DD",night:"\u10E6\u10D0\u10DB\u10D4"},abbreviated:{am:"AM",pm:"PM",midnight:"\u10E8\u10E3\u10D0\u10E6\u10D0\u10DB\u10D4",noon:"\u10E8\u10E3\u10D0\u10D3\u10E6\u10D4",morning:"\u10D3\u10D8\u10DA\u10D0",afternoon:"\u10E1\u10D0\u10E6\u10D0\u10DB\u10DD",evening:"\u10E1\u10D0\u10E6\u10D0\u10DB\u10DD",night:"\u10E6\u10D0\u10DB\u10D4"},wide:{am:"a.m.",pm:"p.m.",midnight:"\u10E8\u10E3\u10D0\u10E6\u10D0\u10DB\u10D4",noon:"\u10E8\u10E3\u10D0\u10D3\u10E6\u10D4",morning:"\u10D3\u10D8\u10DA\u10D0",afternoon:"\u10E1\u10D0\u10E6\u10D0\u10DB\u10DD",evening:"\u10E1\u10D0\u10E6\u10D0\u10DB\u10DD",night:"\u10E6\u10D0\u10DB\u10D4"}},P={narrow:{am:"a",pm:"p",midnight:"\u10E8\u10E3\u10D0\u10E6\u10D0\u10DB\u10D8\u10D7",noon:"\u10E8\u10E3\u10D0\u10D3\u10E6\u10D8\u10E1\u10D0\u10E1",morning:"\u10D3\u10D8\u10DA\u10D8\u10D7",afternoon:"\u10DC\u10D0\u10E8\u10E3\u10D0\u10D3\u10E6\u10D4\u10D5\u10E1",evening:"\u10E1\u10D0\u10E6\u10D0\u10DB\u10DD\u10E1",night:"\u10E6\u10D0\u10DB\u10D8\u10D7"},abbreviated:{am:"AM",pm:"PM",midnight:"\u10E8\u10E3\u10D0\u10E6\u10D0\u10DB\u10D8\u10D7",noon:"\u10E8\u10E3\u10D0\u10D3\u10E6\u10D8\u10E1\u10D0\u10E1",morning:"\u10D3\u10D8\u10DA\u10D8\u10D7",afternoon:"\u10DC\u10D0\u10E8\u10E3\u10D0\u10D3\u10E6\u10D4\u10D5\u10E1",evening:"\u10E1\u10D0\u10E6\u10D0\u10DB\u10DD\u10E1",night:"\u10E6\u10D0\u10DB\u10D8\u10D7"},wide:{am:"a.m.",pm:"p.m.",midnight:"\u10E8\u10E3\u10D0\u10E6\u10D0\u10DB\u10D8\u10D7",noon:"\u10E8\u10E3\u10D0\u10D3\u10E6\u10D8\u10E1\u10D0\u10E1",morning:"\u10D3\u10D8\u10DA\u10D8\u10D7",afternoon:"\u10DC\u10D0\u10E8\u10E3\u10D0\u10D3\u10E6\u10D4\u10D5\u10E1",evening:"\u10E1\u10D0\u10E6\u10D0\u10DB\u10DD\u10E1",night:"\u10E6\u10D0\u10DB\u10D8\u10D7"}},F=function D(B){var E=Number(B);if(E===1)return E+"-\u10DA\u10D8";return E+"-\u10D4"},b={ordinalNumber:F,era:T({values:V,defaultWidth:"wide"}),quarter:T({values:j,defaultWidth:"wide",argumentCallback:function D(B){return B-1}}),month:T({values:v,defaultWidth:"wide"}),day:T({values:w,defaultWidth:"wide"}),dayPeriod:T({values:_,defaultWidth:"wide",formattingValues:P,defaultFormattingWidth:"wide"})};function q(D){return function(B){var E=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},C=E.width,J=C&&D.matchPatterns[C]||D.matchPatterns[D.defaultMatchWidth],G=B.match(J);if(!G)return null;var Y=G[0],Z=C&&D.parsePatterns[C]||D.parsePatterns[D.defaultParseWidth],Q=Array.isArray(Z)?u(Z,function(z){return z.test(Y)}):h(Z,function(z){return z.test(Y)}),I;I=D.valueCallback?D.valueCallback(Q):Q,I=E.valueCallback?E.valueCallback(I):I;var t=B.slice(Y.length);return{value:I,rest:t}}}var h=function D(B,E){for(var C in B)if(Object.prototype.hasOwnProperty.call(B,C)&&E(B[C]))return C;return},u=function D(B,E){for(var C=0;C<B.length;C++)if(E(B[C]))return C;return};function k(D){return function(B){var E=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},C=B.match(D.matchPattern);if(!C)return null;var J=C[0],G=B.match(D.parsePattern);if(!G)return null;var Y=D.valueCallback?D.valueCallback(G[0]):G[0];Y=E.valueCallback?E.valueCallback(Y):Y;var Z=B.slice(J.length);return{value:Y,rest:Z}}}var m=/^(\d+)(-ლი|-ე)?/i,c=/\d+/i,y={narrow:/^(ჩვ?\.წ)/i,abbreviated:/^(ჩვ?\.წ)/i,wide:/^(ჩვენს წელთაღრიცხვამდე|ქრისტეშობამდე|ჩვენი წელთაღრიცხვით|ქრისტეშობიდან)/i},p={any:[/^(ჩვენს წელთაღრიცხვამდე|ქრისტეშობამდე)/i,/^(ჩვენი წელთაღრიცხვით|ქრისტეშობიდან)/i]},g={narrow:/^[1234]/i,abbreviated:/^[1234]-(ლი|ე)? კვ/i,wide:/^[1234]-(ლი|ე)? კვარტალი/i},d={any:[/1/i,/2/i,/3/i,/4/i]},l={any:/^(ია|თე|მა|აპ|მს|ვნ|ვლ|აგ|სე|ოქ|ნო|დე)/i},i={any:[/^ია/i,/^თ/i,/^მარ/i,/^აპ/i,/^მაი/i,/^ი?ვნ/i,/^ი?ვლ/i,/^აგ/i,/^ს/i,/^ო/i,/^ნ/i,/^დ/i]},n={narrow:/^(კვ|ორ|სა|ოთ|ხუ|პა|შა)/i,short:/^(კვი|ორშ|სამ|ოთხ|ხუთ|პარ|შაბ)/i,wide:/^(კვირა|ორშაბათი|სამშაბათი|ოთხშაბათი|ხუთშაბათი|პარასკევი|შაბათი)/i},s={any:[/^კვ/i,/^ორ/i,/^სა/i,/^ოთ/i,/^ხუ/i,/^პა/i,/^შა/i]},o={any:/^([ap]\.?\s?m\.?|შუაღ|დილ)/i},r={any:{am:/^a/i,pm:/^p/i,midnight:/^შუაღ/i,noon:/^შუადღ/i,morning:/^დილ/i,afternoon:/ნაშუადღევს/i,evening:/საღამო/i,night:/ღამ/i}},a={ordinalNumber:k({matchPattern:m,parsePattern:c,valueCallback:function D(B){return parseInt(B,10)}}),era:q({matchPatterns:y,defaultMatchWidth:"wide",parsePatterns:p,defaultParseWidth:"any"}),quarter:q({matchPatterns:g,defaultMatchWidth:"wide",parsePatterns:d,defaultParseWidth:"any",valueCallback:function D(B){return B+1}}),month:q({matchPatterns:l,defaultMatchWidth:"any",parsePatterns:i,defaultParseWidth:"any"}),day:q({matchPatterns:n,defaultMatchWidth:"wide",parsePatterns:s,defaultParseWidth:"any"}),dayPeriod:q({matchPatterns:o,defaultMatchWidth:"any",parsePatterns:r,defaultParseWidth:"any"})},e={code:"ka",formatDistance:x,formatLong:R,formatRelative:f,localize:b,match:a,options:{weekStartsOn:1,firstWeekContainsDate:1}};window.dateFns=K(K({},window.dateFns),{},{locale:K(K({},(U=window.dateFns)===null||U===void 0?void 0:U.locale),{},{ka:e})})})();

//# debugId=A7FF9C4DD7ECC27764756e2164756e21
