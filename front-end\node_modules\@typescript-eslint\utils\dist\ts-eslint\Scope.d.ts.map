{"version": 3, "file": "Scope.d.ts", "sourceRoot": "", "sources": ["../../src/ts-eslint/Scope.ts"], "names": [], "mappings": "AAEA,OAAO,KAAK,YAAY,MAAM,kCAAkC,CAAC;AAEjE,kBAAU,KAAK,CAAC;IACd,KAAY,YAAY,GAAG,YAAY,CAAC,YAAY,CAAC;IACrD,KAAY,SAAS,GAAG,YAAY,CAAC,SAAS,CAAC;IAC/C,KAAY,QAAQ,GAAG,YAAY,CAAC,aAAa,CAAC;IAClD,KAAY,KAAK,GAAG,YAAY,CAAC,KAAK,CAAC;IAChC,MAAM,SAAS,+BAAyB,CAAC;IAEhD,KAAY,cAAc,GAAG,YAAY,CAAC,UAAU,CAAC;IACrD,KAAY,UAAU,GAAG,YAAY,CAAC,UAAU,CAAC;IAC1C,MAAM,cAAc,oCAA8B,CAAC;IAE1D,UAAiB,WAAW,CAAC;QAC3B,KAAY,qBAAqB,GAAG,YAAY,CAAC,qBAAqB,CAAC;QACvE,KAAY,mBAAmB,GAAG,YAAY,CAAC,mBAAmB,CAAC;QACnE,KAAY,sBAAsB,GAAG,YAAY,CAAC,sBAAsB,CAAC;QACzE,KAAY,gCAAgC,GAC1C,YAAY,CAAC,gCAAgC,CAAC;QAChD,KAAY,uBAAuB,GAAG,YAAY,CAAC,uBAAuB,CAAC;QAC3E,KAAY,mBAAmB,GAAG,YAAY,CAAC,mBAAmB,CAAC;QACnE,KAAY,sBAAsB,GAAG,YAAY,CAAC,sBAAsB,CAAC;QACzE,KAAY,oBAAoB,GAAG,YAAY,CAAC,oBAAoB,CAAC;QACrE,KAAY,sBAAsB,GAAG,YAAY,CAAC,sBAAsB,CAAC;QACzE,KAAY,cAAc,GAAG,YAAY,CAAC,cAAc,CAAC;QACzD,KAAY,kBAAkB,GAAG,YAAY,CAAC,kBAAkB,CAAC;KAClE;IACD,UAAiB,MAAM,CAAC;QACtB,KAAY,UAAU,GAAG,YAAY,CAAC,UAAU,CAAC;QACjD,KAAY,UAAU,GAAG,YAAY,CAAC,UAAU,CAAC;QACjD,KAAY,UAAU,GAAG,YAAY,CAAC,UAAU,CAAC;QACjD,KAAY,oBAAoB,GAAG,YAAY,CAAC,oBAAoB,CAAC;QACrE,KAAY,QAAQ,GAAG,YAAY,CAAC,QAAQ,CAAC;QAC7C,KAAY,2BAA2B,GACrC,YAAY,CAAC,2BAA2B,CAAC;QAC3C,KAAY,aAAa,GAAG,YAAY,CAAC,aAAa,CAAC;QACvD,KAAY,iBAAiB,GAAG,YAAY,CAAC,iBAAiB,CAAC;QAC/D,KAAY,WAAW,GAAG,YAAY,CAAC,WAAW,CAAC;QACnD,KAAY,eAAe,GAAG,YAAY,CAAC,eAAe,CAAC;QAC3D,KAAY,WAAW,GAAG,YAAY,CAAC,WAAW,CAAC;QACnD,KAAY,WAAW,GAAG,YAAY,CAAC,WAAW,CAAC;QACnD,KAAY,WAAW,GAAG,YAAY,CAAC,WAAW,CAAC;QACnD,KAAY,aAAa,GAAG,YAAY,CAAC,aAAa,CAAC;QACvD,KAAY,SAAS,GAAG,YAAY,CAAC,SAAS,CAAC;QAC/C,KAAY,SAAS,GAAG,YAAY,CAAC,SAAS,CAAC;KAChD;CACF;AAED,OAAO,EAAE,KAAK,EAAE,CAAC"}