{"version": 3, "file": "unified-signatures.js", "sourceRoot": "", "sources": ["../../src/rules/unified-signatures.ts"], "names": [], "mappings": ";;AACA,oDAA0D;AAG1D,kCAAiE;AA4DjE,kBAAe,IAAA,iBAAU,EAAsB;IAC7C,IAAI,EAAE,oBAAoB;IAC1B,IAAI,EAAE;QACJ,IAAI,EAAE;YACJ,WAAW,EACT,kGAAkG;YACpG,oCAAoC;YACpC,WAAW,EAAE,QAAQ;SACtB;QACD,IAAI,EAAE,YAAY;QAClB,QAAQ,EAAE;YACR,qBAAqB,EAAE,+CAA+C;YACtE,uBAAuB,EACrB,oDAAoD;YACtD,yBAAyB,EACvB,wDAAwD;SAC3D;QACD,MAAM,EAAE;YACN;gBACE,oBAAoB,EAAE,KAAK;gBAC3B,UAAU,EAAE;oBACV,gCAAgC,EAAE;wBAChC,WAAW,EACT,gIAAgI;wBAClI,IAAI,EAAE,SAAS;qBAChB;iBACF;gBACD,IAAI,EAAE,QAAQ;aACf;SACF;KACF;IACD,cAAc,EAAE;QACd;YACE,gCAAgC,EAAE,KAAK;SACxC;KACF;IACD,MAAM,CAAC,OAAO,EAAE,CAAC,EAAE,gCAAgC,EAAE,CAAC;QACpD,wEAAwE;QACxE,UAAU;QACV,wEAAwE;QAExE,SAAS,kBAAkB,CAAC,SAAkB;YAC5C,wEAAwE;YACxE,MAAM,SAAS,GACb,SAAS,KAAK,SAAS;gBACrB,CAAC,CAAC,iBAAiB;gBACnB,CAAC,CAAC,qCAAqC,SAAS,EAAE,CAAC;YACvD,OAAO,GAAG,SAAS,qCAAqC,CAAC;QAC3D,CAAC;QAED,SAAS,WAAW,CAAC,QAAmB;YACtC,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE,CAAC;gBAC/B,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,OAAO,CAAC;gBACjC,QAAQ,KAAK,CAAC,IAAI,EAAE,CAAC;oBACnB,KAAK,6BAA6B,CAAC,CAAC,CAAC;wBACnC,MAAM,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,KAAK,CAAC;wBACzB,MAAM,mBAAmB,GAAG,KAAK,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC;wBAElE,MAAM,eAAe,GAAG,qBAAqB,CAAC,EAAE,CAAC;4BAC/C,CAAC,CAAC,EAAE,CAAC,SAAS,CAAC,cAAc;4BAC7B,CAAC,CAAC,EAAE,CAAC,cAAc,CAAC;wBACtB,MAAM,eAAe,GAAG,qBAAqB,CAAC,EAAE,CAAC;4BAC/C,CAAC,CAAC,EAAE,CAAC,SAAS,CAAC,cAAc;4BAC7B,CAAC,CAAC,EAAE,CAAC,cAAc,CAAC;wBAEtB,OAAO,CAAC,MAAM,CAAC;4BACb,GAAG,EAAE,EAAE,CAAC,GAAG;4BACX,SAAS,EAAE,2BAA2B;4BACtC,IAAI,EAAE;gCACJ,kBAAkB,EAAE,kBAAkB,CAAC,mBAAmB,CAAC;gCAC3D,KAAK,EAAE,OAAO,CAAC,UAAU,CAAC,OAAO,CAC/B,eAAe,EAAE,cAAc,CAChC;gCACD,KAAK,EAAE,OAAO,CAAC,UAAU,CAAC,OAAO,CAC/B,eAAe,EAAE,cAAc,CAChC;6BACF;4BACD,IAAI,EAAE,EAAE;yBACT,CAAC,CAAC;wBACH,MAAM;oBACR,CAAC;oBACD,KAAK,iBAAiB,CAAC,CAAC,CAAC;wBACvB,MAAM,EAAE,cAAc,EAAE,cAAc,EAAE,GAAG,KAAK,CAAC;wBACjD,MAAM,mBAAmB,GAAG,KAAK;4BAC/B,CAAC,CAAC,SAAS;4BACX,CAAC,CAAC,cAAc,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC;wBAElC,OAAO,CAAC,MAAM,CAAC;4BACb,GAAG,EAAE,cAAc,CAAC,GAAG;4BACvB,SAAS,EACP,cAAc,CAAC,IAAI,KAAK,sBAAc,CAAC,WAAW;gCAChD,CAAC,CAAC,uBAAuB;gCACzB,CAAC,CAAC,yBAAyB;4BAC/B,IAAI,EAAE;gCACJ,kBAAkB,EAAE,kBAAkB,CAAC,mBAAmB,CAAC;6BAC5D;4BACD,IAAI,EAAE,cAAc;yBACrB,CAAC,CAAC;oBACL,CAAC;gBACH,CAAC;YACH,CAAC;QACH,CAAC;QAED,SAAS,cAAc,CACrB,UAAqC,EACrC,cAAoD;YAEpD,MAAM,MAAM,GAAc,EAAE,CAAC;YAC7B,MAAM,eAAe,GAAG,kBAAkB,CAAC,cAAc,CAAC,CAAC;YAC3D,KAAK,MAAM,SAAS,IAAI,UAAU,EAAE,CAAC;gBACnC,WAAW,CAAC,SAAS,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;oBAC9B,MAAM,UAAU,GAAI,CAA+B,CAAC,KAAK,IAAI,CAAC,CAAC;oBAC/D,MAAM,UAAU,GAAI,CAA+B,CAAC,KAAK,IAAI,CAAC,CAAC;oBAE/D,MAAM,KAAK,GAAG,iBAAiB,CAC7B,UAAiC,EACjC,UAAiC,EACjC,eAAe,CAChB,CAAC;oBACF,IAAI,KAAK,KAAK,SAAS,EAAE,CAAC;wBACxB,MAAM,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC,CAAC;oBACxD,CAAC;gBACH,CAAC,CAAC,CAAC;YACL,CAAC;YACD,OAAO,MAAM,CAAC;QAChB,CAAC;QAED,SAAS,iBAAiB,CACxB,CAAsB,EACtB,CAAsB,EACtB,eAAgC;YAEhC,IAAI,CAAC,sBAAsB,CAAC,CAAC,EAAE,CAAC,EAAE,eAAe,CAAC,EAAE,CAAC;gBACnD,OAAO,SAAS,CAAC;YACnB,CAAC;YAED,OAAO,CAAC,CAAC,MAAM,CAAC,MAAM,KAAK,CAAC,CAAC,MAAM,CAAC,MAAM;gBACxC,CAAC,CAAC,iCAAiC,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,MAAM,CAAC;gBACvD,CAAC,CAAC,yCAAyC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACtD,CAAC;QAED,SAAS,sBAAsB,CAC7B,CAAsB,EACtB,CAAsB,EACtB,eAAgC;YAEhC,6BAA6B;YAE7B,MAAM,WAAW,GACf,CAAC,CAAC,cAAc,KAAK,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC;YACvE,MAAM,WAAW,GACf,CAAC,CAAC,cAAc,KAAK,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC;YAEvE,IAAI,gCAAgC,EAAE,CAAC;gBACrC,MAAM,kBAAkB,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;gBACtE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,kBAAkB,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;oBAC/C,IACE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI;wBACrC,sBAAsB,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;4BACjC,sBAAsB,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,EACrC,CAAC;wBACD,OAAO,KAAK,CAAC;oBACf,CAAC;gBACH,CAAC;YACH,CAAC;YAED,OAAO,CACL,aAAa,CAAC,CAAC,CAAC,UAAU,EAAE,CAAC,CAAC,UAAU,CAAC;gBACzC,sCAAsC;gBACtC,+FAA+F;gBAC/F,IAAA,qBAAc,EAAC,WAAW,EAAE,WAAW,EAAE,sBAAsB,CAAC;gBAChE,0BAA0B,CAAC,CAAC,EAAE,eAAe,CAAC;oBAC5C,0BAA0B,CAAC,CAAC,EAAE,eAAe,CAAC,CACjD,CAAC;QACJ,CAAC;QAED,4FAA4F;QAC5F,SAAS,iCAAiC,CACxC,MAAqC,EACrC,MAAqC;YAErC,MAAM,KAAK,GAAG,yBAAyB,CACrC,MAAM,EACN,MAAM,EACN,kBAAkB,CACnB,CAAC;YACF,IAAI,KAAK,KAAK,SAAS,EAAE,CAAC;gBACxB,OAAO,SAAS,CAAC;YACnB,CAAC;YAED,kFAAkF;YAClF,IACE,CAAC,IAAA,qBAAc,EACb,MAAM,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,EACvB,MAAM,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,EACvB,kBAAkB,CACnB,EACD,CAAC;gBACD,OAAO,SAAS,CAAC;YACnB,CAAC;YAED,MAAM,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;YACxB,MAAM,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;YACxB,oGAAoG;YACpG,0DAA0D;YAC1D,OAAO,yBAAyB,CAAC,CAAC,EAAE,CAAC,CAAC;gBACpC,CAAC,CAAC,IAAI,KAAK,sBAAc,CAAC,WAAW;gBACrC,CAAC,CAAC,EAAE,IAAI,EAAE,6BAA6B,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE;gBACvD,CAAC,CAAC,SAAS,CAAC;QAChB,CAAC;QAED;;;WAGG;QACH,SAAS,yCAAyC,CAChD,CAAsB,EACtB,CAAsB;YAEtB,MAAM,IAAI,GAAG,CAAC,CAAC,MAAM,CAAC;YACtB,MAAM,IAAI,GAAG,CAAC,CAAC,MAAM,CAAC;YAEtB,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;YACrD,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC;YACvD,MAAM,OAAO,GAAG,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC;YACxD,MAAM,UAAU,GAAG,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAErD,mFAAmF;YACnF,sEAAsE;YACtE,+BAA+B;YAC/B,KAAK,IAAI,CAAC,GAAG,SAAS,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;gBACnD,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;oBACtC,OAAO,SAAS,CAAC;gBACnB,CAAC;YACH,CAAC;YAED,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,EAAE,CAAC,EAAE,EAAE,CAAC;gBACnC,MAAM,KAAK,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;gBACtB,MAAM,KAAK,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;gBACtB,MAAM,eAAe,GAAG,qBAAqB,CAAC,KAAK,CAAC;oBAClD,CAAC,CAAC,KAAK,CAAC,SAAS,CAAC,cAAc;oBAChC,CAAC,CAAC,KAAK,CAAC,cAAc,CAAC;gBACzB,MAAM,eAAe,GAAG,qBAAqB,CAAC,KAAK,CAAC;oBAClD,CAAC,CAAC,KAAK,CAAC,SAAS,CAAC,cAAc;oBAChC,CAAC,CAAC,KAAK,CAAC,cAAc,CAAC;gBAEzB,IAAI,CAAC,aAAa,CAAC,eAAe,EAAE,eAAe,CAAC,EAAE,CAAC;oBACrD,OAAO,SAAS,CAAC;gBACnB,CAAC;YACH,CAAC;YAED,IACE,SAAS,GAAG,CAAC;gBACb,OAAO,CAAC,SAAS,GAAG,CAAC,CAAC,CAAC,IAAI,KAAK,sBAAc,CAAC,WAAW,EAC1D,CAAC;gBACD,OAAO,SAAS,CAAC;YACnB,CAAC;YAED,OAAO;gBACL,cAAc,EAAE,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC;gBACzC,IAAI,EAAE,iBAAiB;gBACvB,cAAc,EAAE,UAAU;aAC3B,CAAC;QACJ,CAAC;QAED,mGAAmG;QACnG,SAAS,kBAAkB,CACzB,cAAoD;YAEpD,IAAI,cAAc,KAAK,SAAS,EAAE,CAAC;gBACjC,OAAO,CAAC,GAAG,EAAE,CAAC,KAAK,CAAoB,CAAC;YAC1C,CAAC;YAED,MAAM,GAAG,GAAG,IAAI,GAAG,EAAU,CAAC;YAC9B,KAAK,MAAM,CAAC,IAAI,cAAc,CAAC,MAAM,EAAE,CAAC;gBACtC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACvB,CAAC;YACD,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAoB,CAAC;QAC5D,CAAC;QAED,wEAAwE;QACxE,SAAS,0BAA0B,CACjC,GAAwB,EACxB,eAAgC;YAEhC,OAAO,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAqB,EAAE,EAAE,CAC/C,yBAAyB,CACvB,qBAAqB,CAAC,CAAC,CAAC;gBACtB,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,cAAc;gBAC5B,CAAC,CAAC,CAAC,CAAC,cAAc,CACrB,CACF,CAAC;YAEF,SAAS,yBAAyB,CAChC,IAAoD;gBAEpD,IAAI,CAAC,IAAI,EAAE,CAAC;oBACV,OAAO,KAAK,CAAC;gBACf,CAAC;gBAED,IAAI,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,eAAe,EAAE,CAAC;oBACjD,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;oBAC/B,IAAI,YAAY,CAAC,QAAQ,CAAC,IAAI,eAAe,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;wBAC7D,OAAO,IAAI,CAAC;oBACd,CAAC;gBACH,CAAC;gBAED,OAAO,yBAAyB,CAC7B,IAA2C,CAAC,cAAc;oBACxD,IAA6B,CAAC,WAAW,CAC7C,CAAC;YACJ,CAAC;QACH,CAAC;QAED,SAAS,qBAAqB,CAC5B,IAAmB;YAEnB,OAAO,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,mBAAmB,CAAC;QAC1D,CAAC;QAED,SAAS,kBAAkB,CACzB,CAAqB,EACrB,CAAqB;YAErB,MAAM,eAAe,GAAG,qBAAqB,CAAC,CAAC,CAAC;gBAC9C,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,cAAc;gBAC5B,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC;YACrB,MAAM,eAAe,GAAG,qBAAqB,CAAC,CAAC,CAAC;gBAC9C,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,cAAc;gBAC5B,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC;YAErB,OAAO,CACL,yBAAyB,CAAC,CAAC,EAAE,CAAC,CAAC;gBAC/B,aAAa,CAAC,eAAe,EAAE,eAAe,CAAC,CAChD,CAAC;QACJ,CAAC;QAED,yCAAyC;QACzC,SAAS,qBAAqB,CAAC,CAAqB;YAClD,MAAM,QAAQ,GAAG,qBAAqB,CAAC,CAAC,CAAC;gBACvC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,QAAQ;gBACtB,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC;YAEf,OAAO,CAAC,CAAC,IAAI,KAAK,sBAAc,CAAC,WAAW,IAAI,QAAQ,CAAC;QAC3D,CAAC;QAED,oGAAoG;QACpG,SAAS,yBAAyB,CAChC,CAAqB,EACrB,CAAqB;YAErB,MAAM,SAAS,GAAG,qBAAqB,CAAC,CAAC,CAAC;gBACxC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,QAAQ;gBACtB,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC;YACf,MAAM,SAAS,GAAG,qBAAqB,CAAC,CAAC,CAAC;gBACxC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,QAAQ;gBACtB,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC;YAEf,OAAO,CACL,CAAC,CAAC,CAAC,IAAI,KAAK,sBAAc,CAAC,WAAW,CAAC;gBACrC,CAAC,CAAC,CAAC,IAAI,KAAK,sBAAc,CAAC,WAAW,CAAC,IAAI,SAAS,KAAK,SAAS,CACrE,CAAC;QACJ,CAAC;QAED,SAAS,sBAAsB,CAC7B,CAA2B,EAC3B,CAA2B;YAE3B,OAAO,CACL,CAAC,CAAC,IAAI,CAAC,IAAI,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI;gBAC3B,mBAAmB,CAAC,CAAC,CAAC,UAAU,EAAE,CAAC,CAAC,UAAU,CAAC,CAChD,CAAC;QACJ,CAAC;QAED,SAAS,aAAa,CACpB,CAAwC,EACxC,CAAwC;YAExC,OAAO,CACL,CAAC,KAAK,CAAC;gBACP,CAAC,CAAC,KAAK,SAAS;oBACd,CAAC,KAAK,SAAS;oBACf,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,cAAc,CAAC;wBAC1C,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAClD,CAAC;QACJ,CAAC;QAED,SAAS,mBAAmB,CAC1B,CAAgC,EAChC,CAAgC;YAEhC,OAAO,CACL,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,KAAK,SAAS,IAAI,CAAC,KAAK,SAAS,IAAI,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,IAAI,CAAC,CACrE,CAAC;QACJ,CAAC;QAED,uDAAuD;QACvD,SAAS,yBAAyB,CAChC,CAAe,EACf,CAAe,EACf,KAAe;YAEf,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,MAAM,IAAI,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;gBAClD,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;oBACvB,OAAO,CAAC,CAAC;gBACX,CAAC;YACH,CAAC;YACD,OAAO,SAAS,CAAC;QACnB,CAAC;QAED,2DAA2D;QAC3D,SAAS,WAAW,CAClB,MAAoB,EACpB,MAA4B;YAE5B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;gBACvC,KAAK,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;oBAC3C,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC/B,CAAC;YACH,CAAC;QACH,CAAC;QAQD,MAAM,MAAM,GAAY,EAAE,CAAC;QAC3B,IAAI,YAAY,GAAsB;YACpC,SAAS,EAAE,IAAI,GAAG,EAA0B;SAC7C,CAAC;QAEF,SAAS,WAAW,CAClB,MAAiB,EACjB,cAAoD;YAEpD,IAAI,YAAY,EAAE,CAAC;gBACjB,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YAC5B,CAAC;YACD,YAAY,GAAG;gBACb,SAAS,EAAE,IAAI,GAAG,EAA0B;gBAC5C,MAAM;gBACN,cAAc;aACf,CAAC;QACJ,CAAC;QAED,SAAS,UAAU;YACjB,MAAM,KAAK,GAAG,IAAA,iBAAU,EACtB,YAAY,EACZ,6CAA6C,CAC9C,CAAC;YACF,MAAM,QAAQ,GAAG,cAAc,CAC7B,CAAC,GAAG,KAAK,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,EAC7B,KAAK,CAAC,cAAc,CACrB,CAAC;YACF,WAAW,CAAC,QAAQ,CAAC,CAAC;YACtB,YAAY,GAAG,MAAM,CAAC,GAAG,EAAE,CAAC;QAC9B,CAAC;QAED,SAAS,WAAW,CAClB,SAAuB,EACvB,GAAY,EACZ,cAA+B;YAE/B,GAAG,KAAK,cAAc,CAAC,SAAS,CAAC,CAAC;YAClC,IACE,YAAY;gBACZ,CAAC,cAAc,IAAI,SAAS,CAAC,CAAC,MAAM,KAAK,YAAY,CAAC,MAAM,EAC5D,CAAC;gBACD,MAAM,SAAS,GAAG,YAAY,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;gBAClD,IAAI,SAAS,KAAK,SAAS,EAAE,CAAC;oBAC5B,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;gBAC5B,CAAC;qBAAM,CAAC;oBACN,YAAY,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,SAAS,CAAC,CAAC,CAAC;gBAC/C,CAAC;YACH,CAAC;QACH,CAAC;QAED,wEAAwE;QACxE,SAAS;QACT,wEAAwE;QAExE,OAAO;YACL,OAAO,EAAE,WAAW;YACpB,aAAa,EAAE,WAAW;YAC1B,sBAAsB,CAAC,IAAI;gBACzB,WAAW,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;YAC9C,CAAC;YACD,gBAAgB,CAAC,IAAI;gBACnB,WAAW,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;YAC9C,CAAC;YACD,aAAa,EAAE,WAAW;YAE1B,oBAAoB;YACpB,iBAAiB,CAAC,IAAI;gBACpB,MAAM,aAAa,GAAG,gBAAgB,CAAC,IAAI,CAAC,CAAC;gBAC7C,WAAW,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE,IAAI,IAAI,aAAa,EAAE,IAAI,EAAE,aAAa,CAAC,CAAC;YACzE,CAAC;YACD,0BAA0B,EAAE,WAAW;YACvC,+BAA+B,EAAE,WAAW;YAC5C,iBAAiB,EAAE,WAAW;YAC9B,0BAA0B,CAAC,IAAI;gBAC7B,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;oBACrB,WAAW,CAAC,IAAI,CAAC,CAAC;gBACpB,CAAC;YACH,CAAC;YACD,gBAAgB,CAAC,IAAI;gBACnB,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;oBACrB,WAAW,CAAC,IAAI,CAAC,CAAC;gBACpB,CAAC;YACH,CAAC;YAED,kBAAkB;YAClB,cAAc,EAAE,UAAU;YAC1B,oBAAoB,EAAE,UAAU;YAChC,6BAA6B,EAAE,UAAU;YACzC,uBAAuB,EAAE,UAAU;YACnC,oBAAoB,EAAE,UAAU;SACjC,CAAC;IACJ,CAAC;CACF,CAAC,CAAC;AAEH,SAAS,gBAAgB,CACvB,IAAgC;IAKhC,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,sBAAsB;QAC/D,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,wBAAwB;QAC5D,CAAC,CAAC,IAAI,CAAC,MAAM;QACb,CAAC,CAAC,SAAS,CAAC;AAChB,CAAC;AAED,SAAS,cAAc,CAAC,IAAkB;IACxC,MAAM,IAAI,GAAG,eAAe,CAAC,IAAI,CAAC,CAAC;IAEnC,OAAO,CACL,CAAE,IAAyB,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;QACjD,CAAE,IAAyB,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;QAC/C,IAAI,CACL,CAAC;AACJ,CAAC;AAED,SAAS,eAAe,CAAC,IAAkB;IACzC,QAAQ,IAAI,CAAC,IAAI,EAAE,CAAC;QAClB,KAAK,sBAAc,CAAC,+BAA+B;YACjD,OAAO,aAAa,CAAC;QACvB,KAAK,sBAAc,CAAC,0BAA0B;YAC5C,OAAO,IAAI,CAAC;QACd,OAAO,CAAC,CAAC,CAAC;YACR,MAAM,EAAE,GAAG,EAAE,GAAG,IAAwB,CAAC;YAEzC,OAAO,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAE,GAAwB,CAAC,GAAG,CAAC;QACtE,CAAC;IACH,CAAC;AACH,CAAC;AAED,SAAS,sBAAsB,CAAC,KAAoB;IAClD,QAAQ,KAAK,CAAC,IAAI,EAAE,CAAC;QACnB,KAAK,sBAAc,CAAC,UAAU;YAC5B,OAAO,KAAK,CAAC,IAAI,CAAC;QACpB,KAAK,sBAAc,CAAC,WAAW;YAC7B,OAAO,sBAAsB,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;QAChD;YACE,OAAO,SAAS,CAAC;IACrB,CAAC;AACH,CAAC;AACD,SAAS,YAAY,CAAC,IAAmB;IACvC,OAAO,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,UAAU,CAAC;AACjD,CAAC"}