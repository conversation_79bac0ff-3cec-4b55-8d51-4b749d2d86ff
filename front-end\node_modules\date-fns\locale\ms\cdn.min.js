var A=function(O){return A=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(J){return typeof J}:function(J){return J&&typeof Symbol=="function"&&J.constructor===Symbol&&J!==Symbol.prototype?"symbol":typeof J},A(O)},z=function(O,J){var U=Object.keys(O);if(Object.getOwnPropertySymbols){var Z=Object.getOwnPropertySymbols(O);J&&(Z=Z.filter(function(x){return Object.getOwnPropertyDescriptor(O,x).enumerable})),U.push.apply(U,Z)}return U},K=function(O){for(var J=1;J<arguments.length;J++){var U=arguments[J]!=null?arguments[J]:{};J%2?z(Object(U),!0).forEach(function(Z){B1(O,Z,U[Z])}):Object.getOwnPropertyDescriptors?Object.defineProperties(O,Object.getOwnPropertyDescriptors(U)):z(Object(U)).forEach(function(Z){Object.defineProperty(O,Z,Object.getOwnPropertyDescriptor(U,Z))})}return O},B1=function(O,J,U){if(J=C1(J),J in O)Object.defineProperty(O,J,{value:U,enumerable:!0,configurable:!0,writable:!0});else O[J]=U;return O},C1=function(O){var J=G1(O,"string");return A(J)=="symbol"?J:String(J)},G1=function(O,J){if(A(O)!="object"||!O)return O;var U=O[Symbol.toPrimitive];if(U!==void 0){var Z=U.call(O,J||"default");if(A(Z)!="object")return Z;throw new TypeError("@@toPrimitive must return a primitive value.")}return(J==="string"?String:Number)(O)};(function(O){var J=Object.defineProperty,U=function B(G,C){for(var H in C)J(G,H,{get:C[H],enumerable:!0,configurable:!0,set:function X(Y){return C[H]=function(){return Y}}})},Z={lessThanXSeconds:{one:"kurang dari 1 saat",other:"kurang dari {{count}} saat"},xSeconds:{one:"1 saat",other:"{{count}} saat"},halfAMinute:"setengah minit",lessThanXMinutes:{one:"kurang dari 1 minit",other:"kurang dari {{count}} minit"},xMinutes:{one:"1 minit",other:"{{count}} minit"},aboutXHours:{one:"sekitar 1 jam",other:"sekitar {{count}} jam"},xHours:{one:"1 jam",other:"{{count}} jam"},xDays:{one:"1 hari",other:"{{count}} hari"},aboutXWeeks:{one:"sekitar 1 minggu",other:"sekitar {{count}} minggu"},xWeeks:{one:"1 minggu",other:"{{count}} minggu"},aboutXMonths:{one:"sekitar 1 bulan",other:"sekitar {{count}} bulan"},xMonths:{one:"1 bulan",other:"{{count}} bulan"},aboutXYears:{one:"sekitar 1 tahun",other:"sekitar {{count}} tahun"},xYears:{one:"1 tahun",other:"{{count}} tahun"},overXYears:{one:"lebih dari 1 tahun",other:"lebih dari {{count}} tahun"},almostXYears:{one:"hampir 1 tahun",other:"hampir {{count}} tahun"}},x=function B(G,C,H){var X,Y=Z[G];if(typeof Y==="string")X=Y;else if(C===1)X=Y.one;else X=Y.other.replace("{{count}}",String(C));if(H!==null&&H!==void 0&&H.addSuffix)if(H.comparison&&H.comparison>0)return"dalam masa "+X;else return X+" yang lalu";return X};function D(B){return function(){var G=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},C=G.width?String(G.width):B.defaultWidth,H=B.formats[C]||B.formats[B.defaultWidth];return H}}var $={full:"EEEE, d MMMM yyyy",long:"d MMMM yyyy",medium:"d MMM yyyy",short:"d/M/yyyy"},M={full:"HH.mm.ss",long:"HH.mm.ss",medium:"HH.mm",short:"HH.mm"},S={full:"{{date}} 'pukul' {{time}}",long:"{{date}} 'pukul' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},R={date:D({formats:$,defaultWidth:"full"}),time:D({formats:M,defaultWidth:"full"}),dateTime:D({formats:S,defaultWidth:"full"})},L={lastWeek:"eeee 'lepas pada jam' p",yesterday:"'Semalam pada jam' p",today:"'Hari ini pada jam' p",tomorrow:"'Esok pada jam' p",nextWeek:"eeee 'pada jam' p",other:"P"},V=function B(G,C,H,X){return L[G]};function Q(B){return function(G,C){var H=C!==null&&C!==void 0&&C.context?String(C.context):"standalone",X;if(H==="formatting"&&B.formattingValues){var Y=B.defaultFormattingWidth||B.defaultWidth,I=C!==null&&C!==void 0&&C.width?String(C.width):Y;X=B.formattingValues[I]||B.formattingValues[Y]}else{var T=B.defaultWidth,W=C!==null&&C!==void 0&&C.width?String(C.width):B.defaultWidth;X=B.values[W]||B.values[T]}var E=B.argumentCallback?B.argumentCallback(G):G;return X[E]}}var j={narrow:["SM","M"],abbreviated:["SM","M"],wide:["Sebelum Masihi","Masihi"]},f={narrow:["1","2","3","4"],abbreviated:["S1","S2","S3","S4"],wide:["Suku pertama","Suku kedua","Suku ketiga","Suku keempat"]},v={narrow:["J","F","M","A","M","J","J","O","S","O","N","D"],abbreviated:["Jan","Feb","Mac","Apr","Mei","Jun","Jul","Ogo","Sep","Okt","Nov","Dis"],wide:["Januari","Februari","Mac","April","Mei","Jun","Julai","Ogos","September","Oktober","November","Disember"]},w={narrow:["A","I","S","R","K","J","S"],short:["Ahd","Isn","Sel","Rab","Kha","Jum","Sab"],abbreviated:["Ahd","Isn","Sel","Rab","Kha","Jum","Sab"],wide:["Ahad","Isnin","Selasa","Rabu","Khamis","Jumaat","Sabtu"]},P={narrow:{am:"am",pm:"pm",midnight:"tgh malam",noon:"tgh hari",morning:"pagi",afternoon:"tengah hari",evening:"petang",night:"malam"},abbreviated:{am:"AM",pm:"PM",midnight:"tengah malam",noon:"tengah hari",morning:"pagi",afternoon:"tengah hari",evening:"petang",night:"malam"},wide:{am:"a.m.",pm:"p.m.",midnight:"tengah malam",noon:"tengah hari",morning:"pagi",afternoon:"tengah hari",evening:"petang",night:"malam"}},_={narrow:{am:"am",pm:"pm",midnight:"tengah malam",noon:"tengah hari",morning:"pagi",afternoon:"tengah hari",evening:"petang",night:"malam"},abbreviated:{am:"AM",pm:"PM",midnight:"tengah malam",noon:"tengah hari",morning:"pagi",afternoon:"tengah hari",evening:"petang",night:"malam"},wide:{am:"a.m.",pm:"p.m.",midnight:"tengah malam",noon:"tengah hari",morning:"pagi",afternoon:"tengah hari",evening:"petang",night:"malam"}},F=function B(G,C){return"ke-"+Number(G)},k={ordinalNumber:F,era:Q({values:j,defaultWidth:"wide"}),quarter:Q({values:f,defaultWidth:"wide",argumentCallback:function B(G){return G-1}}),month:Q({values:v,defaultWidth:"wide"}),day:Q({values:w,defaultWidth:"wide"}),dayPeriod:Q({values:P,defaultWidth:"wide",formattingValues:_,defaultFormattingWidth:"wide"})};function q(B){return function(G){var C=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},H=C.width,X=H&&B.matchPatterns[H]||B.matchPatterns[B.defaultMatchWidth],Y=G.match(X);if(!Y)return null;var I=Y[0],T=H&&B.parsePatterns[H]||B.parsePatterns[B.defaultParseWidth],W=Array.isArray(T)?b(T,function(N){return N.test(I)}):m(T,function(N){return N.test(I)}),E;E=B.valueCallback?B.valueCallback(W):W,E=C.valueCallback?C.valueCallback(E):E;var t=G.slice(I.length);return{value:E,rest:t}}}var m=function B(G,C){for(var H in G)if(Object.prototype.hasOwnProperty.call(G,H)&&C(G[H]))return H;return},b=function B(G,C){for(var H=0;H<G.length;H++)if(C(G[H]))return H;return};function h(B){return function(G){var C=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},H=G.match(B.matchPattern);if(!H)return null;var X=H[0],Y=G.match(B.parsePattern);if(!Y)return null;var I=B.valueCallback?B.valueCallback(Y[0]):Y[0];I=C.valueCallback?C.valueCallback(I):I;var T=G.slice(X.length);return{value:I,rest:T}}}var c=/^ke-(\d+)?/i,y=/petama|\d+/i,p={narrow:/^(sm|m)/i,abbreviated:/^(s\.?\s?m\.?|m\.?)/i,wide:/^(sebelum masihi|masihi)/i},d={any:[/^s/i,/^(m)/i]},g={narrow:/^[1234]/i,abbreviated:/^S[1234]/i,wide:/Suku (pertama|kedua|ketiga|keempat)/i},u={any:[/pertama|1/i,/kedua|2/i,/ketiga|3/i,/keempat|4/i]},l={narrow:/^[jfmasond]/i,abbreviated:/^(jan|feb|mac|apr|mei|jun|jul|ogo|sep|okt|nov|dis)/i,wide:/^(januari|februari|mac|april|mei|jun|julai|ogos|september|oktober|november|disember)/i},i={narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^o/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^ma/i,/^ap/i,/^me/i,/^jun/i,/^jul/i,/^og/i,/^s/i,/^ok/i,/^n/i,/^d/i]},n={narrow:/^[aisrkj]/i,short:/^(ahd|isn|sel|rab|kha|jum|sab)/i,abbreviated:/^(ahd|isn|sel|rab|kha|jum|sab)/i,wide:/^(ahad|isnin|selasa|rabu|khamis|jumaat|sabtu)/i},s={narrow:[/^a/i,/^i/i,/^s/i,/^r/i,/^k/i,/^j/i,/^s/i],any:[/^a/i,/^i/i,/^se/i,/^r/i,/^k/i,/^j/i,/^sa/i]},o={narrow:/^(am|pm|tengah malam|tengah hari|pagi|petang|malam)/i,any:/^([ap]\.?\s?m\.?|tengah malam|tengah hari|pagi|petang|malam)/i},r={any:{am:/^a/i,pm:/^pm/i,midnight:/^tengah m/i,noon:/^tengah h/i,morning:/pa/i,afternoon:/tengah h/i,evening:/pe/i,night:/m/i}},e={ordinalNumber:h({matchPattern:c,parsePattern:y,valueCallback:function B(G){return parseInt(G,10)}}),era:q({matchPatterns:p,defaultMatchWidth:"wide",parsePatterns:d,defaultParseWidth:"any"}),quarter:q({matchPatterns:g,defaultMatchWidth:"wide",parsePatterns:u,defaultParseWidth:"any",valueCallback:function B(G){return G+1}}),month:q({matchPatterns:l,defaultMatchWidth:"wide",parsePatterns:i,defaultParseWidth:"any"}),day:q({matchPatterns:n,defaultMatchWidth:"wide",parsePatterns:s,defaultParseWidth:"any"}),dayPeriod:q({matchPatterns:o,defaultMatchWidth:"any",parsePatterns:r,defaultParseWidth:"any"})},a={code:"ms",formatDistance:x,formatLong:R,formatRelative:V,localize:k,match:e,options:{weekStartsOn:1,firstWeekContainsDate:1}};window.dateFns=K(K({},window.dateFns),{},{locale:K(K({},(O=window.dateFns)===null||O===void 0?void 0:O.locale),{},{ms:a})})})();

//# debugId=14E4A5F041DD4F2C64756e2164756e21
