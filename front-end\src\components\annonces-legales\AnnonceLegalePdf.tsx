import React from 'react';
import { Document, Page, Text, View, StyleSheet, Image } from '@react-pdf/renderer';

// Interface pour les données de l'annonce légale
export interface AnnonceLegaleData {
  numeroSerie: string;
  numeroSerieJournal: string;
  selectedService: string;
  nomEntreprise: string;
  formeJuridique: string;
  numeroRC: string;
  capital: string;
  adresseSiege: string;
  objetSocial: string;
  dirigeant: {
    nom: string;
    prenom: string;
    cin: string;
    qualite: string;
    adresse: string;
  };
  journalPublication: string;
  datePublication: string;
  contenuAnnonce: string;
  dateGeneration: string;
}

// Styles pour le PDF
const styles = StyleSheet.create({
  page: {
    flexDirection: 'column',
    backgroundColor: '#FFFFFF',
    padding: 30,
    fontFamily: 'Helvetica',
  },
  header: {
    marginBottom: 20,
    textAlign: 'center',
    borderBottom: 2,
    borderBottomColor: '#FF6B35',
    paddingBottom: 10,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#FF6B35',
    marginBottom: 5,
  },
  subtitle: {
    fontSize: 14,
    color: '#666666',
    marginBottom: 10,
  },
  numeroSerie: {
    fontSize: 12,
    color: '#FF6B35',
    fontWeight: 'bold',
    textAlign: 'right',
    marginBottom: 10,
  },
  section: {
    marginBottom: 15,
    padding: 10,
    backgroundColor: '#F8F9FA',
    borderRadius: 5,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333333',
    marginBottom: 8,
    borderBottom: 1,
    borderBottomColor: '#E0E0E0',
    paddingBottom: 3,
  },
  row: {
    flexDirection: 'row',
    marginBottom: 5,
  },
  label: {
    fontSize: 11,
    fontWeight: 'bold',
    color: '#555555',
    width: '30%',
  },
  value: {
    fontSize: 11,
    color: '#333333',
    width: '70%',
  },
  contenuAnnonce: {
    fontSize: 11,
    lineHeight: 1.4,
    color: '#333333',
    textAlign: 'justify',
    marginTop: 5,
  },
  footer: {
    position: 'absolute',
    bottom: 30,
    left: 30,
    right: 30,
    textAlign: 'center',
    fontSize: 10,
    color: '#888888',
    borderTop: 1,
    borderTopColor: '#E0E0E0',
    paddingTop: 10,
  },
  watermark: {
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: 'translate(-50%, -50%) rotate(-45deg)',
    fontSize: 60,
    color: '#F0F0F0',
    opacity: 0.1,
    zIndex: -1,
  },
});

// Fonction pour formater le nom du journal
const getJournalName = (journalId: string): string => {
  const journaux: { [key: string]: string } = {
    'al-alam': 'Al Alam',
    'leconomiste': 'L\'Économiste',
    'la-vie-eco': 'La Vie Éco',
    'aujourdhui': 'Aujourd\'hui le Maroc',
    'matin': 'Le Matin'
  };
  return journaux[journalId] || journalId;
};

// Fonction pour formater le type de service
const getServiceName = (serviceId: string): string => {
  const services: { [key: string]: string } = {
    'creation-societe': 'Création de société',
    'modification-statuts': 'Modification des statuts',
    'transfert-siege': 'Transfert de siège social',
    'changement-dirigeant': 'Changement de dirigeant',
    'augmentation-capital': 'Augmentation de capital',
    'dissolution': 'Dissolution de société'
  };
  return services[serviceId] || serviceId;
};

const AnnonceLegalePdf: React.FC<AnnonceLegaleData> = ({
  numeroSerie,
  numeroSerieJournal,
  selectedService,
  nomEntreprise,
  formeJuridique,
  numeroRC,
  capital,
  adresseSiege,
  objetSocial,
  dirigeant,
  journalPublication,
  datePublication,
  contenuAnnonce,
  dateGeneration
}) => (
  <Document>
    <Page size="A4" style={styles.page}>
      {/* Watermark */}
      <Text style={styles.watermark}>CHARIKTI</Text>
      
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.title}>ANNONCE LÉGALE</Text>
        <Text style={styles.subtitle}>Plateforme Charikti - Services Juridiques</Text>
      </View>

      {/* Numéros de série */}
      <View style={{ flexDirection: 'column', marginBottom: 15 }}>
        <Text style={styles.numeroSerie}>N° série annonce: {numeroSerie}</Text>
        <Text style={[styles.numeroSerie, { color: '#2563EB', marginTop: 5 }]}>
          N° série journal (quotidien): {numeroSerieJournal}
        </Text>
        <Text style={{ fontSize: 10, color: '#666666', textAlign: 'center', marginTop: 3 }}>
          Numéro unique par jour - s'incrémente chaque nouveau jour
        </Text>
      </View>

      {/* Informations générales */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>INFORMATIONS GÉNÉRALES</Text>
        <View style={styles.row}>
          <Text style={styles.label}>Type d'annonce:</Text>
          <Text style={styles.value}>{getServiceName(selectedService)}</Text>
        </View>
        <View style={styles.row}>
          <Text style={styles.label}>Journal de publication:</Text>
          <Text style={styles.value}>Publication automatique</Text>
        </View>
        <View style={styles.row}>
          <Text style={styles.label}>Date de génération:</Text>
          <Text style={styles.value}>{dateGeneration}</Text>
        </View>
        <View style={styles.row}>
          <Text style={styles.label}>Date de génération:</Text>
          <Text style={styles.value}>{dateGeneration}</Text>
        </View>
      </View>

      {/* Informations de l'entreprise */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>INFORMATIONS DE L'ENTREPRISE</Text>
        <View style={styles.row}>
          <Text style={styles.label}>Dénomination:</Text>
          <Text style={styles.value}>{nomEntreprise}</Text>
        </View>
        <View style={styles.row}>
          <Text style={styles.label}>Forme juridique:</Text>
          <Text style={styles.value}>{formeJuridique}</Text>
        </View>
        {numeroRC && (
          <View style={styles.row}>
            <Text style={styles.label}>Numéro RC:</Text>
            <Text style={styles.value}>{numeroRC}</Text>
          </View>
        )}
        {capital && (
          <View style={styles.row}>
            <Text style={styles.label}>Capital social:</Text>
            <Text style={styles.value}>{capital} DH</Text>
          </View>
        )}
        <View style={styles.row}>
          <Text style={styles.label}>Siège social:</Text>
          <Text style={styles.value}>{adresseSiege}</Text>
        </View>

      </View>

      {/* Informations du dirigeant */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>INFORMATIONS DU DIRIGEANT</Text>
        <View style={styles.row}>
          <Text style={styles.label}>Nom et prénom:</Text>
          <Text style={styles.value}>{dirigeant.nom} {dirigeant.prenom}</Text>
        </View>
        <View style={styles.row}>
          <Text style={styles.label}>CIN:</Text>
          <Text style={styles.value}>{dirigeant.cin}</Text>
        </View>
        <View style={styles.row}>
          <Text style={styles.label}>Qualité:</Text>
          <Text style={styles.value}>{dirigeant.qualite}</Text>
        </View>
        <View style={styles.row}>
          <Text style={styles.label}>Adresse:</Text>
          <Text style={styles.value}>{dirigeant.adresse}</Text>
        </View>
      </View>

      {/* Contenu de l'annonce */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>CONTENU DE L'ANNONCE</Text>
        <Text style={styles.contenuAnnonce}>{contenuAnnonce}</Text>
      </View>

      {/* Footer */}
      <Text style={styles.footer}>
        Document généré automatiquement par la plateforme Charikti - {dateGeneration}
        {'\n'}Ce document constitue un projet d'annonce légale à publier dans le journal sélectionné.
      </Text>
    </Page>
  </Document>
);

export default AnnonceLegalePdf;
