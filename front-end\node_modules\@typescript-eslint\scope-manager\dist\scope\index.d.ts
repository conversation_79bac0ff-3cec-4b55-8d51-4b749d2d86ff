export * from './BlockScope';
export * from './CatchScope';
export * from './ClassFieldInitializerScope';
export * from './ClassScope';
export * from './ConditionalTypeScope';
export * from './ForScope';
export * from './FunctionExpressionNameScope';
export * from './FunctionScope';
export * from './FunctionTypeScope';
export * from './GlobalScope';
export * from './MappedTypeScope';
export * from './ModuleScope';
export * from './Scope';
export * from './ScopeType';
export * from './SwitchScope';
export * from './TSEnumScope';
export * from './TSModuleScope';
export * from './TypeScope';
export * from './WithScope';
//# sourceMappingURL=index.d.ts.map