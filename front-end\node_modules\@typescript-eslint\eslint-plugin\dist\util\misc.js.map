{"version": 3, "file": "misc.js", "sourceRoot": "", "sources": ["../../src/util/misc.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAmRE,8CAAiB;AACjB,wCAAc;AAGd,0CAAe;AACf,wCAAc;AACd,oCAAY;AACZ,gEAA0B;AAC1B,8DAAyB;AACzB,8CAAiB;AACjB,4CAAgB;AAChB,gEAA0B;AAC1B,4DAAwB;AAIxB,kEAA2B;AAC3B,wCAAc;AACd,sCAAa;AArSf;;GAEG;AACH,8DAAgE;AAEhE,oDAA0D;AAE1D,+CAAiC;AAEjC,yCAA6D;AAE7D,MAAM,qBAAqB,GAAG;IAC5B,EAAE,CAAC,SAAS,CAAC,GAAG;IAChB,EAAE,CAAC,SAAS,CAAC,IAAI;IACjB,EAAE,CAAC,SAAS,CAAC,IAAI;CACT,CAAC;AACX;;GAEG;AACH,SAAS,gBAAgB,CAAC,QAAgB;IACxC,MAAM,aAAa,GAAG,QAAQ,CAAC,WAAW,EAAE,CAAC;IAC7C,KAAK,MAAM,aAAa,IAAI,qBAAqB,EAAE,CAAC;QAClD,IAAI,aAAa,CAAC,QAAQ,CAAC,aAAa,CAAC,EAAE,CAAC;YAC1C,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AAED;;GAEG;AACH,SAAS,cAAc,CAAC,GAAW;IACjC,OAAO,GAAG,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AAC7C,CAAC;AAED,SAAS,iBAAiB,CACxB,KAAU,EACV,MAAwB;IAExB,MAAM,MAAM,GAAG,IAAI,GAAG,EAAY,CAAC;IAEnC,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;QACzB,MAAM,GAAG,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC;QACzB,MAAM,QAAQ,GAAG,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAEjC,IAAI,QAAQ,EAAE,CAAC;YACb,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACtB,CAAC;aAAM,CAAC;YACN,MAAM,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC;QAC1B,CAAC;IACH,CAAC;IAED,OAAO,MAAM,CAAC;AAChB,CAAC;AAKD,SAAS,cAAc,CACrB,CAAkB,EAClB,CAAkB,EAClB,EAA2B;IAE3B,OAAO,CACL,CAAC,KAAK,CAAC;QACP,CAAC,CAAC,KAAK,SAAS;YACd,CAAC,KAAK,SAAS;YACf,CAAC,CAAC,MAAM,KAAK,CAAC,CAAC,MAAM;YACrB,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,GAAG,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CACtC,CAAC;AACJ,CAAC;AAED,gDAAgD;AAChD,SAAS,eAAe,CACtB,MAAW,EACX,SAAkC;IAElC,KAAK,MAAM,OAAO,IAAI,MAAM,EAAE,CAAC;QAC7B,MAAM,MAAM,GAAG,SAAS,CAAC,OAAO,CAAC,CAAC;QAClC,IAAI,MAAM,KAAK,SAAS,EAAE,CAAC;YACzB,OAAO,MAAM,CAAC;QAChB,CAAC;IACH,CAAC;IACD,OAAO,SAAS,CAAC;AACnB,CAAC;AAED;;GAEG;AACH,SAAS,yBAAyB,CAAC,IAA+B;IAChE,MAAM,QAAQ,GAAsC,IAAI,CAAC,UAAU,CAAC,IAAI,CACtE,CAAC,SAA6B,EAAoC,EAAE,CAClE,SAAS,CAAC,IAAI,KAAK,sBAAc,CAAC,UAAU,CAC/C,CAAC;IACF,OAAO,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,mBAAmB,CAAC;AACxD,CAAC;AAED,IAAK,cAKJ;AALD,WAAK,cAAc;IACjB,yDAAW,CAAA;IACX,uDAAU,CAAA;IACV,uDAAU,CAAA;IACV,+DAAc,CAAA;AAChB,CAAC,EALI,cAAc,8BAAd,cAAc,QAKlB;AAED;;;GAGG;AACH,SAAS,iBAAiB,CACxB,MASgC,EAChC,UAA+B;IAE/B,IAAI,MAAM,CAAC,GAAG,CAAC,IAAI,KAAK,sBAAc,CAAC,UAAU,EAAE,CAAC;QAClD,OAAO;YACL,IAAI,EAAE,cAAc,CAAC,MAAM;YAC3B,IAAI,EAAE,MAAM,CAAC,GAAG,CAAC,IAAI;SACtB,CAAC;IACJ,CAAC;IACD,IAAI,MAAM,CAAC,GAAG,CAAC,IAAI,KAAK,sBAAc,CAAC,iBAAiB,EAAE,CAAC;QACzD,OAAO;YACL,IAAI,EAAE,cAAc,CAAC,OAAO;YAC5B,IAAI,EAAE,IAAI,MAAM,CAAC,GAAG,CAAC,IAAI,EAAE;SAC5B,CAAC;IACJ,CAAC;IACD,IAAI,MAAM,CAAC,GAAG,CAAC,IAAI,KAAK,sBAAc,CAAC,OAAO,EAAE,CAAC;QAC/C,MAAM,IAAI,GAAG,GAAG,MAAM,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC;QACnC,IAAI,IAAA,4BAAe,EAAC,IAAI,CAAC,EAAE,CAAC;YAC1B,OAAO;gBACL,IAAI,EAAE,cAAc,CAAC,MAAM;gBAC3B,IAAI,EAAE,IAAI,IAAI,GAAG;aAClB,CAAC;QACJ,CAAC;QACD,OAAO;YACL,IAAI,EAAE,cAAc,CAAC,MAAM;YAC3B,IAAI;SACL,CAAC;IACJ,CAAC;IAED,OAAO;QACL,IAAI,EAAE,cAAc,CAAC,UAAU;QAC/B,IAAI,EAAE,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC;KACjD,CAAC;AACJ,CAAC;AAWD,SAAS,YAAY,CAAmB,MAA0B;IAChE,OAAO,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAQ,CAAC;AAClE,CAAC;AAED;;;;;GAKG;AACH,SAAS,cAAc,CAAC,KAAe;IACrC,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC;QAClB,OAAO,EAAE,CAAC;IACZ,CAAC;IAED,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QACvB,OAAO,KAAK,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC;IAED,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AAC3E,CAAC;AAED;;;;;GAKG;AACH,SAAS,aAAa,CACpB,OAAY,EACZ,SAAoD;IAEpD,IAAI,GAAG,GAAG,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC;IAE7B,OAAO,GAAG,IAAI,CAAC,EAAE,CAAC;QAChB,MAAM,KAAK,GAAG,SAAS,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC;QACtC,IAAI,KAAK,EAAE,CAAC;YACV,OAAO,GAAG,CAAC;QACb,CAAC;QACD,GAAG,EAAE,CAAC;IACR,CAAC;IAED,OAAO,CAAC,CAAC,CAAC;AACZ,CAAC;AAED,SAAS,2BAA2B,CAClC,IAAuB,EACvB,IAAY;IAEZ,OAAO,CACL,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,cAAc;QAC3C,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,iBAAiB;QAC9C,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,iBAAiB;QAC9C,CAAC,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,WAAW,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;QAClE,CAAC,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,kBAAkB,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAC1E,CAAC;AACJ,CAAC;AAED,SAAS,0BAA0B,CAAC,IAAoB;IACtD,OAAO,EAAE,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC;AAC7D,CAAC;AAED,SAAS,wBAAwB,CAC/B,IAAsC,EACtC,UAA+B;IAE/B,OAAO,CACL,IAAI,CAAC,MAAM,CAAC,MAAM,KAAK,CAAC,IAAI,CAAC,IAAA,0BAAe,EAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,CACzE,CAAC;AACJ,CAAC;AASD,SAAS,0BAA0B,CACjC,IAAiB,EACjB,EAAE,UAAU,EAAkC;IAE9C,MAAM,GAAG,GACP,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC;IAC3E,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;QACnB,OAAO,GAAG,CAAC,IAAI,KAAK,sBAAc,CAAC,OAAO;YACxC,CAAC,CAAC,GAAG,GAAG,CAAC,KAAK,EAAE;YAChB,CAAC,CAAE,GAAwD,CAAC,IAAI,CAAC;IACrE,CAAC;IACD,MAAM,KAAK,GAAG,IAAA,yBAAc,EAAC,GAAG,EAAE,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,EAAE,KAIjD,CAAC;IACd,OAAO,KAAK,IAAI,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK,EAAE,CAAC;AAChD,CAAC;AAED;;;;GAIG;AACH,MAAM,2BAA2B,GAAG,CAClC,gBAA6B,EAC7B,OAAuC,EACvC,GAAG,MAAgB,EACV,EAAE,CACV,MAAiC,CAAC,QAAQ,CACzC,0BAA0B,CAAC,gBAAgB,EAAE,OAAO,CAAC,CACtD,CAAC;AAgBF,kEAA2B"}