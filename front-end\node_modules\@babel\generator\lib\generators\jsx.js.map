{"version": 3, "names": ["JSXAttribute", "node", "print", "name", "value", "token", "JSXIdentifier", "word", "JSXNamespacedName", "namespace", "JSXMemberExpression", "object", "property", "JSXSpreadAttribute", "argument", "rightBrace", "JSXExpressionContainer", "expression", "JSXSpreadChild", "JSXText", "raw", "getPossibleRaw", "undefined", "JSXElement", "open", "openingElement", "selfClosing", "indent", "child", "children", "dedent", "closingElement", "spaceSeparator", "space", "JSXOpeningElement", "typeParameters", "attributes", "length", "printJoin", "separator", "JSXClosingElement", "JSXEmptyExpression", "printInnerComments", "JSXFragment", "openingFragment", "closingFragment", "JSXOpeningFragment", "JSXClosingFragment"], "sources": ["../../src/generators/jsx.ts"], "sourcesContent": ["import type Printer from \"../printer.ts\";\nimport type * as t from \"@babel/types\";\n\nexport function JSXAttribute(this: Printer, node: t.JSXAttribute) {\n  this.print(node.name);\n  if (node.value) {\n    this.token(\"=\");\n    this.print(node.value);\n  }\n}\n\nexport function JSXIdentifier(this: Printer, node: t.JSXIdentifier) {\n  this.word(node.name);\n}\n\nexport function JSXNamespacedName(this: Printer, node: t.JSXNamespacedName) {\n  this.print(node.namespace);\n  this.token(\":\");\n  this.print(node.name);\n}\n\nexport function JSXMemberExpression(\n  this: Printer,\n  node: t.JSXMemberExpression,\n) {\n  this.print(node.object);\n  this.token(\".\");\n  this.print(node.property);\n}\n\nexport function JSXSpreadAttribute(this: Printer, node: t.JSXSpreadAttribute) {\n  this.token(\"{\");\n  this.token(\"...\");\n  this.print(node.argument);\n  this.rightBrace(node);\n}\n\nexport function JSXExpressionContainer(\n  this: Printer,\n  node: t.JSXExpressionContainer,\n) {\n  this.token(\"{\");\n  this.print(node.expression);\n  this.rightBrace(node);\n}\n\nexport function JSXSpreadChild(this: Printer, node: t.JSXSpreadChild) {\n  this.token(\"{\");\n  this.token(\"...\");\n  this.print(node.expression);\n  this.rightBrace(node);\n}\n\nexport function JSXText(this: Printer, node: t.JSXText) {\n  const raw = this.getPossibleRaw(node);\n\n  if (raw !== undefined) {\n    this.token(raw, true);\n  } else {\n    this.token(node.value, true);\n  }\n}\n\nexport function JSXElement(this: Printer, node: t.JSXElement) {\n  const open = node.openingElement;\n  this.print(open);\n  if (open.selfClosing) return;\n\n  this.indent();\n  for (const child of node.children) {\n    this.print(child);\n  }\n  this.dedent();\n\n  this.print(node.closingElement);\n}\n\nfunction spaceSeparator(this: Printer) {\n  this.space();\n}\n\nexport function JSXOpeningElement(this: Printer, node: t.JSXOpeningElement) {\n  this.token(\"<\");\n  this.print(node.name);\n  this.print(node.typeParameters); // TS\n  if (node.attributes.length > 0) {\n    this.space();\n    this.printJoin(node.attributes, { separator: spaceSeparator });\n  }\n  if (node.selfClosing) {\n    this.space();\n    this.token(\"/>\");\n  } else {\n    this.token(\">\");\n  }\n}\n\nexport function JSXClosingElement(this: Printer, node: t.JSXClosingElement) {\n  this.token(\"</\");\n  this.print(node.name);\n  this.token(\">\");\n}\n\nexport function JSXEmptyExpression(this: Printer) {\n  // This node is empty, so forcefully print its inner comments.\n  this.printInnerComments();\n}\n\nexport function JSXFragment(this: Printer, node: t.JSXFragment) {\n  this.print(node.openingFragment);\n\n  this.indent();\n  for (const child of node.children) {\n    this.print(child);\n  }\n  this.dedent();\n\n  this.print(node.closingFragment);\n}\n\nexport function JSXOpeningFragment(this: Printer) {\n  this.token(\"<\");\n  this.token(\">\");\n}\n\nexport function JSXClosingFragment(this: Printer) {\n  this.token(\"</\");\n  this.token(\">\");\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAGO,SAASA,YAAYA,CAAgBC,IAAoB,EAAE;EAChE,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,IAAI,CAAC;EACrB,IAAIF,IAAI,CAACG,KAAK,EAAE;IACd,IAAI,CAACC,SAAK,GAAI,CAAC;IACf,IAAI,CAACH,KAAK,CAACD,IAAI,CAACG,KAAK,CAAC;EACxB;AACF;AAEO,SAASE,aAAaA,CAAgBL,IAAqB,EAAE;EAClE,IAAI,CAACM,IAAI,CAACN,IAAI,CAACE,IAAI,CAAC;AACtB;AAEO,SAASK,iBAAiBA,CAAgBP,IAAyB,EAAE;EAC1E,IAAI,CAACC,KAAK,CAACD,IAAI,CAACQ,SAAS,CAAC;EAC1B,IAAI,CAACJ,SAAK,GAAI,CAAC;EACf,IAAI,CAACH,KAAK,CAACD,IAAI,CAACE,IAAI,CAAC;AACvB;AAEO,SAASO,mBAAmBA,CAEjCT,IAA2B,EAC3B;EACA,IAAI,CAACC,KAAK,CAACD,IAAI,CAACU,MAAM,CAAC;EACvB,IAAI,CAACN,SAAK,GAAI,CAAC;EACf,IAAI,CAACH,KAAK,CAACD,IAAI,CAACW,QAAQ,CAAC;AAC3B;AAEO,SAASC,kBAAkBA,CAAgBZ,IAA0B,EAAE;EAC5E,IAAI,CAACI,SAAK,IAAI,CAAC;EACf,IAAI,CAACA,KAAK,CAAC,KAAK,CAAC;EACjB,IAAI,CAACH,KAAK,CAACD,IAAI,CAACa,QAAQ,CAAC;EACzB,IAAI,CAACC,UAAU,CAACd,IAAI,CAAC;AACvB;AAEO,SAASe,sBAAsBA,CAEpCf,IAA8B,EAC9B;EACA,IAAI,CAACI,SAAK,IAAI,CAAC;EACf,IAAI,CAACH,KAAK,CAACD,IAAI,CAACgB,UAAU,CAAC;EAC3B,IAAI,CAACF,UAAU,CAACd,IAAI,CAAC;AACvB;AAEO,SAASiB,cAAcA,CAAgBjB,IAAsB,EAAE;EACpE,IAAI,CAACI,SAAK,IAAI,CAAC;EACf,IAAI,CAACA,KAAK,CAAC,KAAK,CAAC;EACjB,IAAI,CAACH,KAAK,CAACD,IAAI,CAACgB,UAAU,CAAC;EAC3B,IAAI,CAACF,UAAU,CAACd,IAAI,CAAC;AACvB;AAEO,SAASkB,OAAOA,CAAgBlB,IAAe,EAAE;EACtD,MAAMmB,GAAG,GAAG,IAAI,CAACC,cAAc,CAACpB,IAAI,CAAC;EAErC,IAAImB,GAAG,KAAKE,SAAS,EAAE;IACrB,IAAI,CAACjB,KAAK,CAACe,GAAG,EAAE,IAAI,CAAC;EACvB,CAAC,MAAM;IACL,IAAI,CAACf,KAAK,CAACJ,IAAI,CAACG,KAAK,EAAE,IAAI,CAAC;EAC9B;AACF;AAEO,SAASmB,UAAUA,CAAgBtB,IAAkB,EAAE;EAC5D,MAAMuB,IAAI,GAAGvB,IAAI,CAACwB,cAAc;EAChC,IAAI,CAACvB,KAAK,CAACsB,IAAI,CAAC;EAChB,IAAIA,IAAI,CAACE,WAAW,EAAE;EAEtB,IAAI,CAACC,MAAM,CAAC,CAAC;EACb,KAAK,MAAMC,KAAK,IAAI3B,IAAI,CAAC4B,QAAQ,EAAE;IACjC,IAAI,CAAC3B,KAAK,CAAC0B,KAAK,CAAC;EACnB;EACA,IAAI,CAACE,MAAM,CAAC,CAAC;EAEb,IAAI,CAAC5B,KAAK,CAACD,IAAI,CAAC8B,cAAc,CAAC;AACjC;AAEA,SAASC,cAAcA,CAAA,EAAgB;EACrC,IAAI,CAACC,KAAK,CAAC,CAAC;AACd;AAEO,SAASC,iBAAiBA,CAAgBjC,IAAyB,EAAE;EAC1E,IAAI,CAACI,SAAK,GAAI,CAAC;EACf,IAAI,CAACH,KAAK,CAACD,IAAI,CAACE,IAAI,CAAC;EACrB,IAAI,CAACD,KAAK,CAACD,IAAI,CAACkC,cAAc,CAAC;EAC/B,IAAIlC,IAAI,CAACmC,UAAU,CAACC,MAAM,GAAG,CAAC,EAAE;IAC9B,IAAI,CAACJ,KAAK,CAAC,CAAC;IACZ,IAAI,CAACK,SAAS,CAACrC,IAAI,CAACmC,UAAU,EAAE;MAAEG,SAAS,EAAEP;IAAe,CAAC,CAAC;EAChE;EACA,IAAI/B,IAAI,CAACyB,WAAW,EAAE;IACpB,IAAI,CAACO,KAAK,CAAC,CAAC;IACZ,IAAI,CAAC5B,KAAK,CAAC,IAAI,CAAC;EAClB,CAAC,MAAM;IACL,IAAI,CAACA,SAAK,GAAI,CAAC;EACjB;AACF;AAEO,SAASmC,iBAAiBA,CAAgBvC,IAAyB,EAAE;EAC1E,IAAI,CAACI,KAAK,CAAC,IAAI,CAAC;EAChB,IAAI,CAACH,KAAK,CAACD,IAAI,CAACE,IAAI,CAAC;EACrB,IAAI,CAACE,SAAK,GAAI,CAAC;AACjB;AAEO,SAASoC,kBAAkBA,CAAA,EAAgB;EAEhD,IAAI,CAACC,kBAAkB,CAAC,CAAC;AAC3B;AAEO,SAASC,WAAWA,CAAgB1C,IAAmB,EAAE;EAC9D,IAAI,CAACC,KAAK,CAACD,IAAI,CAAC2C,eAAe,CAAC;EAEhC,IAAI,CAACjB,MAAM,CAAC,CAAC;EACb,KAAK,MAAMC,KAAK,IAAI3B,IAAI,CAAC4B,QAAQ,EAAE;IACjC,IAAI,CAAC3B,KAAK,CAAC0B,KAAK,CAAC;EACnB;EACA,IAAI,CAACE,MAAM,CAAC,CAAC;EAEb,IAAI,CAAC5B,KAAK,CAACD,IAAI,CAAC4C,eAAe,CAAC;AAClC;AAEO,SAASC,kBAAkBA,CAAA,EAAgB;EAChD,IAAI,CAACzC,SAAK,GAAI,CAAC;EACf,IAAI,CAACA,SAAK,GAAI,CAAC;AACjB;AAEO,SAAS0C,kBAAkBA,CAAA,EAAgB;EAChD,IAAI,CAAC1C,KAAK,CAAC,IAAI,CAAC;EAChB,IAAI,CAACA,SAAK,GAAI,CAAC;AACjB", "ignoreList": []}