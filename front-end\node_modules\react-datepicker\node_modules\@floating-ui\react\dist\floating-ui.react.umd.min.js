!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports,require("react"),require("react-dom"),require("@floating-ui/react-dom")):"function"==typeof define&&define.amd?define(["exports","react","react-dom","@floating-ui/react-dom"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).FloatingUIReact={},e.<PERSON><PERSON>,e.ReactDOM,e.FloatingUIReactDOM)}(this,(function(e,t,n,r){"use strict";function o(e){var t=Object.create(null);return e&&Object.keys(e).forEach((function(n){if("default"!==n){var r=Object.getOwnPropertyDescriptor(e,n);Object.defineProperty(t,n,r.get?r:{enumerable:!0,get:function(){return e[n]}})}})),t.default=e,Object.freeze(t)}var u=o(t),i=o(n);function l(){return"undefined"!=typeof window}function c(e){return a(e)?(e.nodeName||"").toLowerCase():"#document"}function s(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function a(e){return!!l()&&(e instanceof Node||e instanceof s(e).Node)}function f(e){return!!l()&&(e instanceof Element||e instanceof s(e).Element)}function d(e){return!!l()&&(e instanceof HTMLElement||e instanceof s(e).HTMLElement)}function v(e){return!(!l()||"undefined"==typeof ShadowRoot)&&(e instanceof ShadowRoot||e instanceof s(e).ShadowRoot)}function m(e){return["html","body","#document"].includes(c(e))}function p(e){return s(e).getComputedStyle(e)}function g(e){if("html"===c(e))return e;const t=e.assignedSlot||e.parentNode||v(e)&&e.host||function(e){var t;return null==(t=(a(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}(e);return v(t)?t.host:t}const h=Math.min,y=Math.max,b=Math.round,w=Math.floor;
/*!
  * tabbable 6.2.0
  * @license MIT, https://github.com/focus-trap/tabbable/blob/master/LICENSE
  */
var E=["input:not([inert])","select:not([inert])","textarea:not([inert])","a[href]:not([inert])","button:not([inert])","[tabindex]:not(slot):not([inert])","audio[controls]:not([inert])","video[controls]:not([inert])",'[contenteditable]:not([contenteditable="false"]):not([inert])',"details>summary:first-of-type:not([inert])","details:not([inert])"].join(","),R="undefined"==typeof Element,x=R?function(){}:Element.prototype.matches||Element.prototype.msMatchesSelector||Element.prototype.webkitMatchesSelector,I=!R&&Element.prototype.getRootNode?function(e){var t;return null==e||null===(t=e.getRootNode)||void 0===t?void 0:t.call(e)}:function(e){return null==e?void 0:e.ownerDocument},k=function e(t,n){var r;void 0===n&&(n=!0);var o=null==t||null===(r=t.getAttribute)||void 0===r?void 0:r.call(t,"inert");return""===o||"true"===o||n&&t&&e(t.parentNode)},C=function(e,t,n){if(k(e))return[];var r=Array.prototype.slice.apply(e.querySelectorAll(E));return t&&x.call(e,E)&&r.unshift(e),r=r.filter(n)},M=function e(t,n,r){for(var o=[],u=Array.from(t);u.length;){var i=u.shift();if(!k(i,!1))if("SLOT"===i.tagName){var l=i.assignedElements(),c=e(l.length?l:i.children,!0,r);r.flatten?o.push.apply(o,c):o.push({scopeParent:i,candidates:c})}else{x.call(i,E)&&r.filter(i)&&(n||!t.includes(i))&&o.push(i);var s=i.shadowRoot||"function"==typeof r.getShadowRoot&&r.getShadowRoot(i),a=!k(s,!1)&&(!r.shadowRootFilter||r.shadowRootFilter(i));if(s&&a){var f=e(!0===s?i.children:s.children,!0,r);r.flatten?o.push.apply(o,f):o.push({scopeParent:i,candidates:f})}else u.unshift.apply(u,i.children)}}return o},O=function(e){return!isNaN(parseInt(e.getAttribute("tabindex"),10))},S=function(e){if(!e)throw new Error("No node provided");return e.tabIndex<0&&(/^(AUDIO|VIDEO|DETAILS)$/.test(e.tagName)||function(e){var t,n=null==e||null===(t=e.getAttribute)||void 0===t?void 0:t.call(e,"contenteditable");return""===n||"true"===n}(e))&&!O(e)?0:e.tabIndex},P=function(e,t){return e.tabIndex===t.tabIndex?e.documentOrder-t.documentOrder:e.tabIndex-t.tabIndex},T=function(e){return"INPUT"===e.tagName},A=function(e){return function(e){return T(e)&&"radio"===e.type}(e)&&!function(e){if(!e.name)return!0;var t,n=e.form||I(e),r=function(e){return n.querySelectorAll('input[type="radio"][name="'+e+'"]')};if("undefined"!=typeof window&&void 0!==window.CSS&&"function"==typeof window.CSS.escape)t=r(window.CSS.escape(e.name));else try{t=r(e.name)}catch(e){return console.error("Looks like you have a radio button with a name attribute containing invalid CSS selector characters and need the CSS.escape polyfill: %s",e.message),!1}var o=function(e,t){for(var n=0;n<e.length;n++)if(e[n].checked&&e[n].form===t)return e[n]}(t,e.form);return!o||o===e}(e)},L=function(e){var t=e.getBoundingClientRect(),n=t.width,r=t.height;return 0===n&&0===r},N=function(e,t){var n=t.displayCheck,r=t.getShadowRoot;if("hidden"===getComputedStyle(e).visibility)return!0;var o=x.call(e,"details>summary:first-of-type")?e.parentElement:e;if(x.call(o,"details:not([open]) *"))return!0;if(n&&"full"!==n&&"legacy-full"!==n){if("non-zero-area"===n)return L(e)}else{if("function"==typeof r){for(var u=e;e;){var i=e.parentElement,l=I(e);if(i&&!i.shadowRoot&&!0===r(i))return L(e);e=e.assignedSlot?e.assignedSlot:i||l===e.ownerDocument?i:l.host}e=u}if(function(e){var t,n,r,o,u=e&&I(e),i=null===(t=u)||void 0===t?void 0:t.host,l=!1;if(u&&u!==e)for(l=!!(null!==(n=i)&&void 0!==n&&null!==(r=n.ownerDocument)&&void 0!==r&&r.contains(i)||null!=e&&null!==(o=e.ownerDocument)&&void 0!==o&&o.contains(e));!l&&i;){var c,s,a;l=!(null===(s=i=null===(c=u=I(i))||void 0===c?void 0:c.host)||void 0===s||null===(a=s.ownerDocument)||void 0===a||!a.contains(i))}return l}(e))return!e.getClientRects().length;if("legacy-full"!==n)return!0}return!1},D=function(e,t){return!(t.disabled||k(t)||function(e){return T(e)&&"hidden"===e.type}(t)||N(t,e)||function(e){return"DETAILS"===e.tagName&&Array.prototype.slice.apply(e.children).some((function(e){return"SUMMARY"===e.tagName}))}(t)||function(e){if(/^(INPUT|BUTTON|SELECT|TEXTAREA)$/.test(e.tagName))for(var t=e.parentElement;t;){if("FIELDSET"===t.tagName&&t.disabled){for(var n=0;n<t.children.length;n++){var r=t.children.item(n);if("LEGEND"===r.tagName)return!!x.call(t,"fieldset[disabled] *")||!r.contains(e)}return!0}t=t.parentElement}return!1}(t))},F=function(e,t){return!(A(t)||S(t)<0||!D(e,t))},j=function(e){var t=parseInt(e.getAttribute("tabindex"),10);return!!(isNaN(t)||t>=0)},K=function e(t){var n=[],r=[];return t.forEach((function(t,o){var u=!!t.scopeParent,i=u?t.scopeParent:t,l=function(e,t){var n=S(e);return n<0&&t&&!O(e)?0:n}(i,u),c=u?e(t.candidates):i;0===l?u?n.push.apply(n,c):n.push(i):r.push({documentOrder:o,tabIndex:l,item:t,isScope:u,content:c})})),r.sort(P).reduce((function(e,t){return t.isScope?e.push.apply(e,t.content):e.push(t.content),e}),[]).concat(n)},H=function(e,t){var n;return n=(t=t||{}).getShadowRoot?M([e],t.includeContainer,{filter:F.bind(null,t),flatten:!1,getShadowRoot:t.getShadowRoot,shadowRootFilter:j}):C(e,t.includeContainer,F.bind(null,t)),K(n)},q=function(e,t){if(t=t||{},!e)throw new Error("No node provided");return!1!==x.call(e,E)&&F(t,e)};function _(){const e=navigator.userAgentData;return null!=e&&e.platform?e.platform:navigator.platform}function W(){const e=navigator.userAgentData;return e&&Array.isArray(e.brands)?e.brands.map((e=>{let{brand:t,version:n}=e;return t+"/"+n})).join(" "):navigator.userAgent}function U(){return/apple/i.test(navigator.vendor)}function B(){const e=/android/i;return e.test(_())||e.test(W())}function z(){return W().includes("jsdom/")}const X="data-floating-ui-focusable",Y="ArrowLeft",V="ArrowRight";function G(e){let t=e.activeElement;for(;null!=(null==(n=t)||null==(n=n.shadowRoot)?void 0:n.activeElement);){var n;t=t.shadowRoot.activeElement}return t}function Z(e,t){if(!e||!t)return!1;const n=null==t.getRootNode?void 0:t.getRootNode();if(e.contains(t))return!0;if(n&&v(n)){let n=t;for(;n;){if(e===n)return!0;n=n.parentNode||n.host}}return!1}function $(e){return"composedPath"in e?e.composedPath()[0]:e.target}function Q(e,t){if(null==t)return!1;if("composedPath"in e)return e.composedPath().includes(t);const n=e;return null!=n.target&&t.contains(n.target)}function J(e){return(null==e?void 0:e.ownerDocument)||document}function ee(e){return d(e)&&e.matches("input:not([type='hidden']):not([disabled]),[contenteditable]:not([contenteditable='false']),textarea:not([disabled])")}function te(e){return!!e&&("combobox"===e.getAttribute("role")&&ee(e))}function ne(e){return e?e.hasAttribute(X)?e:e.querySelector("["+X+"]")||e:null}function re(e,t,n){void 0===n&&(n=!0);let r=e.filter((e=>{var n;return e.parentId===t&&(null==(n=e.context)?void 0:n.open)})),o=r;for(;o.length;)o=n?e.filter((e=>{var t;return null==(t=o)?void 0:t.some((t=>{var n;return e.parentId===t.id&&(null==(n=e.context)?void 0:n.open)}))})):e,r=r.concat(o);return r}function oe(e,t){var n;let r=[],o=null==(n=e.find((e=>e.id===t)))?void 0:n.parentId;for(;o;){const t=e.find((e=>e.id===o));o=null==t?void 0:t.parentId,t&&(r=r.concat(t))}return r}function ue(e){e.preventDefault(),e.stopPropagation()}function ie(e){return!(0!==e.mozInputSource||!e.isTrusted)||(B()&&e.pointerType?"click"===e.type&&1===e.buttons:0===e.detail&&!e.pointerType)}function le(e){return!z()&&(!B()&&0===e.width&&0===e.height||B()&&1===e.width&&1===e.height&&0===e.pressure&&0===e.detail&&"mouse"===e.pointerType||e.width<1&&e.height<1&&0===e.pressure&&0===e.detail&&"touch"===e.pointerType)}function ce(e,t){const n=["mouse","pen"];return t||n.push("",void 0),n.includes(e)}var se="undefined"!=typeof document?t.useLayoutEffect:t.useEffect;function ae(e){const t=u.useRef(e);return se((()=>{t.current=e})),t}const fe={...u}.useInsertionEffect||(e=>e());function de(e){const t=u.useRef((()=>{if("production"!==process.env.NODE_ENV)throw new Error("Cannot call an event handler while rendering.")}));return fe((()=>{t.current=e})),u.useCallback((function(){for(var e=arguments.length,n=new Array(e),r=0;r<e;r++)n[r]=arguments[r];return null==t.current?void 0:t.current(...n)}),[])}function ve(e,t,n){return Math.floor(e/t)!==n}function me(e,t){return t<0||t>=e.current.length}function pe(e,t){return he(e,{disabledIndices:t})}function ge(e,t){return he(e,{decrement:!0,startingIndex:e.current.length,disabledIndices:t})}function he(e,t){let{startingIndex:n=-1,decrement:r=!1,disabledIndices:o,amount:u=1}=void 0===t?{}:t,i=n;do{i+=r?-u:u}while(i>=0&&i<=e.current.length-1&&Re(e,i,o));return i}function ye(e,t){let{event:n,orientation:r,loop:o,rtl:u,cols:i,disabledIndices:l,minIndex:c,maxIndex:s,prevIndex:a,stopEvent:f=!1}=t,d=a;if("ArrowUp"===n.key){if(f&&ue(n),-1===a)d=s;else if(d=he(e,{startingIndex:d,amount:i,decrement:!0,disabledIndices:l}),o&&(a-i<c||d<0)){const e=a%i,t=s%i,n=s-(t-e);d=t===e?s:t>e?n:n-i}me(e,d)&&(d=a)}if("ArrowDown"===n.key&&(f&&ue(n),-1===a?d=c:(d=he(e,{startingIndex:a,amount:i,disabledIndices:l}),o&&a+i>s&&(d=he(e,{startingIndex:a%i-i,amount:i,disabledIndices:l}))),me(e,d)&&(d=a)),"both"===r){const t=w(a/i);n.key===(u?Y:V)&&(f&&ue(n),a%i!=i-1?(d=he(e,{startingIndex:a,disabledIndices:l}),o&&ve(d,i,t)&&(d=he(e,{startingIndex:a-a%i-1,disabledIndices:l}))):o&&(d=he(e,{startingIndex:a-a%i-1,disabledIndices:l})),ve(d,i,t)&&(d=a)),n.key===(u?V:Y)&&(f&&ue(n),a%i!=0?(d=he(e,{startingIndex:a,decrement:!0,disabledIndices:l}),o&&ve(d,i,t)&&(d=he(e,{startingIndex:a+(i-a%i),decrement:!0,disabledIndices:l}))):o&&(d=he(e,{startingIndex:a+(i-a%i),decrement:!0,disabledIndices:l})),ve(d,i,t)&&(d=a));const r=w(s/i)===t;me(e,d)&&(d=o&&r?n.key===(u?V:Y)?s:he(e,{startingIndex:a-a%i-1,disabledIndices:l}):a)}return d}function be(e,t,n){const r=[];let o=0;return e.forEach(((e,u)=>{let{width:i,height:l}=e;if(i>t&&"production"!==process.env.NODE_ENV)throw new Error("[Floating UI]: Invalid grid - item width at index "+u+" is greater than grid columns");let c=!1;for(n&&(o=0);!c;){const e=[];for(let n=0;n<i;n++)for(let r=0;r<l;r++)e.push(o+n+r*t);o%t+i<=t&&e.every((e=>null==r[e]))?(e.forEach((e=>{r[e]=u})),c=!0):o++}})),[...r]}function we(e,t,n,r,o){if(-1===e)return-1;const u=n.indexOf(e),i=t[e];switch(o){case"tl":return u;case"tr":return i?u+i.width-1:u;case"bl":return i?u+(i.height-1)*r:u;case"br":return n.lastIndexOf(e)}}function Ee(e,t){return t.flatMap(((t,n)=>e.includes(t)?[n]:[]))}function Re(e,t,n){if(n)return n.includes(t);const r=e.current[t];return null==r||r.hasAttribute("disabled")||"true"===r.getAttribute("aria-disabled")}const xe=()=>({getShadowRoot:!0,displayCheck:"function"==typeof ResizeObserver&&ResizeObserver.toString().includes("[native code]")?"full":"none"});function Ie(e,t){const n=H(e,xe()),r=n.length;if(0===r)return;const o=G(J(e)),u=n.indexOf(o);return n[-1===u?1===t?0:r-1:u+t]}function ke(e){return Ie(J(e).body,1)||e}function Ce(e){return Ie(J(e).body,-1)||e}function Me(e,t){const n=t||e.currentTarget,r=e.relatedTarget;return!r||!Z(n,r)}function Oe(e){H(e,xe()).forEach((e=>{e.dataset.tabindex=e.getAttribute("tabindex")||"",e.setAttribute("tabindex","-1")}))}function Se(e){e.querySelectorAll("[data-tabindex]").forEach((e=>{const t=e.dataset.tabindex;delete e.dataset.tabindex,t?e.setAttribute("tabindex",t):e.removeAttribute("tabindex")}))}function Pe(e){const t=u.useRef(void 0),n=u.useCallback((t=>{const n=e.map((e=>{if(null!=e){if("function"==typeof e){const n=e,r=n(t);return"function"==typeof r?r:()=>{n(null)}}return e.current=t,()=>{e.current=null}}}));return()=>{n.forEach((e=>null==e?void 0:e()))}}),e);return u.useMemo((()=>e.every((e=>null==e))?null:e=>{t.current&&(t.current(),t.current=void 0),null!=e&&(t.current=n(e))}),e)}function Te(e,t){const n=e.compareDocumentPosition(t);return n&Node.DOCUMENT_POSITION_FOLLOWING||n&Node.DOCUMENT_POSITION_CONTAINED_BY?-1:n&Node.DOCUMENT_POSITION_PRECEDING||n&Node.DOCUMENT_POSITION_CONTAINS?1:0}const Ae=u.createContext({register:()=>{},unregister:()=>{},map:new Map,elementsRef:{current:[]}});function Le(e){const{children:t,elementsRef:n,labelsRef:r}=e,[o,i]=u.useState((()=>new Set)),l=u.useCallback((e=>{i((t=>new Set(t).add(e)))}),[]),c=u.useCallback((e=>{i((t=>{const n=new Set(t);return n.delete(e),n}))}),[]),s=u.useMemo((()=>{const e=new Map;return Array.from(o.keys()).sort(Te).forEach(((t,n)=>{e.set(t,n)})),e}),[o]);return u.createElement(Ae.Provider,{value:u.useMemo((()=>({register:l,unregister:c,map:s,elementsRef:n,labelsRef:r})),[l,c,s,n,r])},t)}function Ne(e){void 0===e&&(e={});const{label:t}=e,{register:n,unregister:r,map:o,elementsRef:i,labelsRef:l}=u.useContext(Ae),[c,s]=u.useState(null),a=u.useRef(null),f=u.useCallback((e=>{if(a.current=e,null!==c&&(i.current[c]=e,l)){var n;const r=void 0!==t;l.current[c]=r?t:null!=(n=null==e?void 0:e.textContent)?n:null}}),[c,i,l,t]);return se((()=>{const e=a.current;if(e)return n(e),()=>{r(e)}}),[n,r]),se((()=>{const e=a.current?o.get(a.current):null;null!=e&&s(e)}),[o]),u.useMemo((()=>({ref:f,index:null==c?-1:c})),[c,f])}const De="data-floating-ui-focusable",Fe="active",je="selected",Ke="ArrowLeft",He="ArrowRight",qe="ArrowUp",_e="ArrowDown";function We(e,t){return"function"==typeof e?e(t):e?u.cloneElement(e,t):u.createElement("div",t)}const Ue=u.createContext({activeIndex:0,onNavigate:()=>{}}),Be=[Ke,He],ze=[qe,_e],Xe=[...Be,...ze],Ye=u.forwardRef((function(e,t){const{render:n,orientation:r="both",loop:o=!0,rtl:i=!1,cols:l=1,disabledIndices:c,activeIndex:s,onNavigate:a,itemSizes:f,dense:d=!1,...v}=e,[m,p]=u.useState(0),g=null!=s?s:m,h=de(null!=a?a:p),y=u.useRef([]),b=n&&"function"!=typeof n?n.props:{},w=u.useMemo((()=>({activeIndex:g,onNavigate:h})),[g,h]),E=l>1;const R={...v,...b,ref:t,"aria-orientation":"both"===r?void 0:r,onKeyDown(e){null==v.onKeyDown||v.onKeyDown(e),null==b.onKeyDown||b.onKeyDown(e),function(e){if(!Xe.includes(e.key))return;let t=g;const n=pe(y,c),u=ge(y,c),s=i?Ke:He,a=i?He:Ke;if(E){const a=f||Array.from({length:y.current.length},(()=>({width:1,height:1}))),v=be(a,l,d),m=v.findIndex((e=>null!=e&&!Re(y,e,c))),p=v.reduce(((e,t,n)=>null==t||Re(y,t,c)?e:n),-1),h=v[ye({current:v.map((e=>e?y.current[e]:null))},{event:e,orientation:r,loop:o,rtl:i,cols:l,disabledIndices:Ee([...c||y.current.map(((e,t)=>Re(y,t)?t:void 0)),void 0],v),minIndex:m,maxIndex:p,prevIndex:we(g>u?n:g,a,v,l,e.key===_e?"bl":e.key===s?"tr":"tl")})];null!=h&&(t=h)}const v={horizontal:[s],vertical:[_e],both:[s,_e]}[r],m={horizontal:[a],vertical:[qe],both:[a,qe]}[r],p=E?Xe:{horizontal:Be,vertical:ze,both:Xe}[r];var b;t===g&&[...v,...m].includes(e.key)&&(t=o&&t===u&&v.includes(e.key)?n:o&&t===n&&m.includes(e.key)?u:he(y,{startingIndex:t,decrement:m.includes(e.key),disabledIndices:c})),t===g||me(y,t)||(e.stopPropagation(),p.includes(e.key)&&e.preventDefault(),h(t),null==(b=y.current[t])||b.focus())}(e)}};return u.createElement(Ue.Provider,{value:w},u.createElement(Le,{elementsRef:y},We(n,R)))})),Ve=u.forwardRef((function(e,t){const{render:n,...r}=e,o=n&&"function"!=typeof n?n.props:{},{activeIndex:i,onNavigate:l}=u.useContext(Ue),{ref:c,index:s}=Ne(),a=Pe([c,t,o.ref]),f=i===s;return We(n,{...r,...o,ref:a,tabIndex:f?0:-1,"data-active":f?"":void 0,onFocus(e){null==r.onFocus||r.onFocus(e),null==o.onFocus||o.onFocus(e),l(s)}})}));function Ge(){return Ge=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)({}).hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Ge.apply(null,arguments)}const Ze={...u};let $e=!1,Qe=0;const Je=()=>"floating-ui-"+Math.random().toString(36).slice(2,6)+Qe++;const et=Ze.useId||function(){const[e,t]=u.useState((()=>$e?Je():void 0));return se((()=>{null==e&&t(Je())}),[]),u.useEffect((()=>{$e=!0}),[]),e},tt=u.forwardRef((function(e,t){const{context:{placement:n,elements:{floating:r},middlewareData:{arrow:o,shift:i}},width:l=14,height:c=7,tipRadius:s=0,strokeWidth:a=0,staticOffset:f,stroke:d,d:v,style:{transform:m,...g}={},...h}=e,y=et(),[b,w]=u.useState(!1);if(se((()=>{if(!r)return;"rtl"===p(r).direction&&w(!0)}),[r]),!r)return null;const[E,R]=n.split("-"),x="top"===E||"bottom"===E;let I=f;(x&&null!=i&&i.x||!x&&null!=i&&i.y)&&(I=null);const k=2*a,C=k/2,M=l/2*(s/-8+1),O=c/2*s/4,S=!!v,P=I&&"end"===R?"bottom":"top";let T=I&&"end"===R?"right":"left";I&&b&&(T="end"===R?"left":"right");const A=null!=(null==o?void 0:o.x)?I||o.x:"",L=null!=(null==o?void 0:o.y)?I||o.y:"",N=v||"M0,0 H"+l+" L"+(l-M)+","+(c-O)+" Q"+l/2+","+c+" "+M+","+(c-O)+" Z",D={top:S?"rotate(180deg)":"",left:S?"rotate(90deg)":"rotate(-90deg)",bottom:S?"":"rotate(180deg)",right:S?"rotate(-90deg)":"rotate(90deg)"}[E];return u.createElement("svg",Ge({},h,{"aria-hidden":!0,ref:t,width:S?l:l+k,height:l,viewBox:"0 0 "+l+" "+(c>l?c:l),style:{position:"absolute",pointerEvents:"none",[T]:A,[P]:L,[E]:x||S?"100%":"calc(100% - "+k/2+"px)",transform:[D,m].filter((e=>!!e)).join(" "),...g}}),k>0&&u.createElement("path",{clipPath:"url(#"+y+")",fill:"none",stroke:d,strokeWidth:k+(v?0:1),d:N}),u.createElement("path",{stroke:k&&!v?h.fill:"none",d:N}),u.createElement("clipPath",{id:y},u.createElement("rect",{x:-C,y:C*(S?-1:1),width:l+k,height:l})))}));function nt(){const e=new Map;return{emit(t,n){var r;null==(r=e.get(t))||r.forEach((e=>e(n)))},on(t,n){e.has(t)||e.set(t,new Set),e.get(t).add(n)},off(t,n){var r;null==(r=e.get(t))||r.delete(n)}}}const rt=u.createContext(null),ot=u.createContext(null),ut=()=>{var e;return(null==(e=u.useContext(rt))?void 0:e.id)||null},it=()=>u.useContext(ot);function lt(e){return"data-floating-ui-"+e}function ct(e){-1!==e.current&&(clearTimeout(e.current),e.current=-1)}const st=lt("safe-polygon");function at(e,t,n){if(n&&!ce(n))return 0;if("number"==typeof e)return e;if("function"==typeof e){const n=e();return"number"==typeof n?n:null==n?void 0:n[t]}return null==e?void 0:e[t]}function ft(e){return"function"==typeof e?e():e}const dt=()=>{},vt=u.createContext({delay:0,initialDelay:0,timeoutMs:0,currentId:null,setCurrentId:dt,setState:dt,isInstantPhase:!1}),mt=()=>u.useContext(vt);const pt=u.createContext({hasProvider:!1,timeoutMs:0,delayRef:{current:0},initialDelayRef:{current:0},timeoutIdRef:{current:-1},currentIdRef:{current:null},currentContextRef:{current:null}});let gt=0;function ht(e,t){void 0===t&&(t={});const{preventScroll:n=!1,cancelPrevious:r=!0,sync:o=!1}=t;r&&cancelAnimationFrame(gt);const u=()=>null==e?void 0:e.focus({preventScroll:n});o?u():gt=requestAnimationFrame(u)}function yt(e,t){if(!e||!t)return!1;const n=null==t.getRootNode?void 0:t.getRootNode();if(e.contains(t))return!0;if(n&&v(n)){let n=t;for(;n;){if(e===n)return!0;n=n.parentNode||n.host}}return!1}let bt=new WeakMap,wt=new WeakSet,Et={},Rt=0;const xt=e=>e&&(e.host||xt(e.parentNode));function It(e,t,n,r){const o="data-floating-ui-inert",u=r?"inert":n?"aria-hidden":null,i=(l=t,e.map((e=>{if(l.contains(e))return e;const t=xt(e);return l.contains(t)?t:null})).filter((e=>null!=e)));var l;const s=new Set,a=new Set(i),f=[];Et[o]||(Et[o]=new WeakMap);const d=Et[o];return i.forEach((function e(t){if(!t||s.has(t))return;s.add(t),t.parentNode&&e(t.parentNode)})),function e(t){if(!t||a.has(t))return;[].forEach.call(t.children,(t=>{if("script"!==c(t))if(s.has(t))e(t);else{const e=u?t.getAttribute(u):null,n=null!==e&&"false"!==e,r=bt.get(t)||0,i=u?r+1:r,l=(d.get(t)||0)+1;bt.set(t,i),d.set(t,l),f.push(t),1===i&&n&&wt.add(t),1===l&&t.setAttribute(o,""),!n&&u&&t.setAttribute(u,"inert"===u?"":"true")}}))}(t),s.clear(),Rt++,()=>{f.forEach((e=>{const t=bt.get(e)||0,n=u?t-1:t,r=(d.get(e)||0)-1;bt.set(e,n),d.set(e,r),n||(!wt.has(e)&&u&&e.removeAttribute(u),wt.delete(e)),r||e.removeAttribute(o)})),Rt--,Rt||(bt=new WeakMap,bt=new WeakMap,wt=new WeakSet,Et={})}}function kt(e,t,n){void 0===t&&(t=!1),void 0===n&&(n=!1);const r=(o=e[0],(null==o?void 0:o.ownerDocument)||document).body;var o;return It(e.concat(Array.from(r.querySelectorAll("[aria-live]"))),r,t,n)}const Ct={border:0,clip:"rect(0 0 0 0)",height:"1px",margin:"-1px",overflow:"hidden",padding:0,position:"fixed",whiteSpace:"nowrap",width:"1px",top:0,left:0},Mt=u.forwardRef((function(e,t){const[n,r]=u.useState();se((()=>{U()&&r("button")}),[]);const o={ref:t,tabIndex:0,role:n,"aria-hidden":!n||void 0,[lt("focus-guard")]:"",style:Ct};return u.createElement("span",Ge({},e,o))})),Ot=u.createContext(null),St=lt("portal");function Pt(e){void 0===e&&(e={});const{id:t,root:n}=e,r=et(),o=Tt(),[i,l]=u.useState(null),c=u.useRef(null);return se((()=>()=>{null==i||i.remove(),queueMicrotask((()=>{c.current=null}))}),[i]),se((()=>{if(!r)return;if(c.current)return;const e=t?document.getElementById(t):null;if(!e)return;const n=document.createElement("div");n.id=r,n.setAttribute(St,""),e.appendChild(n),c.current=n,l(n)}),[t,r]),se((()=>{if(null===n)return;if(!r)return;if(c.current)return;let e=n||(null==o?void 0:o.portalNode);e&&!f(e)&&(e=e.current),e=e||document.body;let u=null;t&&(u=document.createElement("div"),u.id=t,e.appendChild(u));const i=document.createElement("div");i.id=r,i.setAttribute(St,""),e=u||e,e.appendChild(i),c.current=i,l(i)}),[t,n,r,o]),i}const Tt=()=>u.useContext(Ot);function At(e){return u.useMemo((()=>t=>{e.forEach((e=>{e&&(e.current=t)}))}),e)}let Lt=[];function Nt(){return Lt.slice().reverse().find((e=>e.isConnected))}function Dt(e,t){var n;if(!(t.current.includes("floating")||null!=(n=e.getAttribute("role"))&&n.includes("dialog")))return;const r=xe(),o=function(e,t){return(t=t||{}).getShadowRoot?M([e],t.includeContainer,{filter:D.bind(null,t),flatten:!0,getShadowRoot:t.getShadowRoot}):C(e,t.includeContainer,D.bind(null,t))}(e,r),u=o.filter((e=>{const t=e.getAttribute("data-tabindex")||"";return q(e,r)||e.hasAttribute("data-tabindex")&&!t.startsWith("-")})),i=e.getAttribute("tabindex");t.current.includes("floating")||0===u.length?"0"!==i&&e.setAttribute("tabindex","0"):("-1"!==i||e.hasAttribute("data-tabindex")&&"-1"!==e.getAttribute("data-tabindex"))&&(e.setAttribute("tabindex","-1"),e.setAttribute("data-tabindex","-1"))}const Ft=u.forwardRef((function(e,t){return u.createElement("button",Ge({},e,{type:"button",ref:t,tabIndex:-1,style:Ct}))}));let jt=0;let Kt=()=>{};const Ht=u.forwardRef((function(e,t){const{lockScroll:n=!1,...r}=e;return se((()=>{if(n)return jt++,1===jt&&(Kt=function(){const e=/iP(hone|ad|od)|iOS/.test(_()),t=document.body.style,n=Math.round(document.documentElement.getBoundingClientRect().left)+document.documentElement.scrollLeft?"paddingLeft":"paddingRight",r=window.innerWidth-document.documentElement.clientWidth,o=t.left?parseFloat(t.left):window.scrollX,u=t.top?parseFloat(t.top):window.scrollY;if(t.overflow="hidden",r&&(t[n]=r+"px"),e){var i,l;const e=(null==(i=window.visualViewport)?void 0:i.offsetLeft)||0,n=(null==(l=window.visualViewport)?void 0:l.offsetTop)||0;Object.assign(t,{position:"fixed",top:-(u-Math.floor(n))+"px",left:-(o-Math.floor(e))+"px",right:"0"})}return()=>{Object.assign(t,{overflow:"",[n]:""}),e&&(Object.assign(t,{position:"",top:"",left:"",right:""}),window.scrollTo(o,u))}}()),()=>{jt--,0===jt&&Kt()}}),[n]),u.createElement("div",Ge({ref:t},r,{style:{position:"fixed",overflow:"auto",top:0,right:0,bottom:0,left:0,...r.style}}))}));function qt(e){return d(e.target)&&"BUTTON"===e.target.tagName}function _t(e){return ee(e)}function Wt(e){return null!=e&&null!=e.clientX}const Ut={pointerdown:"onPointerDown",mousedown:"onMouseDown",click:"onClick"},Bt={pointerdown:"onPointerDownCapture",mousedown:"onMouseDownCapture",click:"onClickCapture"},zt=e=>{var t,n;return{escapeKey:"boolean"==typeof e?e:null!=(t=null==e?void 0:e.escapeKey)&&t,outsidePress:"boolean"==typeof e?e:null==(n=null==e?void 0:e.outsidePress)||n}};function Xt(e){const{open:t=!1,onOpenChange:n,elements:r}=e,o=et(),i=u.useRef({}),[l]=u.useState((()=>nt())),c=null!=ut(),[s,a]=u.useState(r.reference),f=de(((e,t,r)=>{i.current.openEvent=e?t:void 0,l.emit("openchange",{open:e,event:t,reason:r,nested:c}),null==n||n(e,t,r)})),d=u.useMemo((()=>({setPositionReference:a})),[]),v=u.useMemo((()=>({reference:s||r.reference||null,floating:r.floating||null,domReference:r.reference})),[s,r.reference,r.floating]);return u.useMemo((()=>({dataRef:i,open:t,onOpenChange:f,elements:v,events:l,floatingId:o,refs:d})),[t,f,v,l,o,d])}function Yt(){return _().toLowerCase().startsWith("mac")&&!navigator.maxTouchPoints&&U()}function Vt(e,t,n){const r=new Map,o="item"===n;let u=e;if(o&&e){const{[Fe]:t,[je]:n,...r}=e;u=r}return{..."floating"===n&&{tabIndex:-1,[De]:""},...u,...t.map((t=>{const r=t?t[n]:null;return"function"==typeof r?e?r(e):null:r})).concat(e).reduce(((e,t)=>t?(Object.entries(t).forEach((t=>{let[n,u]=t;var i;o&&[Fe,je].includes(n)||(0===n.indexOf("on")?(r.has(n)||r.set(n,[]),"function"==typeof u&&(null==(i=r.get(n))||i.push(u),e[n]=function(){for(var e,t=arguments.length,o=new Array(t),u=0;u<t;u++)o[u]=arguments[u];return null==(e=r.get(n))?void 0:e.map((e=>e(...o))).find((e=>void 0!==e))})):e[n]=u)})),e):e),{})}}function Gt(e,t,n){switch(e){case"vertical":return t;case"horizontal":return n;default:return t||n}}function Zt(e,t){return Gt(t,e===qe||e===_e,e===Ke||e===He)}function $t(e,t,n){return Gt(t,e===_e,n?e===Ke:e===He)||"Enter"===e||" "===e||""===e}function Qt(e,t,n){return Gt(t,n?e===Ke:e===He,e===_e)}function Jt(e,t,n,r){return"both"===t||"horizontal"===t&&r&&r>1?"Escape"===e:Gt(t,n?e===He:e===Ke,e===qe)}const en=new Map([["select","listbox"],["combobox","listbox"],["label",!1]]);const tn=e=>e.replace(/[A-Z]+(?![a-z])|[A-Z]/g,((e,t)=>(t?"-":"")+e.toLowerCase()));function nn(e,t){return"function"==typeof e?e(t):e}function rn(e,t){void 0===t&&(t={});const{open:n,elements:{floating:r}}=e,{duration:o=250}=t,l=("number"==typeof o?o:o.close)||0,[c,s]=u.useState("unmounted"),a=function(e,t){const[n,r]=u.useState(e);return e&&!n&&r(!0),u.useEffect((()=>{if(!e&&n){const e=setTimeout((()=>r(!1)),t);return()=>clearTimeout(e)}}),[e,n,t]),n}(n,l);return a||"close"!==c||s("unmounted"),se((()=>{if(r){if(n){s("initial");const e=requestAnimationFrame((()=>{i.flushSync((()=>{s("open")}))}));return()=>{cancelAnimationFrame(e)}}s("close")}}),[n,r]),{isMounted:a,status:c}}function on(e,t){return{...e,rects:{...e.rects,floating:{...e.rects.floating,height:t}}}}function un(e,t){const[n,r]=e;let o=!1;const u=t.length;for(let e=0,i=u-1;e<u;i=e++){const[u,l]=t[e]||[0,0],[c,s]=t[i]||[0,0];l>=r!=s>=r&&n<=(c-u)*(r-l)/(s-l)+u&&(o=!o)}return o}Object.defineProperty(e,"arrow",{enumerable:!0,get:function(){return r.arrow}}),Object.defineProperty(e,"autoPlacement",{enumerable:!0,get:function(){return r.autoPlacement}}),Object.defineProperty(e,"autoUpdate",{enumerable:!0,get:function(){return r.autoUpdate}}),Object.defineProperty(e,"computePosition",{enumerable:!0,get:function(){return r.computePosition}}),Object.defineProperty(e,"detectOverflow",{enumerable:!0,get:function(){return r.detectOverflow}}),Object.defineProperty(e,"flip",{enumerable:!0,get:function(){return r.flip}}),Object.defineProperty(e,"getOverflowAncestors",{enumerable:!0,get:function(){return r.getOverflowAncestors}}),Object.defineProperty(e,"hide",{enumerable:!0,get:function(){return r.hide}}),Object.defineProperty(e,"inline",{enumerable:!0,get:function(){return r.inline}}),Object.defineProperty(e,"limitShift",{enumerable:!0,get:function(){return r.limitShift}}),Object.defineProperty(e,"offset",{enumerable:!0,get:function(){return r.offset}}),Object.defineProperty(e,"platform",{enumerable:!0,get:function(){return r.platform}}),Object.defineProperty(e,"shift",{enumerable:!0,get:function(){return r.shift}}),Object.defineProperty(e,"size",{enumerable:!0,get:function(){return r.size}}),e.Composite=Ye,e.CompositeItem=Ve,e.FloatingArrow=tt,e.FloatingDelayGroup=function(e){const{children:t,delay:n,timeoutMs:r=0}=e,[o,i]=u.useReducer(((e,t)=>({...e,...t})),{delay:n,timeoutMs:r,initialDelay:n,currentId:null,isInstantPhase:!1}),l=u.useRef(null),c=u.useCallback((e=>{i({currentId:e})}),[]);return se((()=>{o.currentId?null===l.current?l.current=o.currentId:o.isInstantPhase||i({isInstantPhase:!0}):(o.isInstantPhase&&i({isInstantPhase:!1}),l.current=null)}),[o.currentId,o.isInstantPhase]),u.createElement(vt.Provider,{value:u.useMemo((()=>({...o,setState:i,setCurrentId:c})),[o,c])},t)},e.FloatingFocusManager=function(e){const{context:t,children:n,disabled:r=!1,order:o=["content"],guards:i=!0,initialFocus:l=0,returnFocus:s=!0,restoreFocus:a=!1,modal:f=!0,visuallyHiddenDismiss:v=!1,closeOnFocusOut:m=!0,outsideElementsInert:p=!1,getInsideElements:g=()=>[]}=e,{open:h,onOpenChange:y,events:b,dataRef:w,elements:{domReference:E,floating:R}}=t,x=de((()=>{var e;return null==(e=w.current.floatingContext)?void 0:e.nodeId})),I=de(g),k="number"==typeof l&&l<0,C=te(E)&&k,M="undefined"!=typeof HTMLElement&&"inert"in HTMLElement.prototype,O=!M||i,S=!O||M&&p,P=ae(o),T=ae(l),A=ae(s),L=it(),N=Tt(),D=u.useRef(null),F=u.useRef(null),j=u.useRef(!1),K=u.useRef(!1),_=u.useRef(-1),W=null!=N,U=ne(R),B=de((function(e){return void 0===e&&(e=U),e?H(e,xe()):[]})),z=de((e=>{const t=B(e);return P.current.map((e=>E&&"reference"===e?E:U&&"floating"===e?U:t)).filter(Boolean).flat()}));u.useEffect((()=>{if(r)return;if(!f)return;function e(e){if("Tab"===e.key){Z(U,G(J(U)))&&0===B().length&&!C&&ue(e);const t=z(),n=$(e);"reference"===P.current[0]&&n===E&&(ue(e),e.shiftKey?ht(t[t.length-1]):ht(t[1])),"floating"===P.current[1]&&n===U&&e.shiftKey&&(ue(e),ht(t[0]))}}const t=J(U);return t.addEventListener("keydown",e),()=>{t.removeEventListener("keydown",e)}}),[r,E,U,f,P,C,B,z]),u.useEffect((()=>{if(!r&&R)return R.addEventListener("focusin",e),()=>{R.removeEventListener("focusin",e)};function e(e){const t=$(e),n=B().indexOf(t);-1!==n&&(_.current=n)}}),[r,R,B]),u.useEffect((()=>{if(!r&&m)return R&&d(E)?(E.addEventListener("focusout",t),E.addEventListener("pointerdown",e),R.addEventListener("focusout",t),()=>{E.removeEventListener("focusout",t),E.removeEventListener("pointerdown",e),R.removeEventListener("focusout",t)}):void 0;function e(){K.current=!0,setTimeout((()=>{K.current=!1}))}function t(e){const t=e.relatedTarget,n=e.currentTarget;queueMicrotask((()=>{const r=x(),o=!(Z(E,t)||Z(R,t)||Z(t,R)||Z(null==N?void 0:N.portalNode,t)||null!=t&&t.hasAttribute(lt("focus-guard"))||L&&(re(L.nodesRef.current,r).find((e=>{var n,r;return Z(null==(n=e.context)?void 0:n.elements.floating,t)||Z(null==(r=e.context)?void 0:r.elements.domReference,t)}))||oe(L.nodesRef.current,r).find((e=>{var n,r,o;return[null==(n=e.context)?void 0:n.elements.floating,ne(null==(r=e.context)?void 0:r.elements.floating)].includes(t)||(null==(o=e.context)?void 0:o.elements.domReference)===t}))));if(n===E&&U&&Dt(U,P),a&&o&&G(J(U))===J(U).body){d(U)&&U.focus();const e=_.current,t=B(),n=t[e]||t[t.length-1]||U;d(n)&&n.focus()}!C&&f||!t||!o||K.current||t===Nt()||(j.current=!0,y(!1,e,"focus-out"))}))}}),[r,E,R,U,f,L,N,y,m,a,B,C,x,P]);const X=u.useRef(null),Y=u.useRef(null),V=At([X,null==N?void 0:N.beforeInsideRef]),Q=At([Y,null==N?void 0:N.afterInsideRef]);function ee(e){return!r&&v&&f?u.createElement(Ft,{ref:"start"===e?D:F,onClick:e=>y(!1,e.nativeEvent)},"string"==typeof v?v:"Dismiss"):null}u.useEffect((()=>{var e,t;if(r)return;if(!R)return;const n=Array.from((null==N||null==(e=N.portalNode)?void 0:e.querySelectorAll("["+lt("portal")+"]"))||[]),o=L?oe(L.nodesRef.current,x()):[],u=L&&!f?o.map((e=>{var t;return null==(t=e.context)?void 0:t.elements.floating})):[],i=null==(t=o.find((e=>{var t;return te((null==(t=e.context)?void 0:t.elements.domReference)||null)})))||null==(t=t.context)?void 0:t.elements.domReference,l=[R,i,...n,...u,...I(),D.current,F.current,X.current,Y.current,null==N?void 0:N.beforeOutsideRef.current,null==N?void 0:N.afterOutsideRef.current,P.current.includes("reference")||C?E:null].filter((e=>null!=e)),c=f||C?kt(l,!S,S):kt(l);return()=>{c()}}),[r,E,R,f,P,N,C,O,S,L,x,I]),se((()=>{if(r||!d(U))return;const e=G(J(U));queueMicrotask((()=>{const t=z(U),n=T.current,r=("number"==typeof n?t[n]:n.current)||U,o=Z(U,e);k||o||!h||ht(r,{preventScroll:r===U})}))}),[r,h,U,k,z,T]),se((()=>{if(r||!U)return;let e=!1;const t=J(U),n=G(t);var o;function u(t){let{reason:n,event:r,nested:o}=t;if(["hover","safe-polygon"].includes(n)&&"mouseleave"===r.type&&(j.current=!0),"outside-press"===n)if(o)j.current=!1,e=!0;else if(ie(r)||le(r))j.current=!1;else{let t=!1;document.createElement("div").focus({get preventScroll(){return t=!0,!1}}),t?(j.current=!1,e=!0):j.current=!0}}o=n,Lt=Lt.filter((e=>e.isConnected)),o&&"body"!==c(o)&&(Lt.push(o),Lt.length>20&&(Lt=Lt.slice(-20))),b.on("openchange",u);const i=t.createElement("span");return i.setAttribute("tabindex","-1"),i.setAttribute("aria-hidden","true"),Object.assign(i.style,Ct),W&&E&&E.insertAdjacentElement("afterend",i),()=>{b.off("openchange",u);const n=G(t),r=Z(R,n)||L&&re(L.nodesRef.current,x()).some((e=>{var t;return Z(null==(t=e.context)?void 0:t.elements.floating,n)})),o=function(){if("boolean"==typeof A.current){const e=E||Nt();return e&&e.isConnected?e:i}return A.current.current||i}();queueMicrotask((()=>{const u=function(e){const t=xe();return q(e,t)?e:H(e,t)[0]||e}(o);A.current&&!j.current&&d(u)&&(u===n||n===t.body||r)&&u.focus({preventScroll:e}),i.remove()}))}}),[r,R,U,A,w,b,L,W,E,x]),u.useEffect((()=>{queueMicrotask((()=>{j.current=!1}))}),[r]),se((()=>{if(!r&&N)return N.setFocusManagerState({modal:f,closeOnFocusOut:m,open:h,onOpenChange:y,domReference:E}),()=>{N.setFocusManagerState(null)}}),[r,N,f,h,y,m,E]),se((()=>{r||U&&Dt(U,P)}),[r,U,P]);const ce=!r&&O&&(!f||!C)&&(W||f);return u.createElement(u.Fragment,null,ce&&u.createElement(Mt,{"data-type":"inside",ref:V,onFocus:e=>{if(f){const e=z();ht("reference"===o[0]?e[0]:e[e.length-1])}else if(null!=N&&N.preserveTabOrder&&N.portalNode)if(j.current=!1,Me(e,N.portalNode)){const e=ke(E);null==e||e.focus()}else{var t;null==(t=N.beforeOutsideRef.current)||t.focus()}}}),!C&&ee("start"),n,ee("end"),ce&&u.createElement(Mt,{"data-type":"inside",ref:Q,onFocus:e=>{if(f)ht(z()[0]);else if(null!=N&&N.preserveTabOrder&&N.portalNode)if(m&&(j.current=!0),Me(e,N.portalNode)){const e=Ce(E);null==e||e.focus()}else{var t;null==(t=N.afterOutsideRef.current)||t.focus()}}}))},e.FloatingList=Le,e.FloatingNode=function(e){const{children:t,id:n}=e,r=ut();return u.createElement(rt.Provider,{value:u.useMemo((()=>({id:n,parentId:r})),[n,r])},t)},e.FloatingOverlay=Ht,e.FloatingPortal=function(e){const{children:t,id:n,root:r,preserveTabOrder:o=!0}=e,l=Pt({id:n,root:r}),[c,s]=u.useState(null),a=u.useRef(null),f=u.useRef(null),d=u.useRef(null),v=u.useRef(null),m=null==c?void 0:c.modal,p=null==c?void 0:c.open,g=!!c&&!c.modal&&c.open&&o&&!(!r&&!l);return u.useEffect((()=>{if(l&&o&&!m)return l.addEventListener("focusin",e,!0),l.addEventListener("focusout",e,!0),()=>{l.removeEventListener("focusin",e,!0),l.removeEventListener("focusout",e,!0)};function e(e){if(l&&Me(e)){("focusin"===e.type?Se:Oe)(l)}}}),[l,o,m]),u.useEffect((()=>{l&&(p||Se(l))}),[p,l]),u.createElement(Ot.Provider,{value:u.useMemo((()=>({preserveTabOrder:o,beforeOutsideRef:a,afterOutsideRef:f,beforeInsideRef:d,afterInsideRef:v,portalNode:l,setFocusManagerState:s})),[o,l])},g&&l&&u.createElement(Mt,{"data-type":"outside",ref:a,onFocus:e=>{if(Me(e,l)){var t;null==(t=d.current)||t.focus()}else{const e=Ce(c?c.domReference:null);null==e||e.focus()}}}),g&&l&&u.createElement("span",{"aria-owns":l.id,style:Ct}),l&&i.createPortal(t,l),g&&l&&u.createElement(Mt,{"data-type":"outside",ref:f,onFocus:e=>{if(Me(e,l)){var t;null==(t=v.current)||t.focus()}else{const t=ke(c?c.domReference:null);null==t||t.focus(),(null==c?void 0:c.closeOnFocusOut)&&(null==c||c.onOpenChange(!1,e.nativeEvent,"focus-out"))}}}))},e.FloatingTree=function(e){const{children:t}=e,n=u.useRef([]),r=u.useCallback((e=>{n.current=[...n.current,e]}),[]),o=u.useCallback((e=>{n.current=n.current.filter((t=>t!==e))}),[]),[i]=u.useState((()=>nt()));return u.createElement(ot.Provider,{value:u.useMemo((()=>({nodesRef:n,addNode:r,removeNode:o,events:i})),[r,o,i])},t)},e.NextFloatingDelayGroup=function(e){const{children:t,delay:n,timeoutMs:r=0}=e,o=u.useRef(n),i=u.useRef(n),l=u.useRef(null),c=u.useRef(null),s=u.useRef(-1);return u.createElement(pt.Provider,{value:u.useMemo((()=>({hasProvider:!0,delayRef:o,initialDelayRef:i,currentIdRef:l,timeoutMs:r,currentContextRef:c,timeoutIdRef:s})),[r])},t)},e.inner=e=>({name:"inner",options:e,async fn(t){const{listRef:n,overflowRef:o,onFallbackChange:u,offset:l=0,index:c=0,minItemsVisible:s=4,referenceOverflowThreshold:a=0,scrollRef:f,...d}=(m=t,"function"==typeof(v=e)?v(m):v);var v,m;const{rects:p,elements:{floating:g}}=t,w=n.current[c],E=(null==f?void 0:f.current)||g,R=g.clientTop||E.clientTop,x=0!==g.clientTop,I=0!==E.clientTop,k=g===E;if(!w)return{};const C={...t,...await r.offset(-w.offsetTop-g.clientTop-p.reference.height/2-w.offsetHeight/2-l).fn(t)},M=await r.detectOverflow(on(C,E.scrollHeight+R+g.clientTop),d),O=await r.detectOverflow(C,{...d,elementContext:"reference"}),S=y(0,M.top),P=C.y+S,T=(E.scrollHeight>E.clientHeight?e=>e:b)(y(0,E.scrollHeight+(x&&k||I?2*R:0)-S-y(0,M.bottom)));if(E.style.maxHeight=T+"px",E.scrollTop=S,u){const e=E.offsetHeight<w.offsetHeight*h(s,n.current.length)-1||O.top>=-a||O.bottom>=-a;i.flushSync((()=>u(e)))}return o&&(o.current=await r.detectOverflow(on({...C,y:P},E.offsetHeight+R+g.clientTop),d)),{y:P}}}),e.safePolygon=function(e){void 0===e&&(e={});const{buffer:t=.5,blockPointerEvents:n=!1,requireIntent:r=!0}=e;let o,u=!1,i=null,l=null,c=performance.now();const s=e=>{let{x:n,y:s,placement:a,elements:d,onClose:v,nodeId:m,tree:p}=e;return function(e){function g(){clearTimeout(o),v()}if(clearTimeout(o),!d.domReference||!d.floating||null==a||null==n||null==s)return;const{clientX:h,clientY:y}=e,b=[h,y],w=function(e){return"composedPath"in e?e.composedPath()[0]:e.target}(e),E="mouseleave"===e.type,R=yt(d.floating,w),x=yt(d.domReference,w),I=d.domReference.getBoundingClientRect(),k=d.floating.getBoundingClientRect(),C=a.split("-")[0],M=n>k.right-k.width/2,O=s>k.bottom-k.height/2,S=function(e,t){return e[0]>=t.x&&e[0]<=t.x+t.width&&e[1]>=t.y&&e[1]<=t.y+t.height}(b,I),P=k.width>I.width,T=k.height>I.height,A=(P?I:k).left,L=(P?I:k).right,N=(T?I:k).top,D=(T?I:k).bottom;if(R&&(u=!0,!E))return;if(x&&(u=!1),x&&!E)return void(u=!0);if(E&&f(e.relatedTarget)&&yt(d.floating,e.relatedTarget))return;if(p&&function(e,t,n){void 0===n&&(n=!0);let r=e.filter((e=>{var n;return e.parentId===t&&(null==(n=e.context)?void 0:n.open)})),o=r;for(;o.length;)o=n?e.filter((e=>{var t;return null==(t=o)?void 0:t.some((t=>{var n;return e.parentId===t.id&&(null==(n=e.context)?void 0:n.open)}))})):e,r=r.concat(o);return r}(p.nodesRef.current,m).some((e=>{let{context:t}=e;return null==t?void 0:t.open})))return;if("top"===C&&s>=I.bottom-1||"bottom"===C&&s<=I.top+1||"left"===C&&n>=I.right-1||"right"===C&&n<=I.left+1)return g();let F=[];switch(C){case"top":F=[[A,I.top+1],[A,k.bottom-1],[L,k.bottom-1],[L,I.top+1]];break;case"bottom":F=[[A,k.top+1],[A,I.bottom-1],[L,I.bottom-1],[L,k.top+1]];break;case"left":F=[[k.right-1,D],[k.right-1,N],[I.left+1,N],[I.left+1,D]];break;case"right":F=[[I.right-1,D],[I.right-1,N],[k.left+1,N],[k.left+1,D]]}if(!un([h,y],F)){if(u&&!S)return g();if(!E&&r){const t=function(e,t){const n=performance.now(),r=n-c;if(null===i||null===l||0===r)return i=e,l=t,c=n,null;const o=e-i,u=t-l,s=Math.sqrt(o*o+u*u);return i=e,l=t,c=n,s/r}(e.clientX,e.clientY);if(null!==t&&t<.1)return g()}un([h,y],function(e){let[n,r]=e;switch(C){case"top":return[[P?n+t/2:M?n+4*t:n-4*t,r+t+1],[P?n-t/2:M?n+4*t:n-4*t,r+t+1],...[[k.left,M||P?k.bottom-t:k.top],[k.right,M?P?k.bottom-t:k.top:k.bottom-t]]];case"bottom":return[[P?n+t/2:M?n+4*t:n-4*t,r-t],[P?n-t/2:M?n+4*t:n-4*t,r-t],...[[k.left,M||P?k.top+t:k.bottom],[k.right,M?P?k.top+t:k.bottom:k.top+t]]];case"left":{const e=[n+t+1,T?r+t/2:O?r+4*t:r-4*t],o=[n+t+1,T?r-t/2:O?r+4*t:r-4*t];return[...[[O||T?k.right-t:k.left,k.top],[O?T?k.right-t:k.left:k.right-t,k.bottom]],e,o]}case"right":return[[n-t,T?r+t/2:O?r+4*t:r-4*t],[n-t,T?r-t/2:O?r+4*t:r-4*t],...[[O||T?k.left+t:k.right,k.top],[O?T?k.left+t:k.right:k.left+t,k.bottom]]]}}([n,s]))?!u&&r&&(o=window.setTimeout(g,40)):g()}}};return s.__options={blockPointerEvents:n},s},e.useClick=function(e,t){void 0===t&&(t={});const{open:n,onOpenChange:r,dataRef:o,elements:{domReference:i}}=e,{enabled:l=!0,event:c="click",toggle:s=!0,ignoreMouse:a=!1,keyboardHandlers:f=!0,stickIfOpen:v=!0}=t,m=u.useRef(),p=u.useRef(!1),g=u.useMemo((()=>({onPointerDown(e){m.current=e.pointerType},onMouseDown(e){const t=m.current;0===e.button&&"click"!==c&&(ce(t,!0)&&a||(!n||!s||o.current.openEvent&&v&&"mousedown"!==o.current.openEvent.type?(e.preventDefault(),r(!0,e.nativeEvent,"click")):r(!1,e.nativeEvent,"click")))},onClick(e){const t=m.current;"mousedown"===c&&m.current?m.current=void 0:ce(t,!0)&&a||(!n||!s||o.current.openEvent&&v&&"click"!==o.current.openEvent.type?r(!0,e.nativeEvent,"click"):r(!1,e.nativeEvent,"click"))},onKeyDown(e){m.current=void 0,e.defaultPrevented||!f||qt(e)||(" "!==e.key||_t(i)||(e.preventDefault(),p.current=!0),function(e){return d(e.target)&&"A"===e.target.tagName}(e)||"Enter"===e.key&&r(!n||!s,e.nativeEvent,"click"))},onKeyUp(e){e.defaultPrevented||!f||qt(e)||_t(i)||" "===e.key&&p.current&&(p.current=!1,r(!n||!s,e.nativeEvent,"click"))}})),[o,i,c,a,f,r,n,v,s]);return u.useMemo((()=>l?{reference:g}:{}),[l,g])},e.useClientPoint=function(e,t){void 0===t&&(t={});const{open:n,dataRef:r,elements:{floating:o,domReference:i},refs:l}=e,{enabled:c=!0,axis:a="both",x:f=null,y:d=null}=t,v=u.useRef(!1),m=u.useRef(null),[p,g]=u.useState(),[h,y]=u.useState([]),b=de(((e,t)=>{v.current||r.current.openEvent&&!Wt(r.current.openEvent)||l.setPositionReference(function(e,t){let n=null,r=null,o=!1;return{contextElement:e||void 0,getBoundingClientRect(){var u;const i=(null==e?void 0:e.getBoundingClientRect())||{width:0,height:0,x:0,y:0},l="x"===t.axis||"both"===t.axis,c="y"===t.axis||"both"===t.axis,s=["mouseenter","mousemove"].includes((null==(u=t.dataRef.current.openEvent)?void 0:u.type)||"")&&"touch"!==t.pointerType;let a=i.width,f=i.height,d=i.x,v=i.y;return null==n&&t.x&&l&&(n=i.x-t.x),null==r&&t.y&&c&&(r=i.y-t.y),d-=n||0,v-=r||0,a=0,f=0,!o||s?(a="y"===t.axis?i.width:0,f="x"===t.axis?i.height:0,d=l&&null!=t.x?t.x:d,v=c&&null!=t.y?t.y:v):o&&!s&&(f="x"===t.axis?i.height:f,a="y"===t.axis?i.width:a),o=!0,{width:a,height:f,x:d,y:v,top:v,right:d+a,bottom:v+f,left:d}}}}(i,{x:e,y:t,axis:a,dataRef:r,pointerType:p}))})),w=de((e=>{null==f&&null==d&&(n?m.current||y([]):b(e.clientX,e.clientY))})),E=ce(p)?o:n,R=u.useCallback((()=>{if(!E||!c||null!=f||null!=d)return;const e=s(o);function t(n){const r=$(n);Z(o,r)?(e.removeEventListener("mousemove",t),m.current=null):b(n.clientX,n.clientY)}if(!r.current.openEvent||Wt(r.current.openEvent)){e.addEventListener("mousemove",t);const n=()=>{e.removeEventListener("mousemove",t),m.current=null};return m.current=n,n}l.setPositionReference(i)}),[E,c,f,d,o,r,l,i,b]);u.useEffect((()=>R()),[R,h]),u.useEffect((()=>{c&&!o&&(v.current=!1)}),[c,o]),u.useEffect((()=>{!c&&n&&(v.current=!0)}),[c,n]),se((()=>{!c||null==f&&null==d||(v.current=!1,b(f,d))}),[c,f,d,b]);const x=u.useMemo((()=>{function e(e){let{pointerType:t}=e;g(t)}return{onPointerDown:e,onPointerEnter:e,onMouseMove:w,onMouseEnter:w}}),[w]);return u.useMemo((()=>c?{reference:x}:{}),[c,x])},e.useDelayGroup=function(e,t){void 0===t&&(t={});const{open:n,onOpenChange:r,floatingId:o}=e,{id:u,enabled:i=!0}=t,l=null!=u?u:o,c=mt(),{currentId:s,setCurrentId:a,initialDelay:f,setState:d,timeoutMs:v}=c;return se((()=>{i&&s&&(d({delay:{open:1,close:at(f,"close")}}),s!==l&&r(!1))}),[i,l,r,d,s,f]),se((()=>{function e(){r(!1),d({delay:f,currentId:null})}if(i&&s&&!n&&s===l){if(v){const t=window.setTimeout(e,v);return()=>{clearTimeout(t)}}e()}}),[i,n,d,s,l,r,f,v]),se((()=>{i&&a!==dt&&n&&a(l)}),[i,n,a,l]),c},e.useDelayGroupContext=mt,e.useDismiss=function(e,t){void 0===t&&(t={});const{open:n,onOpenChange:o,elements:i,dataRef:l}=e,{enabled:c=!0,escapeKey:s=!0,outsidePress:a=!0,outsidePressEvent:v="pointerdown",referencePress:h=!1,referencePressEvent:y="pointerdown",ancestorScroll:b=!1,bubbles:w,capture:E}=t,R=it(),x=de("function"==typeof a?a:()=>!1),I="function"==typeof a?x:a,k=u.useRef(!1),C=u.useRef(!1),{escapeKey:M,outsidePress:O}=zt(w),{escapeKey:S,outsidePress:P}=zt(E),T=u.useRef(!1),A=de((e=>{var t;if(!n||!c||!s||"Escape"!==e.key)return;if(T.current)return;const r=null==(t=l.current.floatingContext)?void 0:t.nodeId,u=R?re(R.nodesRef.current,r):[];if(!M&&(e.stopPropagation(),u.length>0)){let e=!0;if(u.forEach((t=>{var n;null==(n=t.context)||!n.open||t.context.dataRef.current.__escapeKeyBubbles||(e=!1)})),!e)return}o(!1,function(e){return"nativeEvent"in e}(e)?e.nativeEvent:e,"escape-key")})),L=de((e=>{var t;const n=()=>{var t;A(e),null==(t=$(e))||t.removeEventListener("keydown",n)};null==(t=$(e))||t.addEventListener("keydown",n)})),N=de((e=>{var t;const n=k.current;k.current=!1;const r=C.current;if(C.current=!1,"click"===v&&r)return;if(n)return;if("function"==typeof I&&!I(e))return;const u=$(e),c="["+lt("inert")+"]",s=J(i.floating).querySelectorAll(c);let a=f(u)?u:null;for(;a&&!m(a);){const e=g(a);if(m(e)||!f(e))break;a=e}if(s.length&&f(u)&&!u.matches("html,body")&&!Z(u,i.floating)&&Array.from(s).every((e=>!Z(a,e))))return;if(d(u)&&j){const t=m(u),n=p(u),r=/auto|scroll/,o=t||r.test(n.overflowX),i=t||r.test(n.overflowY),l=o&&u.clientWidth>0&&u.scrollWidth>u.clientWidth,c=i&&u.clientHeight>0&&u.scrollHeight>u.clientHeight,s="rtl"===n.direction,a=c&&(s?e.offsetX<=u.offsetWidth-u.clientWidth:e.offsetX>u.clientWidth),f=l&&e.offsetY>u.clientHeight;if(a||f)return}const h=null==(t=l.current.floatingContext)?void 0:t.nodeId,y=R&&re(R.nodesRef.current,h).some((t=>{var n;return Q(e,null==(n=t.context)?void 0:n.elements.floating)}));if(Q(e,i.floating)||Q(e,i.domReference)||y)return;const b=R?re(R.nodesRef.current,h):[];if(b.length>0){let e=!0;if(b.forEach((t=>{var n;null==(n=t.context)||!n.open||t.context.dataRef.current.__outsidePressBubbles||(e=!1)})),!e)return}o(!1,e,"outside-press")})),D=de((e=>{var t;const n=()=>{var t;N(e),null==(t=$(e))||t.removeEventListener(v,n)};null==(t=$(e))||t.addEventListener(v,n)}));u.useEffect((()=>{if(!n||!c)return;l.current.__escapeKeyBubbles=M,l.current.__outsidePressBubbles=O;let e=-1;function t(e){o(!1,e,"ancestor-scroll")}function u(){window.clearTimeout(e),T.current=!0}function a(){e=window.setTimeout((()=>{T.current=!1}),"undefined"!=typeof CSS&&CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")?5:0)}const d=J(i.floating);s&&(d.addEventListener("keydown",S?L:A,S),d.addEventListener("compositionstart",u),d.addEventListener("compositionend",a)),I&&d.addEventListener(v,P?D:N,P);let m=[];return b&&(f(i.domReference)&&(m=r.getOverflowAncestors(i.domReference)),f(i.floating)&&(m=m.concat(r.getOverflowAncestors(i.floating))),!f(i.reference)&&i.reference&&i.reference.contextElement&&(m=m.concat(r.getOverflowAncestors(i.reference.contextElement)))),m=m.filter((e=>{var t;return e!==(null==(t=d.defaultView)?void 0:t.visualViewport)})),m.forEach((e=>{e.addEventListener("scroll",t,{passive:!0})})),()=>{s&&(d.removeEventListener("keydown",S?L:A,S),d.removeEventListener("compositionstart",u),d.removeEventListener("compositionend",a)),I&&d.removeEventListener(v,P?D:N,P),m.forEach((e=>{e.removeEventListener("scroll",t)})),window.clearTimeout(e)}}),[l,i,s,I,v,n,o,b,c,M,O,A,S,L,N,P,D]),u.useEffect((()=>{k.current=!1}),[I,v]);const F=u.useMemo((()=>({onKeyDown:A,...h&&{[Ut[y]]:e=>{o(!1,e.nativeEvent,"reference-press")},..."click"!==y&&{onClick(e){o(!1,e.nativeEvent,"reference-press")}}}})),[A,o,h,y]),j=u.useMemo((()=>({onKeyDown:A,onMouseDown(){C.current=!0},onMouseUp(){C.current=!0},[Bt[v]]:()=>{k.current=!0}})),[A,v]);return u.useMemo((()=>c?{reference:F,floating:j}:{}),[c,F,j])},e.useFloating=function(e){void 0===e&&(e={});const{nodeId:t}=e,n=Xt({...e,elements:{reference:null,floating:null,...e.elements}}),o=e.rootContext||n,i=o.elements,[l,c]=u.useState(null),[s,a]=u.useState(null),d=(null==i?void 0:i.domReference)||l,v=u.useRef(null),m=it();se((()=>{d&&(v.current=d)}),[d]);const p=r.useFloating({...e,elements:{...i,...s&&{reference:s}}}),g=u.useCallback((e=>{const t=f(e)?{getBoundingClientRect:()=>e.getBoundingClientRect(),getClientRects:()=>e.getClientRects(),contextElement:e}:e;a(t),p.refs.setReference(t)}),[p.refs]),h=u.useCallback((e=>{(f(e)||null===e)&&(v.current=e,c(e)),(f(p.refs.reference.current)||null===p.refs.reference.current||null!==e&&!f(e))&&p.refs.setReference(e)}),[p.refs]),y=u.useMemo((()=>({...p.refs,setReference:h,setPositionReference:g,domReference:v})),[p.refs,h,g]),b=u.useMemo((()=>({...p.elements,domReference:d})),[p.elements,d]),w=u.useMemo((()=>({...p,...o,refs:y,elements:b,nodeId:t})),[p,y,b,t,o]);return se((()=>{o.dataRef.current.floatingContext=w;const e=null==m?void 0:m.nodesRef.current.find((e=>e.id===t));e&&(e.context=w)})),u.useMemo((()=>({...p,context:w,refs:y,elements:b})),[p,y,b,w])},e.useFloatingNodeId=function(e){const t=et(),n=it(),r=ut(),o=e||r;return se((()=>{if(!t)return;const e={id:t,parentId:o};return null==n||n.addNode(e),()=>{null==n||n.removeNode(e)}}),[n,t,o]),t},e.useFloatingParentNodeId=ut,e.useFloatingPortalNode=Pt,e.useFloatingRootContext=Xt,e.useFloatingTree=it,e.useFocus=function(e,t){void 0===t&&(t={});const{open:n,onOpenChange:r,events:o,dataRef:i,elements:l}=e,{enabled:c=!0,visibleOnly:a=!0}=t,v=u.useRef(!1),m=u.useRef(-1),p=u.useRef(!0);u.useEffect((()=>{if(!c)return;const e=s(l.domReference);function t(){!n&&d(l.domReference)&&l.domReference===G(J(l.domReference))&&(v.current=!0)}function r(){p.current=!0}function o(){p.current=!1}return e.addEventListener("blur",t),Yt()&&(e.addEventListener("keydown",r,!0),e.addEventListener("pointerdown",o,!0)),()=>{e.removeEventListener("blur",t),Yt()&&(e.removeEventListener("keydown",r,!0),e.removeEventListener("pointerdown",o,!0))}}),[l.domReference,n,c]),u.useEffect((()=>{if(c)return o.on("openchange",e),()=>{o.off("openchange",e)};function e(e){let{reason:t}=e;"reference-press"!==t&&"escape-key"!==t||(v.current=!0)}}),[o,c]),u.useEffect((()=>()=>{ct(m)}),[]);const g=u.useMemo((()=>({onMouseLeave(){v.current=!1},onFocus(e){if(v.current)return;const t=$(e.nativeEvent);if(a&&f(t))if(Yt()&&!e.relatedTarget){if(!p.current&&!ee(t))return}else if(!function(e){if(!e||z())return!0;try{return e.matches(":focus-visible")}catch(e){return!0}}(t))return;r(!0,e.nativeEvent,"focus")},onBlur(e){v.current=!1;const t=e.relatedTarget,n=e.nativeEvent,o=f(t)&&t.hasAttribute(lt("focus-guard"))&&"outside"===t.getAttribute("data-type");m.current=window.setTimeout((()=>{var e;const u=G(l.domReference?l.domReference.ownerDocument:document);(t||u!==l.domReference)&&(Z(null==(e=i.current.floatingContext)?void 0:e.refs.floating.current,u)||Z(l.domReference,u)||o||r(!1,n,"focus"))}))}})),[i,l.domReference,r,a]);return u.useMemo((()=>c?{reference:g}:{}),[c,g])},e.useHover=function(e,t){void 0===t&&(t={});const{open:n,onOpenChange:r,dataRef:o,events:i,elements:l}=e,{enabled:c=!0,delay:s=0,handleClose:a=null,mouseOnly:d=!1,restMs:v=0,move:m=!0}=t,p=it(),g=ut(),h=ae(a),y=ae(s),b=ae(n),w=ae(v),E=u.useRef(),R=u.useRef(-1),x=u.useRef(),I=u.useRef(-1),k=u.useRef(!0),C=u.useRef(!1),M=u.useRef((()=>{})),O=u.useRef(!1),S=u.useCallback((()=>{var e;const t=null==(e=o.current.openEvent)?void 0:e.type;return(null==t?void 0:t.includes("mouse"))&&"mousedown"!==t}),[o]);u.useEffect((()=>{if(c)return i.on("openchange",e),()=>{i.off("openchange",e)};function e(e){let{open:t}=e;t||(ct(R),ct(I),k.current=!0,O.current=!1)}}),[c,i]),u.useEffect((()=>{if(!c)return;if(!h.current)return;if(!n)return;function e(e){S()&&r(!1,e,"hover")}const t=J(l.floating).documentElement;return t.addEventListener("mouseleave",e),()=>{t.removeEventListener("mouseleave",e)}}),[l.floating,n,r,c,h,S]);const P=u.useCallback((function(e,t,n){void 0===t&&(t=!0),void 0===n&&(n="hover");const o=at(y.current,"close",E.current);o&&!x.current?(ct(R),R.current=window.setTimeout((()=>r(!1,e,n)),o)):t&&(ct(R),r(!1,e,n))}),[y,r]),T=de((()=>{M.current(),x.current=void 0})),A=de((()=>{if(C.current){const e=J(l.floating).body;e.style.pointerEvents="",e.removeAttribute(st),C.current=!1}})),L=de((()=>!!o.current.openEvent&&["click","mousedown"].includes(o.current.openEvent.type)));u.useEffect((()=>{if(c&&f(l.domReference)){const r=l.domReference,o=l.floating;return n&&r.addEventListener("mouseleave",u),m&&r.addEventListener("mousemove",e,{once:!0}),r.addEventListener("mouseenter",e),r.addEventListener("mouseleave",t),o&&(o.addEventListener("mouseleave",u),o.addEventListener("mouseenter",i),o.addEventListener("mouseleave",s)),()=>{n&&r.removeEventListener("mouseleave",u),m&&r.removeEventListener("mousemove",e),r.removeEventListener("mouseenter",e),r.removeEventListener("mouseleave",t),o&&(o.removeEventListener("mouseleave",u),o.removeEventListener("mouseenter",i),o.removeEventListener("mouseleave",s))}}function e(e){if(ct(R),k.current=!1,d&&!ce(E.current)||ft(w.current)>0&&!at(y.current,"open"))return;const t=at(y.current,"open",E.current);t?R.current=window.setTimeout((()=>{b.current||r(!0,e,"hover")}),t):n||r(!0,e,"hover")}function t(e){if(L())return void A();M.current();const t=J(l.floating);if(ct(I),O.current=!1,h.current&&o.current.floatingContext){n||ct(R),x.current=h.current({...o.current.floatingContext,tree:p,x:e.clientX,y:e.clientY,onClose(){A(),T(),L()||P(e,!0,"safe-polygon")}});const r=x.current;return t.addEventListener("mousemove",r),void(M.current=()=>{t.removeEventListener("mousemove",r)})}("touch"!==E.current||!Z(l.floating,e.relatedTarget))&&P(e)}function u(e){L()||o.current.floatingContext&&(null==h.current||h.current({...o.current.floatingContext,tree:p,x:e.clientX,y:e.clientY,onClose(){A(),T(),L()||P(e)}})(e))}function i(){ct(R)}function s(e){L()||P(e,!1)}}),[l,c,e,d,m,P,T,A,r,n,b,p,y,h,o,L,w]),se((()=>{var e;if(c&&n&&null!=(e=h.current)&&e.__options.blockPointerEvents&&S()){C.current=!0;const e=l.floating;if(f(l.domReference)&&e){var t;const n=J(l.floating).body;n.setAttribute(st,"");const r=l.domReference,o=null==p||null==(t=p.nodesRef.current.find((e=>e.id===g)))||null==(t=t.context)?void 0:t.elements.floating;return o&&(o.style.pointerEvents=""),n.style.pointerEvents="none",r.style.pointerEvents="auto",e.style.pointerEvents="auto",()=>{n.style.pointerEvents="",r.style.pointerEvents="",e.style.pointerEvents=""}}}}),[c,n,g,l,p,h,S]),se((()=>{n||(E.current=void 0,O.current=!1,T(),A())}),[n,T,A]),u.useEffect((()=>()=>{T(),ct(R),ct(I),A()}),[c,l.domReference,T,A]);const N=u.useMemo((()=>{function e(e){E.current=e.pointerType}return{onPointerDown:e,onPointerEnter:e,onMouseMove(e){const{nativeEvent:t}=e;function o(){k.current||b.current||r(!0,t,"hover")}d&&!ce(E.current)||n||0===ft(w.current)||O.current&&e.movementX**2+e.movementY**2<2||(ct(I),"touch"===E.current?o():(O.current=!0,I.current=window.setTimeout(o,ft(w.current))))}}}),[d,r,n,b,w]);return u.useMemo((()=>c?{reference:N}:{}),[c,N])},e.useId=et,e.useInnerOffset=function(e,t){const{open:n,elements:r}=e,{enabled:o=!0,overflowRef:l,scrollRef:c,onChange:s}=t,a=de(s),f=u.useRef(!1),d=u.useRef(null),v=u.useRef(null);u.useEffect((()=>{if(!o)return;function e(e){if(e.ctrlKey||!t||null==l.current)return;const n=e.deltaY,r=l.current.top>=-.5,o=l.current.bottom>=-.5,u=t.scrollHeight-t.clientHeight,c=n<0?-1:1,s=n<0?"max":"min";t.scrollHeight<=t.clientHeight||(!r&&n>0||!o&&n<0?(e.preventDefault(),i.flushSync((()=>{a((e=>e+Math[s](n,u*c)))}))):/firefox/i.test(W())&&(t.scrollTop+=n))}const t=(null==c?void 0:c.current)||r.floating;return n&&t?(t.addEventListener("wheel",e),requestAnimationFrame((()=>{d.current=t.scrollTop,null!=l.current&&(v.current={...l.current})})),()=>{d.current=null,v.current=null,t.removeEventListener("wheel",e)}):void 0}),[o,n,r.floating,l,c,a]);const m=u.useMemo((()=>({onKeyDown(){f.current=!0},onWheel(){f.current=!1},onPointerMove(){f.current=!1},onScroll(){const e=(null==c?void 0:c.current)||r.floating;if(l.current&&e&&f.current){if(null!==d.current){const t=e.scrollTop-d.current;(l.current.bottom<-.5&&t<-1||l.current.top<-.5&&t>1)&&i.flushSync((()=>a((e=>e+t))))}requestAnimationFrame((()=>{d.current=e.scrollTop}))}}})),[r.floating,a,l,c]);return u.useMemo((()=>o?{floating:m}:{}),[o,m])},e.useInteractions=function(e){void 0===e&&(e=[]);const t=e.map((e=>null==e?void 0:e.reference)),n=e.map((e=>null==e?void 0:e.floating)),r=e.map((e=>null==e?void 0:e.item)),o=u.useCallback((t=>Vt(t,e,"reference")),t),i=u.useCallback((t=>Vt(t,e,"floating")),n),l=u.useCallback((t=>Vt(t,e,"item")),r);return u.useMemo((()=>({getReferenceProps:o,getFloatingProps:i,getItemProps:l})),[o,i,l])},e.useListItem=Ne,e.useListNavigation=function(e,t){const{open:n,onOpenChange:r,elements:o,floatingId:i}=e,{listRef:l,activeIndex:c,onNavigate:s=()=>{},enabled:a=!0,selectedIndex:f=null,allowEscape:v=!1,loop:m=!1,nested:p=!1,rtl:g=!1,virtual:h=!1,focusItemOnOpen:y="auto",focusItemOnHover:b=!0,openOnArrowKeyDown:w=!0,disabledIndices:E,orientation:R="vertical",parentOrientation:x,cols:I=1,scrollItemIntoView:k=!0,virtualItemRef:C,itemSizes:M,dense:O=!1}=t,S=ae(ne(o.floating)),P=ut(),T=it();se((()=>{e.dataRef.current.orientation=R}),[e,R]);const A=de((()=>{s(-1===D.current?null:D.current)})),L=te(o.domReference),N=u.useRef(y),D=u.useRef(null!=f?f:-1),F=u.useRef(null),j=u.useRef(!0),K=u.useRef(A),H=u.useRef(!!o.floating),q=u.useRef(n),_=u.useRef(!1),W=u.useRef(!1),U=ae(E),B=ae(n),z=ae(k),X=ae(f),[Y,V]=u.useState(),[$,Q]=u.useState(),ee=de((()=>{function e(e){var t;h?(null!=(t=e.id)&&t.endsWith("-fui-option")&&(e.id=i+"-"+Math.random().toString(16).slice(2,10)),V(e.id),null==T||T.events.emit("virtualfocus",e),C&&(C.current=e)):ht(e,{sync:_.current,preventScroll:!0})}const t=l.current[D.current],n=W.current;t&&e(t);(_.current?e=>e():requestAnimationFrame)((()=>{const r=l.current[D.current]||t;if(!r)return;t||e(r);const o=z.current;o&&ce&&(n||!j.current)&&(null==r.scrollIntoView||r.scrollIntoView("boolean"==typeof o?{block:"nearest",inline:"nearest"}:o))}))}));se((()=>{a&&(n&&o.floating?N.current&&null!=f&&(W.current=!0,D.current=f,A()):H.current&&(D.current=-1,K.current()))}),[a,n,o.floating,f,A]),se((()=>{if(a&&n&&o.floating)if(null==c){if(_.current=!1,null!=X.current)return;if(H.current&&(D.current=-1,ee()),(!q.current||!H.current)&&N.current&&(null!=F.current||!0===N.current&&null==F.current)){let e=0;const t=()=>{if(null==l.current[0]){if(e<2){(e?requestAnimationFrame:queueMicrotask)(t)}e++}else D.current=null==F.current||$t(F.current,R,g)||p?pe(l,U.current):ge(l,U.current),F.current=null,A()};t()}}else me(l,c)||(D.current=c,ee(),W.current=!1)}),[a,n,o.floating,c,X,p,l,R,g,A,ee,U]),se((()=>{var e;if(!a||o.floating||!T||h||!H.current)return;const t=T.nodesRef.current,n=null==(e=t.find((e=>e.id===P)))||null==(e=e.context)?void 0:e.elements.floating,r=G(J(o.floating)),u=t.some((e=>e.context&&Z(e.context.elements.floating,r)));n&&!u&&j.current&&n.focus({preventScroll:!0})}),[a,o.floating,T,P,h]),se((()=>{if(a&&T&&h&&!P)return T.events.on("virtualfocus",e),()=>{T.events.off("virtualfocus",e)};function e(e){Q(e.id),C&&(C.current=e)}}),[a,T,h,P,C]),se((()=>{K.current=A,q.current=n,H.current=!!o.floating})),se((()=>{n||(F.current=null)}),[n]);const oe=null!=c,ce=u.useMemo((()=>{function e(e){if(!n)return;const t=l.current.indexOf(e);-1!==t&&D.current!==t&&(D.current=t,A())}return{onFocus(t){let{currentTarget:n}=t;_.current=!0,e(n)},onClick:e=>{let{currentTarget:t}=e;return t.focus({preventScroll:!0})},...b&&{onMouseMove(t){let{currentTarget:n}=t;_.current=!0,W.current=!1,e(n)},onPointerLeave(e){let{pointerType:t}=e;var n;j.current&&"touch"!==t&&(_.current=!0,D.current=-1,A(),h||null==(n=S.current)||n.focus({preventScroll:!0}))}}}}),[n,S,b,l,A,h]),fe=u.useCallback((()=>{var e;return null!=x?x:null==T||null==(e=T.nodesRef.current.find((e=>e.id===P)))||null==(e=e.context)||null==(e=e.dataRef)?void 0:e.current.orientation}),[P,T,x]),ve=de((e=>{if(j.current=!1,_.current=!0,229===e.which)return;if(!B.current&&e.currentTarget===S.current)return;if(p&&Jt(e.key,R,g,I))return Zt(e.key,fe())||ue(e),r(!1,e.nativeEvent,"list-navigation"),void(d(o.domReference)&&(h?null==T||T.events.emit("virtualfocus",o.domReference):o.domReference.focus()));const t=D.current,u=pe(l,E),i=ge(l,E);if(L||("Home"===e.key&&(ue(e),D.current=u,A()),"End"===e.key&&(ue(e),D.current=i,A())),I>1){const t=M||Array.from({length:l.current.length},(()=>({width:1,height:1}))),n=be(t,I,O),r=n.findIndex((e=>null!=e&&!Re(l,e,E))),o=n.reduce(((e,t,n)=>null==t||Re(l,t,E)?e:n),-1),c=n[ye({current:n.map((e=>null!=e?l.current[e]:null))},{event:e,orientation:R,loop:m,rtl:g,cols:I,disabledIndices:Ee([...E||l.current.map(((e,t)=>Re(l,t)?t:void 0)),void 0],n),minIndex:r,maxIndex:o,prevIndex:we(D.current>i?u:D.current,t,n,I,e.key===_e?"bl":e.key===(g?Ke:He)?"tr":"tl"),stopEvent:!0})];if(null!=c&&(D.current=c,A()),"both"===R)return}if(Zt(e.key,R)){if(ue(e),n&&!h&&G(e.currentTarget.ownerDocument)===e.currentTarget)return D.current=$t(e.key,R,g)?u:i,void A();$t(e.key,R,g)?D.current=m?t>=i?v&&t!==l.current.length?-1:u:he(l,{startingIndex:t,disabledIndices:E}):Math.min(i,he(l,{startingIndex:t,disabledIndices:E})):D.current=m?t<=u?v&&-1!==t?l.current.length:i:he(l,{startingIndex:t,decrement:!0,disabledIndices:E}):Math.max(u,he(l,{startingIndex:t,decrement:!0,disabledIndices:E})),me(l,D.current)&&(D.current=-1),A()}})),xe=u.useMemo((()=>h&&n&&oe&&{"aria-activedescendant":$||Y}),[h,n,oe,$,Y]),Ie=u.useMemo((()=>({"aria-orientation":"both"===R?void 0:R,...L?{}:xe,onKeyDown:ve,onPointerMove(){j.current=!0}})),[xe,ve,R,L]),ke=u.useMemo((()=>{function e(e){"auto"===y&&ie(e.nativeEvent)&&(N.current=!0)}function t(e){N.current=y,"auto"===y&&le(e.nativeEvent)&&(N.current=!0)}return{...xe,onKeyDown(e){j.current=!1;const t=e.key.startsWith("Arrow"),o=["Home","End"].includes(e.key),u=t||o,i=Qt(e.key,R,g),c=Jt(e.key,R,g,I),s=Qt(e.key,fe(),g),a=Zt(e.key,R),d=(p?s:a)||"Enter"===e.key||""===e.key.trim();if(h&&n){const t=null==T?void 0:T.nodesRef.current.find((e=>null==e.parentId)),n=T&&t?function(e,t){let n,r=-1;return function t(o,u){u>r&&(n=o,r=u),re(e,o).forEach((e=>{t(e.id,u+1)}))}(t,0),e.find((e=>e.id===n))}(T.nodesRef.current,t.id):null;if(u&&n&&C){const t=new KeyboardEvent("keydown",{key:e.key,bubbles:!0});if(i||c){var v,m;const r=(null==(v=n.context)?void 0:v.elements.domReference)===e.currentTarget,o=c&&!r?null==(m=n.context)?void 0:m.elements.domReference:i?l.current.find((e=>(null==e?void 0:e.id)===Y)):null;o&&(ue(e),o.dispatchEvent(t),Q(void 0))}var y;if((a||o)&&n.context)if(n.context.open&&n.parentId&&e.currentTarget!==n.context.elements.domReference)return ue(e),void(null==(y=n.context.elements.domReference)||y.dispatchEvent(t))}return ve(e)}if(n||w||!t){if(d){const t=Zt(e.key,fe());F.current=p&&t?null:e.key}p?s&&(ue(e),n?(D.current=pe(l,U.current),A()):r(!0,e.nativeEvent,"list-navigation")):a&&(null!=f&&(D.current=f),ue(e),!n&&w?r(!0,e.nativeEvent,"list-navigation"):ve(e),n&&A())}},onFocus(){n&&!h&&(D.current=-1,A())},onPointerDown:t,onPointerEnter:t,onMouseDown:e,onClick:e}}),[Y,xe,I,ve,U,y,l,p,A,r,n,w,R,fe,g,f,T,h,C]);return u.useMemo((()=>a?{reference:ke,floating:Ie,item:ce}:{}),[a,ke,Ie,ce])},e.useMergeRefs=Pe,e.useNextDelayGroup=function(e,t){void 0===t&&(t={});const{open:n,onOpenChange:r,floatingId:o}=e,{enabled:i=!0}=t,l=u.useContext(pt),{currentIdRef:c,delayRef:s,timeoutMs:a,initialDelayRef:f,currentContextRef:d,hasProvider:v,timeoutIdRef:m}=l,[p,g]=u.useState(!1);return se((()=>{function e(){var e;g(!1),null==(e=d.current)||e.setIsInstantPhase(!1),c.current=null,d.current=null,s.current=f.current}if(i&&c.current&&!n&&c.current===o){if(g(!1),a)return m.current=window.setTimeout(e,a),()=>{clearTimeout(m.current)};e()}}),[i,n,o,c,s,a,f,d,m]),se((()=>{if(!i)return;if(!n)return;const e=d.current,t=c.current;d.current={onOpenChange:r,setIsInstantPhase:g},c.current=o,s.current={open:0,close:at(f.current,"close")},null!==t&&t!==o?(ct(m),g(!0),null==e||e.setIsInstantPhase(!0),null==e||e.onOpenChange(!1)):(g(!1),null==e||e.setIsInstantPhase(!1))}),[i,n,o,r,c,s,a,f,d,m]),se((()=>()=>{d.current=null}),[d]),u.useMemo((()=>({hasProvider:v,delayRef:s,isInstantPhase:p})),[v,s,p])},e.useRole=function(e,t){var n,r;void 0===t&&(t={});const{open:o,elements:i,floatingId:l}=e,{enabled:c=!0,role:s="dialog"}=t,a=et(),f=(null==(n=i.domReference)?void 0:n.id)||a,d=u.useMemo((()=>{var e;return(null==(e=ne(i.floating))?void 0:e.id)||l}),[i.floating,l]),v=null!=(r=en.get(s))?r:s,m=null!=ut(),p=u.useMemo((()=>"tooltip"===v||"label"===s?{["aria-"+("label"===s?"labelledby":"describedby")]:o?d:void 0}:{"aria-expanded":o?"true":"false","aria-haspopup":"alertdialog"===v?"dialog":v,"aria-controls":o?d:void 0,..."listbox"===v&&{role:"combobox"},..."menu"===v&&{id:f},..."menu"===v&&m&&{role:"menuitem"},..."select"===s&&{"aria-autocomplete":"none"},..."combobox"===s&&{"aria-autocomplete":"list"}}),[v,d,m,o,f,s]),g=u.useMemo((()=>{const e={id:d,...v&&{role:v}};return"tooltip"===v||"label"===s?e:{...e,..."menu"===v&&{"aria-labelledby":f}}}),[v,d,f,s]),h=u.useCallback((e=>{let{active:t,selected:n}=e;const r={role:"option",...t&&{id:d+"-fui-option"}};switch(s){case"select":return{...r,"aria-selected":t&&n};case"combobox":return{...r,"aria-selected":n}}return{}}),[d,s]);return u.useMemo((()=>c?{reference:p,floating:g,item:h}:{}),[c,p,g,h])},e.useTransitionStatus=rn,e.useTransitionStyles=function(e,t){void 0===t&&(t={});const{initial:n={opacity:0},open:r,close:o,common:i,duration:l=250}=t,c=e.placement,s=c.split("-")[0],a=u.useMemo((()=>({side:s,placement:c})),[s,c]),f="number"==typeof l,d=(f?l:l.open)||0,v=(f?l:l.close)||0,[m,p]=u.useState((()=>({...nn(i,a),...nn(n,a)}))),{isMounted:g,status:h}=rn(e,{duration:l}),y=ae(n),b=ae(r),w=ae(o),E=ae(i);return se((()=>{const e=nn(y.current,a),t=nn(w.current,a),n=nn(E.current,a),r=nn(b.current,a)||Object.keys(e).reduce(((e,t)=>(e[t]="",e)),{});if("initial"===h&&p((t=>({transitionProperty:t.transitionProperty,...n,...e}))),"open"===h&&p({transitionProperty:Object.keys(r).map(tn).join(","),transitionDuration:d+"ms",...n,...r}),"close"===h){const r=t||e;p({transitionProperty:Object.keys(r).map(tn).join(","),transitionDuration:v+"ms",...n,...r})}}),[v,w,y,b,E,d,h,a]),{isMounted:g,styles:m}},e.useTypeahead=function(e,t){var n;const{open:r,dataRef:o}=e,{listRef:i,activeIndex:l,onMatch:c,onTypingChange:s,enabled:a=!0,findMatch:f=null,resetMs:d=750,ignoreKeys:v=[],selectedIndex:m=null}=t,p=u.useRef(-1),g=u.useRef(""),h=u.useRef(null!=(n=null!=m?m:l)?n:-1),y=u.useRef(null),b=de(c),w=de(s),E=ae(f),R=ae(v);se((()=>{r&&(ct(p),y.current=null,g.current="")}),[r]),se((()=>{var e;r&&""===g.current&&(h.current=null!=(e=null!=m?m:l)?e:-1)}),[r,m,l]);const x=de((e=>{e?o.current.typing||(o.current.typing=e,w(e)):o.current.typing&&(o.current.typing=e,w(e))})),I=de((e=>{function t(e,t,n){const r=E.current?E.current(t,n):t.find((e=>0===(null==e?void 0:e.toLocaleLowerCase().indexOf(n.toLocaleLowerCase()))));return r?e.indexOf(r):-1}const n=i.current;if(g.current.length>0&&" "!==g.current[0]&&(-1===t(n,n,g.current)?x(!1):" "===e.key&&ue(e)),null==n||R.current.includes(e.key)||1!==e.key.length||e.ctrlKey||e.metaKey||e.altKey)return;r&&" "!==e.key&&(ue(e),x(!0));n.every((e=>{var t,n;return!e||(null==(t=e[0])?void 0:t.toLocaleLowerCase())!==(null==(n=e[1])?void 0:n.toLocaleLowerCase())}))&&g.current===e.key&&(g.current="",h.current=y.current),g.current+=e.key,ct(p),p.current=window.setTimeout((()=>{g.current="",h.current=y.current,x(!1)}),d);const o=h.current,u=t(n,[...n.slice((o||0)+1),...n.slice(0,(o||0)+1)],g.current);-1!==u?(b(u),y.current=u):" "!==e.key&&(g.current="",x(!1))})),k=u.useMemo((()=>({onKeyDown:I})),[I]),C=u.useMemo((()=>({onKeyDown:I,onKeyUp(e){" "===e.key&&x(!1)}})),[I,x]);return u.useMemo((()=>a?{reference:k,floating:C}:{}),[a,k,C])}}));
