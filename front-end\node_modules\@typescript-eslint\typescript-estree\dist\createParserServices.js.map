{"version": 3, "file": "createParserServices.js", "sourceRoot": "", "sources": ["../src/createParserServices.ts"], "names": [], "mappings": ";;AAKA,oDA8BC;AA9BD,SAAgB,oBAAoB,CAClC,OAAgB,EAChB,OAA0B;IAE1B,IAAI,CAAC,OAAO,EAAE,CAAC;QACb,OAAO;YACL,OAAO;YACP,qBAAqB,EAAE,SAAS;YAChC,sBAAsB,EAAE,SAAS;YACjC,yCAAyC;YACzC,uCAAuC;YACvC,mFAAmF;YACnF,GAAG,OAAO;SACX,CAAC;IACJ,CAAC;IAED,MAAM,OAAO,GAAG,OAAO,CAAC,cAAc,EAAE,CAAC;IACzC,MAAM,eAAe,GAAG,OAAO,CAAC,kBAAkB,EAAE,CAAC;IAErD,OAAO;QACL,OAAO;QACP,2CAA2C;QAC3C,qBAAqB,EAAE,eAAe,CAAC,qBAAqB,IAAI,KAAK;QACrE,sBAAsB,EAAE,eAAe,CAAC,sBAAsB,IAAI,KAAK;QACvE,GAAG,OAAO;QACV,mBAAmB,EAAE,IAAI,CAAC,EAAE,CAC1B,OAAO,CAAC,mBAAmB,CAAC,OAAO,CAAC,qBAAqB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QACtE,iBAAiB,EAAE,IAAI,CAAC,EAAE,CACxB,OAAO,CAAC,iBAAiB,CAAC,OAAO,CAAC,qBAAqB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;KACrE,CAAC;AACJ,CAAC"}