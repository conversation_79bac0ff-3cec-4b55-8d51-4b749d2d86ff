{"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://colinmeinke.com"}, "babel": {"env": {"cjs": {"plugins": ["transform-object-rest-spread", "add-module-exports"], "presets": ["es2015"]}, "modules": {"plugins": ["transform-object-rest-spread"], "presets": [["es2015", {"modules": false}]]}, "umd": {"plugins": ["transform-object-rest-spread"], "presets": [["es2015", {"modules": false}]]}}}, "bugs": {"url": "https://github.com/colinmeinke/svg-arc-to-cubic-bezier/issues"}, "config": {"commitizen": {"path": "node_modules/cz-conventional-changelog"}}, "description": "A function that takes an SVG arc curve as input, and maps it to one or more cubic bezier curves", "devDependencies": {"babel-cli": "^6.26.0", "babel-core": "^6.26.3", "babel-plugin-add-module-exports": "^0.3.3", "babel-plugin-transform-object-rest-spread": "^6.26.0", "babel-preset-es2015": "^6.24.1", "commitizen": "^3.1.1", "cz-conventional-changelog": "^2.1.0", "rimraf": "^2.6.3", "rollup": "^1.16.6", "rollup-plugin-babel": "^3.0.7", "rollup-plugin-commonjs": "^10.0.1", "rollup-plugin-uglify": "^6.0.2", "snazzy": "^8.0.0", "standard": "^12.0.1", "travis-deploy-once": "^5.0.11", "semantic-release": "^15.13.18"}, "keywords": ["arc", "bezier", "convert", "cubic", "curve", "path", "svg"], "license": "ISC", "main": "cjs/index.js", "module": "modules/index.js", "name": "svg-arc-to-cubic-bezier", "repository": {"type": "git", "url": "https://github.com/colinmeinke/svg-arc-to-cubic-bezier"}, "scripts": {"build": "npm run build:modules && npm run build:cjs && npm run build:umd", "build:cjs": "BABEL_ENV=cjs babel src --out-dir cjs", "build:modules": "BABEL_ENV=modules babel src --out-dir modules", "build:umd": "npm run build:umd:dev && npm run build:umd:pro", "build:umd:dev": "BABEL_ENV=umd rollup -c", "build:umd:pro": "NODE_ENV=production BABEL_ENV=umd rollup -c", "commit": "git-cz", "fix": "standard --fix", "lint": "standard --verbose | snazzy", "prepublish": "npm run tidy && npm run build", "semantic-release": "semantic-release", "tidy": "rimraf modules cjs dist", "travis-deploy-once": "travis-deploy-once"}, "version": "3.2.0"}