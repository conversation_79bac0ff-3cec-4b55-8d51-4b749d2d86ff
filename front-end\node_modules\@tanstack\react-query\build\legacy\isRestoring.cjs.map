{"version": 3, "sources": ["../../src/isRestoring.ts"], "sourcesContent": ["'use client'\nimport * as React from 'react'\n\nconst IsRestoringContext = React.createContext(false)\n\nexport const useIsRestoring = () => React.useContext(IsRestoringContext)\nexport const IsRestoringProvider = IsRestoringContext.Provider\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,YAAuB;AAEvB,IAAM,qBAA2B,oBAAc,KAAK;AAE7C,IAAM,iBAAiB,MAAY,iBAAW,kBAAkB;AAChE,IAAM,sBAAsB,mBAAmB;", "names": []}