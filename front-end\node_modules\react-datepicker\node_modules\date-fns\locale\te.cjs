"use strict";
exports.te = void 0;
var _index = require("./te/_lib/formatDistance.cjs");
var _index2 = require("./te/_lib/formatLong.cjs");
var _index3 = require("./te/_lib/formatRelative.cjs");
var _index4 = require("./te/_lib/localize.cjs");
var _index5 = require("./te/_lib/match.cjs");

/**
 * @category Locales
 * @summary Telugu locale
 * @language Telugu
 * @iso-639-2 tel
 * <AUTHOR> [@kranthilakum](https://github.com/kranthilakum)
 */
const te = (exports.te = {
  code: "te",
  formatDistance: _index.formatDistance,
  formatLong: _index2.formatLong,
  formatRelative: _index3.formatRelative,
  localize: _index4.localize,
  match: _index5.match,
  options: {
    weekStartsOn: 0 /* Sunday */,
    firstWeekContainsDate: 1,
  },
});
