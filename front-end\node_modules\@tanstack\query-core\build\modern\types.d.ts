export { aV as CancelOptions, E as DataTag, z as DefaultError, aU as DefaultOptions, a9 as DefaultedInfiniteQueryObserverOptions, a7 as DefaultedQueryObserverOptions, aC as DefinedInfiniteQueryObserverResult, au as DefinedQueryObserverResult, I as Enabled, ac as EnsureInfiniteQueryDataOptions, ab as EnsureQueryDataOptions, ad as FetchInfiniteQueryOptions, ak as FetchNextPageOptions, al as FetchPreviousPageOptions, aa as FetchQueryOptions, an as FetchStatus, X as GetNextPageParamFunction, W as GetPreviousPageParamFunction, Y as InfiniteData, aw as InfiniteQueryObserverBaseResult, az as InfiniteQueryObserverLoadingErrorResult, ay as InfiniteQueryObserverLoadingResult, a8 as InfiniteQueryObserverOptions, ax as InfiniteQueryObserverPendingResult, aA as InfiniteQueryObserverRefetchErrorResult, aD as InfiniteQueryObserverResult, aB as InfiniteQueryObserverSuccessResult, a2 as InfiniteQueryPageParamsOptions, L as InitialDataFunction, a1 as InitialPageParam, ai as InvalidateOptions, ag as InvalidateQueryFilters, aM as MutateFunction, aL as MutateOptions, aI as MutationFunction, aE as MutationKey, aH as MutationMeta, aN as MutationObserverBaseResult, aQ as MutationObserverErrorResult, aO as MutationObserverIdleResult, aP as MutationObserverLoadingResult, aK as MutationObserverOptions, aS as MutationObserverResult, aR as MutationObserverSuccessResult, aJ as MutationOptions, aG as MutationScope, aF as MutationStatus, _ as NetworkMode, N as NoInfer, aY as NotifyEvent, aX as NotifyEventType, $ as NotifyOnChangeProps, O as OmitKeyof, a6 as Optional, y as Override, P as PlaceholderDataFunction, T as QueriesPlaceholderDataFunction, aT as QueryClientConfig, F as QueryFunction, K as QueryFunctionContext, A as QueryKey, V as QueryKeyHashFunction, Z as QueryMeta, ao as QueryObserverBaseResult, ar as QueryObserverLoadingErrorResult, aq as QueryObserverLoadingResult, a4 as QueryObserverOptions, ap as QueryObserverPendingResult, as as QueryObserverRefetchErrorResult, av as QueryObserverResult, at as QueryObserverSuccessResult, a0 as QueryOptions, J as QueryPersister, am as QueryStatus, af as RefetchOptions, ah as RefetchQueryFilters, R as Register, aj as ResetOptions, ae as ResultOptions, aW as SetDataOptions, G as StaleTime, a3 as ThrowOnError, a5 as WithRequired, B as dataTagSymbol } from './hydration-mKPlgzt9.js';
import './removable.js';
import './subscribable.js';
