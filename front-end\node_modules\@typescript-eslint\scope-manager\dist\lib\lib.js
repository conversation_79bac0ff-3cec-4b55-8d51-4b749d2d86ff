"use strict";
// THIS CODE WAS AUTOMATICALLY GENERATED
// DO NOT EDIT THIS CODE BY HAND
// RUN THE FOLLOWING COMMAND FROM THE WORKSPACE ROOT TO REGENERATE:
// npx nx generate-lib repo
Object.defineProperty(exports, "__esModule", { value: true });
exports.lib = void 0;
const dom_1 = require("./dom");
const es5_1 = require("./es5");
const scripthost_1 = require("./scripthost");
const webworker_importscripts_1 = require("./webworker.importscripts");
exports.lib = {
    ...es5_1.es5,
    ...dom_1.dom,
    ...webworker_importscripts_1.webworker_importscripts,
    ...scripthost_1.scripthost,
};
//# sourceMappingURL=lib.js.map