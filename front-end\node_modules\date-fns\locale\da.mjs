import { formatDistance } from "./da/_lib/formatDistance.mjs";
import { formatLong } from "./da/_lib/formatLong.mjs";
import { formatRelative } from "./da/_lib/formatRelative.mjs";
import { localize } from "./da/_lib/localize.mjs";
import { match } from "./da/_lib/match.mjs";

/**
 * @category Locales
 * @summary Danish locale.
 * @language Danish
 * @iso-639-2 dan
 * <AUTHOR> [@MathiasKandelborg](https://github.com/MathiasKandelborg)
 * <AUTHOR> [@Andersbiha](https://github.com/Andersbiha)
 * <AUTHOR>
 * <AUTHOR>
 */
export const da = {
  code: "da",
  formatDistance: formatDistance,
  formatLong: formatLong,
  formatRelative: formatRelative,
  localize: localize,
  match: match,
  options: {
    weekStartsOn: 1 /* Monday */,
    firstWeekContainsDate: 4,
  },
};

// Fallback for modularized imports:
export default da;
