import { formatDistance } from "./ar-TN/_lib/formatDistance.mjs";
import { formatLong } from "./ar-TN/_lib/formatLong.mjs";
import { formatRelative } from "./ar-TN/_lib/formatRelative.mjs";
import { localize } from "./ar-TN/_lib/localize.mjs";
import { match } from "./ar-TN/_lib/match.mjs";

/**
 * @category Locales
 * @summary Arabic locale (Tunisian Arabic).
 * @language Arabic
 * @iso-639-2 ara
 * <AUTHOR> [@essana3](https://github.com/essana3)
 */
export const arTN = {
  code: "ar-TN",
  formatDistance: formatDistance,
  formatLong: formatLong,
  formatRelative: formatRelative,
  localize: localize,
  match: match,
  options: {
    weekStartsOn: 1 /* Monday */,
    firstWeekContainsDate: 1,
  },
};

// Fallback for modularized imports:
export default arTN;
