(()=>{var A;function C(B){return C=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(G){return typeof G}:function(G){return G&&typeof Symbol=="function"&&G.constructor===Symbol&&G!==Symbol.prototype?"symbol":typeof G},C(B)}function x(B,G){var H=Object.keys(B);if(Object.getOwnPropertySymbols){var J=Object.getOwnPropertySymbols(B);G&&(J=J.filter(function(X){return Object.getOwnPropertyDescriptor(B,X).enumerable})),H.push.apply(H,J)}return H}function Q(B){for(var G=1;G<arguments.length;G++){var H=arguments[G]!=null?arguments[G]:{};G%2?x(Object(H),!0).forEach(function(J){N(B,J,H[J])}):Object.getOwnPropertyDescriptors?Object.defineProperties(B,Object.getOwnPropertyDescriptors(H)):x(Object(H)).forEach(function(J){Object.defineProperty(B,J,Object.getOwnPropertyDescriptor(H,J))})}return B}function N(B,G,H){if(G=z(G),G in B)Object.defineProperty(B,G,{value:H,enumerable:!0,configurable:!0,writable:!0});else B[G]=H;return B}function z(B){var G=E(B,"string");return C(G)=="symbol"?G:String(G)}function E(B,G){if(C(B)!="object"||!B)return B;var H=B[Symbol.toPrimitive];if(H!==void 0){var J=H.call(B,G||"default");if(C(J)!="object")return J;throw new TypeError("@@toPrimitive must return a primitive value.")}return(G==="string"?String:Number)(B)}var W=Object.defineProperty,JB=function B(G,H){for(var J in H)W(G,J,{get:H[J],enumerable:!0,configurable:!0,set:function X(Y){return H[J]=function(){return Y}}})},S={lessThanXSeconds:{one:"\u0441\u0435\u043A\u0443\u043D\u0434 \u0445\u04AF\u0440\u044D\u0445\u0433\u04AF\u0439",other:"{{count}} \u0441\u0435\u043A\u0443\u043D\u0434 \u0445\u04AF\u0440\u044D\u0445\u0433\u04AF\u0439"},xSeconds:{one:"1 \u0441\u0435\u043A\u0443\u043D\u0434",other:"{{count}} \u0441\u0435\u043A\u0443\u043D\u0434"},halfAMinute:"\u0445\u0430\u0433\u0430\u0441 \u043C\u0438\u043D\u0443\u0442",lessThanXMinutes:{one:"\u043C\u0438\u043D\u0443\u0442 \u0445\u04AF\u0440\u044D\u0445\u0433\u04AF\u0439",other:"{{count}} \u043C\u0438\u043D\u0443\u0442 \u0445\u04AF\u0440\u044D\u0445\u0433\u04AF\u0439"},xMinutes:{one:"1 \u043C\u0438\u043D\u0443\u0442",other:"{{count}} \u043C\u0438\u043D\u0443\u0442"},aboutXHours:{one:"\u043E\u0439\u0440\u043E\u043B\u0446\u043E\u043E\u0433\u043E\u043E\u0440 1 \u0446\u0430\u0433",other:"\u043E\u0439\u0440\u043E\u043B\u0446\u043E\u043E\u0433\u043E\u043E\u0440 {{count}} \u0446\u0430\u0433"},xHours:{one:"1 \u0446\u0430\u0433",other:"{{count}} \u0446\u0430\u0433"},xDays:{one:"1 \u04E9\u0434\u04E9\u0440",other:"{{count}} \u04E9\u0434\u04E9\u0440"},aboutXWeeks:{one:"\u043E\u0439\u0440\u043E\u043B\u0446\u043E\u043E\u0433\u043E\u043E\u0440 1 \u0434\u043E\u043B\u043E\u043E \u0445\u043E\u043D\u043E\u0433",other:"\u043E\u0439\u0440\u043E\u043B\u0446\u043E\u043E\u0433\u043E\u043E\u0440 {{count}} \u0434\u043E\u043B\u043E\u043E \u0445\u043E\u043D\u043E\u0433"},xWeeks:{one:"1 \u0434\u043E\u043B\u043E\u043E \u0445\u043E\u043D\u043E\u0433",other:"{{count}} \u0434\u043E\u043B\u043E\u043E \u0445\u043E\u043D\u043E\u0433"},aboutXMonths:{one:"\u043E\u0439\u0440\u043E\u043B\u0446\u043E\u043E\u0433\u043E\u043E\u0440 1 \u0441\u0430\u0440",other:"\u043E\u0439\u0440\u043E\u043B\u0446\u043E\u043E\u0433\u043E\u043E\u0440 {{count}} \u0441\u0430\u0440"},xMonths:{one:"1 \u0441\u0430\u0440",other:"{{count}} \u0441\u0430\u0440"},aboutXYears:{one:"\u043E\u0439\u0440\u043E\u043B\u0446\u043E\u043E\u0433\u043E\u043E\u0440 1 \u0436\u0438\u043B",other:"\u043E\u0439\u0440\u043E\u043B\u0446\u043E\u043E\u0433\u043E\u043E\u0440 {{count}} \u0436\u0438\u043B"},xYears:{one:"1 \u0436\u0438\u043B",other:"{{count}} \u0436\u0438\u043B"},overXYears:{one:"1 \u0436\u0438\u043B \u0433\u0430\u0440\u0430\u043D",other:"{{count}} \u0436\u0438\u043B \u0433\u0430\u0440\u0430\u043D"},almostXYears:{one:"\u0431\u0430\u0440\u0430\u0433 1 \u0436\u0438\u043B",other:"\u0431\u0430\u0440\u0430\u0433 {{count}} \u0436\u0438\u043B"}},D=function B(G,H,J){var X,Y=S[G];if(typeof Y==="string")X=Y;else if(H===1)X=Y.one;else X=Y.other.replace("{{count}}",String(H));if(J!==null&&J!==void 0&&J.addSuffix){var Z=X.split(" "),T=Z.pop();switch(X=Z.join(" "),T){case"\u0441\u0435\u043A\u0443\u043D\u0434":X+=" \u0441\u0435\u043A\u0443\u043D\u0434\u0438\u0439\u043D";break;case"\u043C\u0438\u043D\u0443\u0442":X+=" \u043C\u0438\u043D\u0443\u0442\u044B\u043D";break;case"\u0446\u0430\u0433":X+=" \u0446\u0430\u0433\u0438\u0439\u043D";break;case"\u04E9\u0434\u04E9\u0440":X+=" \u04E9\u0434\u0440\u0438\u0439\u043D";break;case"\u0441\u0430\u0440":X+=" \u0441\u0430\u0440\u044B\u043D";break;case"\u0436\u0438\u043B":X+=" \u0436\u0438\u043B\u0438\u0439\u043D";break;case"\u0445\u043E\u043D\u043E\u0433":X+=" \u0445\u043E\u043D\u043E\u0433\u0438\u0439\u043D";break;case"\u0433\u0430\u0440\u0430\u043D":X+=" \u0433\u0430\u0440\u0430\u043D\u044B";break;case"\u0445\u04AF\u0440\u044D\u0445\u0433\u04AF\u0439":X+=" \u0445\u04AF\u0440\u044D\u0445\u0433\u04AF\u0439 \u0445\u0443\u0433\u0430\u0446\u0430\u0430\u043D\u044B";break;default:X+=T+"-\u043D"}if(J.comparison&&J.comparison>0)return X+" \u0434\u0430\u0440\u0430\u0430";else return X+" \u04E9\u043C\u043D\u04E9"}return X};function $(B){return function(){var G=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},H=G.width?String(G.width):B.defaultWidth,J=B.formats[H]||B.formats[B.defaultWidth];return J}}var M={full:"y '\u043E\u043D\u044B' MMMM'\u044B\u043D' d, EEEE '\u0433\u0430\u0440\u0430\u0433'",long:"y '\u043E\u043D\u044B' MMMM'\u044B\u043D' d",medium:"y '\u043E\u043D\u044B' MMM'\u044B\u043D' d",short:"y.MM.dd"},R={full:"H:mm:ss zzzz",long:"H:mm:ss z",medium:"H:mm:ss",short:"H:mm"},L={full:"{{date}} {{time}}",long:"{{date}} {{time}}",medium:"{{date}} {{time}}",short:"{{date}} {{time}}"},V={date:$({formats:M,defaultWidth:"full"}),time:$({formats:R,defaultWidth:"full"}),dateTime:$({formats:L,defaultWidth:"full"})},j={lastWeek:"'\u04E9\u043D\u0433\u04E9\u0440\u0441\u04E9\u043D' eeee '\u0433\u0430\u0440\u0430\u0433\u0438\u0439\u043D' p '\u0446\u0430\u0433\u0442'",yesterday:"'\u04E9\u0447\u0438\u0433\u0434\u04E9\u0440' p '\u0446\u0430\u0433\u0442'",today:"'\u04E9\u043D\u04E9\u04E9\u0434\u04E9\u0440' p '\u0446\u0430\u0433\u0442'",tomorrow:"'\u043C\u0430\u0440\u0433\u0430\u0430\u0448' p '\u0446\u0430\u0433\u0442'",nextWeek:"'\u0438\u0440\u044D\u0445' eeee '\u0433\u0430\u0440\u0430\u0433\u0438\u0439\u043D' p '\u0446\u0430\u0433\u0442'",other:"P"},_=function B(G,H,J,X){return j[G]};function I(B){return function(G,H){var J=H!==null&&H!==void 0&&H.context?String(H.context):"standalone",X;if(J==="formatting"&&B.formattingValues){var Y=B.defaultFormattingWidth||B.defaultWidth,Z=H!==null&&H!==void 0&&H.width?String(H.width):Y;X=B.formattingValues[Z]||B.formattingValues[Y]}else{var T=B.defaultWidth,q=H!==null&&H!==void 0&&H.width?String(H.width):B.defaultWidth;X=B.values[q]||B.values[T]}var U=B.argumentCallback?B.argumentCallback(G):G;return X[U]}}var f={narrow:["\u041D\u0422\u04E8","\u041D\u0422"],abbreviated:["\u041D\u0422\u04E8","\u041D\u0422"],wide:["\u043D\u0438\u0439\u0442\u0438\u0439\u043D \u0442\u043E\u043E\u043B\u043B\u044B\u043D \u04E9\u043C\u043D\u04E9\u0445","\u043D\u0438\u0439\u0442\u0438\u0439\u043D \u0442\u043E\u043E\u043B\u043B\u044B\u043D"]},F={narrow:["I","II","III","IV"],abbreviated:["I \u0443\u043B\u0438\u0440\u0430\u043B","II \u0443\u043B\u0438\u0440\u0430\u043B","III \u0443\u043B\u0438\u0440\u0430\u043B","IV \u0443\u043B\u0438\u0440\u0430\u043B"],wide:["1-\u0440 \u0443\u043B\u0438\u0440\u0430\u043B","2-\u0440 \u0443\u043B\u0438\u0440\u0430\u043B","3-\u0440 \u0443\u043B\u0438\u0440\u0430\u043B","4-\u0440 \u0443\u043B\u0438\u0440\u0430\u043B"]},P={narrow:["I","II","III","IV","V","VI","VII","VIII","IX","X","XI","XII"],abbreviated:["1-\u0440 \u0441\u0430\u0440","2-\u0440 \u0441\u0430\u0440","3-\u0440 \u0441\u0430\u0440","4-\u0440 \u0441\u0430\u0440","5-\u0440 \u0441\u0430\u0440","6-\u0440 \u0441\u0430\u0440","7-\u0440 \u0441\u0430\u0440","8-\u0440 \u0441\u0430\u0440","9-\u0440 \u0441\u0430\u0440","10-\u0440 \u0441\u0430\u0440","11-\u0440 \u0441\u0430\u0440","12-\u0440 \u0441\u0430\u0440"],wide:["\u041D\u044D\u0433\u0434\u04AF\u0433\u044D\u044D\u0440 \u0441\u0430\u0440","\u0425\u043E\u0451\u0440\u0434\u0443\u0433\u0430\u0430\u0440 \u0441\u0430\u0440","\u0413\u0443\u0440\u0430\u0432\u0434\u0443\u0433\u0430\u0430\u0440 \u0441\u0430\u0440","\u0414\u04E9\u0440\u04E9\u0432\u0434\u04AF\u0433\u044D\u044D\u0440 \u0441\u0430\u0440","\u0422\u0430\u0432\u0434\u0443\u0433\u0430\u0430\u0440 \u0441\u0430\u0440","\u0417\u0443\u0440\u0433\u0430\u0430\u0434\u0443\u0433\u0430\u0430\u0440 \u0441\u0430\u0440","\u0414\u043E\u043B\u043E\u043E\u0434\u0443\u0433\u0430\u0430\u0440 \u0441\u0430\u0440","\u041D\u0430\u0439\u043C\u0434\u0443\u0433\u0430\u0430\u0440 \u0441\u0430\u0440","\u0415\u0441\u0434\u04AF\u0433\u044D\u044D\u0440 \u0441\u0430\u0440","\u0410\u0440\u0430\u0432\u0434\u0443\u0433\u0430\u0430\u0440 \u0441\u0430\u0440","\u0410\u0440\u0432\u0430\u043D\u043D\u044D\u0433\u0434\u04AF\u0433\u044D\u044D\u0440 \u0441\u0430\u0440","\u0410\u0440\u0432\u0430\u043D \u0445\u043E\u0451\u0440\u0434\u0443\u0433\u0430\u0430\u0440 \u0441\u0430\u0440"]},v={narrow:["I","II","III","IV","V","VI","VII","VIII","IX","X","XI","XII"],abbreviated:["1-\u0440 \u0441\u0430\u0440","2-\u0440 \u0441\u0430\u0440","3-\u0440 \u0441\u0430\u0440","4-\u0440 \u0441\u0430\u0440","5-\u0440 \u0441\u0430\u0440","6-\u0440 \u0441\u0430\u0440","7-\u0440 \u0441\u0430\u0440","8-\u0440 \u0441\u0430\u0440","9-\u0440 \u0441\u0430\u0440","10-\u0440 \u0441\u0430\u0440","11-\u0440 \u0441\u0430\u0440","12-\u0440 \u0441\u0430\u0440"],wide:["\u043D\u044D\u0433\u0434\u04AF\u0433\u044D\u044D\u0440 \u0441\u0430\u0440","\u0445\u043E\u0451\u0440\u0434\u0443\u0433\u0430\u0430\u0440 \u0441\u0430\u0440","\u0433\u0443\u0440\u0430\u0432\u0434\u0443\u0433\u0430\u0430\u0440 \u0441\u0430\u0440","\u0434\u04E9\u0440\u04E9\u0432\u0434\u04AF\u0433\u044D\u044D\u0440 \u0441\u0430\u0440","\u0442\u0430\u0432\u0434\u0443\u0433\u0430\u0430\u0440 \u0441\u0430\u0440","\u0437\u0443\u0440\u0433\u0430\u0430\u0434\u0443\u0433\u0430\u0430\u0440 \u0441\u0430\u0440","\u0434\u043E\u043B\u043E\u043E\u0434\u0443\u0433\u0430\u0430\u0440 \u0441\u0430\u0440","\u043D\u0430\u0439\u043C\u0434\u0443\u0433\u0430\u0430\u0440 \u0441\u0430\u0440","\u0435\u0441\u0434\u04AF\u0433\u044D\u044D\u0440 \u0441\u0430\u0440","\u0430\u0440\u0430\u0432\u0434\u0443\u0433\u0430\u0430\u0440 \u0441\u0430\u0440","\u0430\u0440\u0432\u0430\u043D\u043D\u044D\u0433\u0434\u04AF\u0433\u044D\u044D\u0440 \u0441\u0430\u0440","\u0430\u0440\u0432\u0430\u043D \u0445\u043E\u0451\u0440\u0434\u0443\u0433\u0430\u0430\u0440 \u0441\u0430\u0440"]},w={narrow:["\u041D","\u0414","\u041C","\u041B","\u041F","\u0411","\u0411"],short:["\u041D\u044F","\u0414\u0430","\u041C\u044F","\u041B\u0445","\u041F\u04AF","\u0411\u0430","\u0411\u044F"],abbreviated:["\u041D\u044F\u043C","\u0414\u0430\u0432","\u041C\u044F\u0433","\u041B\u0445\u0430","\u041F\u04AF\u0440","\u0411\u0430\u0430","\u0411\u044F\u043C"],wide:["\u041D\u044F\u043C","\u0414\u0430\u0432\u0430\u0430","\u041C\u044F\u0433\u043C\u0430\u0440","\u041B\u0445\u0430\u0433\u0432\u0430","\u041F\u04AF\u0440\u044D\u0432","\u0411\u0430\u0430\u0441\u0430\u043D","\u0411\u044F\u043C\u0431\u0430"]},k={narrow:["\u041D","\u0414","\u041C","\u041B","\u041F","\u0411","\u0411"],short:["\u041D\u044F","\u0414\u0430","\u041C\u044F","\u041B\u0445","\u041F\u04AF","\u0411\u0430","\u0411\u044F"],abbreviated:["\u041D\u044F\u043C","\u0414\u0430\u0432","\u041C\u044F\u0433","\u041B\u0445\u0430","\u041F\u04AF\u0440","\u0411\u0430\u0430","\u0411\u044F\u043C"],wide:["\u043D\u044F\u043C","\u0434\u0430\u0432\u0430\u0430","\u043C\u044F\u0433\u043C\u0430\u0440","\u043B\u0445\u0430\u0433\u0432\u0430","\u043F\u04AF\u0440\u044D\u0432","\u0431\u0430\u0430\u0441\u0430\u043D","\u0431\u044F\u043C\u0431\u0430"]},b={narrow:{am:"\u04AF.\u04E9.",pm:"\u04AF.\u0445.",midnight:"\u0448\u04E9\u043D\u04E9 \u0434\u0443\u043D\u0434",noon:"\u04AF\u0434 \u0434\u0443\u043D\u0434",morning:"\u04E9\u0433\u043B\u04E9\u04E9",afternoon:"\u04E9\u0434\u04E9\u0440",evening:"\u043E\u0440\u043E\u0439",night:"\u0448\u04E9\u043D\u04E9"},abbreviated:{am:"\u04AF.\u04E9.",pm:"\u04AF.\u0445.",midnight:"\u0448\u04E9\u043D\u04E9 \u0434\u0443\u043D\u0434",noon:"\u04AF\u0434 \u0434\u0443\u043D\u0434",morning:"\u04E9\u0433\u043B\u04E9\u04E9",afternoon:"\u04E9\u0434\u04E9\u0440",evening:"\u043E\u0440\u043E\u0439",night:"\u0448\u04E9\u043D\u04E9"},wide:{am:"\u04AF.\u04E9.",pm:"\u04AF.\u0445.",midnight:"\u0448\u04E9\u043D\u04E9 \u0434\u0443\u043D\u0434",noon:"\u04AF\u0434 \u0434\u0443\u043D\u0434",morning:"\u04E9\u0433\u043B\u04E9\u04E9",afternoon:"\u04E9\u0434\u04E9\u0440",evening:"\u043E\u0440\u043E\u0439",night:"\u0448\u04E9\u043D\u04E9"}},h=function B(G,H){return String(G)},m={ordinalNumber:h,era:I({values:f,defaultWidth:"wide"}),quarter:I({values:F,defaultWidth:"wide",argumentCallback:function B(G){return G-1}}),month:I({values:P,defaultWidth:"wide",formattingValues:v,defaultFormattingWidth:"wide"}),day:I({values:w,defaultWidth:"wide",formattingValues:k,defaultFormattingWidth:"wide"}),dayPeriod:I({values:b,defaultWidth:"wide"})};function O(B){return function(G){var H=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},J=H.width,X=J&&B.matchPatterns[J]||B.matchPatterns[B.defaultMatchWidth],Y=G.match(X);if(!Y)return null;var Z=Y[0],T=J&&B.parsePatterns[J]||B.parsePatterns[B.defaultParseWidth],q=Array.isArray(T)?y(T,function(K){return K.test(Z)}):c(T,function(K){return K.test(Z)}),U;U=B.valueCallback?B.valueCallback(q):q,U=H.valueCallback?H.valueCallback(U):U;var HB=G.slice(Z.length);return{value:U,rest:HB}}}function c(B,G){for(var H in B)if(Object.prototype.hasOwnProperty.call(B,H)&&G(B[H]))return H;return}function y(B,G){for(var H=0;H<B.length;H++)if(G(B[H]))return H;return}function p(B){return function(G){var H=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},J=G.match(B.matchPattern);if(!J)return null;var X=J[0],Y=G.match(B.parsePattern);if(!Y)return null;var Z=B.valueCallback?B.valueCallback(Y[0]):Y[0];Z=H.valueCallback?H.valueCallback(Z):Z;var T=G.slice(X.length);return{value:Z,rest:T}}}var g=/\d+/i,d=/\d+/i,u={narrow:/^(нтө|нт)/i,abbreviated:/^(нтө|нт)/i,wide:/^(нийтийн тооллын өмнө|нийтийн тооллын)/i},l={any:[/^(нтө|нийтийн тооллын өмнө)/i,/^(нт|нийтийн тооллын)/i]},i={narrow:/^(iv|iii|ii|i)/i,abbreviated:/^(iv|iii|ii|i) улирал/i,wide:/^[1-4]-р улирал/i},n={any:[/^(i(\s|$)|1)/i,/^(ii(\s|$)|2)/i,/^(iii(\s|$)|3)/i,/^(iv(\s|$)|4)/i]},s={narrow:/^(xii|xi|x|ix|viii|vii|vi|v|iv|iii|ii|i)/i,abbreviated:/^(1-р сар|2-р сар|3-р сар|4-р сар|5-р сар|6-р сар|7-р сар|8-р сар|9-р сар|10-р сар|11-р сар|12-р сар)/i,wide:/^(нэгдүгээр сар|хоёрдугаар сар|гуравдугаар сар|дөрөвдүгээр сар|тавдугаар сар|зургаадугаар сар|долоодугаар сар|наймдугаар сар|есдүгээр сар|аравдугаар сар|арван нэгдүгээр сар|арван хоёрдугаар сар)/i},o={narrow:[/^i$/i,/^ii$/i,/^iii$/i,/^iv$/i,/^v$/i,/^vi$/i,/^vii$/i,/^viii$/i,/^ix$/i,/^x$/i,/^xi$/i,/^xii$/i],any:[/^(1|нэгдүгээр)/i,/^(2|хоёрдугаар)/i,/^(3|гуравдугаар)/i,/^(4|дөрөвдүгээр)/i,/^(5|тавдугаар)/i,/^(6|зургаадугаар)/i,/^(7|долоодугаар)/i,/^(8|наймдугаар)/i,/^(9|есдүгээр)/i,/^(10|аравдугаар)/i,/^(11|арван нэгдүгээр)/i,/^(12|арван хоёрдугаар)/i]},r={narrow:/^[ндмлпбб]/i,short:/^(ня|да|мя|лх|пү|ба|бя)/i,abbreviated:/^(ням|дав|мяг|лха|пүр|баа|бям)/i,wide:/^(ням|даваа|мягмар|лхагва|пүрэв|баасан|бямба)/i},a={narrow:[/^н/i,/^д/i,/^м/i,/^л/i,/^п/i,/^б/i,/^б/i],any:[/^ня/i,/^да/i,/^мя/i,/^лх/i,/^пү/i,/^ба/i,/^бя/i]},e={narrow:/^(ү\.ө\.|ү\.х\.|шөнө дунд|үд дунд|өглөө|өдөр|орой|шөнө)/i,any:/^(ү\.ө\.|ү\.х\.|шөнө дунд|үд дунд|өглөө|өдөр|орой|шөнө)/i},t={any:{am:/^ү\.ө\./i,pm:/^ү\.х\./i,midnight:/^шөнө дунд/i,noon:/^үд дунд/i,morning:/өглөө/i,afternoon:/өдөр/i,evening:/орой/i,night:/шөнө/i}},BB={ordinalNumber:p({matchPattern:g,parsePattern:d,valueCallback:function B(G){return parseInt(G,10)}}),era:O({matchPatterns:u,defaultMatchWidth:"wide",parsePatterns:l,defaultParseWidth:"any"}),quarter:O({matchPatterns:i,defaultMatchWidth:"wide",parsePatterns:n,defaultParseWidth:"any",valueCallback:function B(G){return G+1}}),month:O({matchPatterns:s,defaultMatchWidth:"wide",parsePatterns:o,defaultParseWidth:"any"}),day:O({matchPatterns:r,defaultMatchWidth:"wide",parsePatterns:a,defaultParseWidth:"any"}),dayPeriod:O({matchPatterns:e,defaultMatchWidth:"any",parsePatterns:t,defaultParseWidth:"any"})},GB={code:"mn",formatDistance:D,formatLong:V,formatRelative:_,localize:m,match:BB,options:{weekStartsOn:1,firstWeekContainsDate:1}};window.dateFns=Q(Q({},window.dateFns),{},{locale:Q(Q({},(A=window.dateFns)===null||A===void 0?void 0:A.locale),{},{mn:GB})})})();

//# debugId=23521DCA12F128AF64756E2164756E21
