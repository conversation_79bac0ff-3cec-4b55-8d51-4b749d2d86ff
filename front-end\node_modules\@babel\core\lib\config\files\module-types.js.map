{"version": 3, "names": ["_async", "require", "_path", "data", "_url", "_semver", "_debug", "_rewriteStackTrace", "_configError", "_transformFile", "asyncGeneratorStep", "gen", "resolve", "reject", "_next", "_throw", "key", "arg", "info", "value", "error", "done", "Promise", "then", "_asyncToGenerator", "fn", "self", "args", "arguments", "apply", "err", "undefined", "debug", "buildDebug", "import_", "_unused", "supportsESM", "exports", "semver", "satisfies", "process", "versions", "node", "LOADING_CJS_FILES", "Set", "loadCjsDefault", "filepath", "has", "module", "add", "endHiddenCallStack", "delete", "_module", "__esModule", "default", "loadMjsDefault", "_loadMjsDefault", "url", "pathToFileURL", "toString", "ConfigError", "_x", "loadCodeDefault", "asyncError", "path", "extname", "loadCtsDefault", "e", "code", "isAsync", "waitFor", "ext", "hasTsSupport", "extensions", "handler", "opts", "babelrc", "configFile", "sourceType", "sourceMaps", "sourceFileName", "basename", "presets", "getTSPreset", "Object", "assign", "onlyRemoveTypeImports", "optimizeConstEnums", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "m", "filename", "endsWith", "_compile", "transformFileSync", "packageJson", "lt", "version", "console", "message", "pnp"], "sources": ["../../../src/config/files/module-types.ts"], "sourcesContent": ["import { isAsync, waitFor } from \"../../gensync-utils/async.ts\";\nimport type { <PERSON><PERSON> } from \"gensync\";\nimport path from \"path\";\nimport { pathToFileURL } from \"url\";\nimport { createRequire } from \"module\";\nimport semver from \"semver\";\nimport buildDebug from \"debug\";\n\nimport { endHiddenCallStack } from \"../../errors/rewrite-stack-trace.ts\";\nimport ConfigError from \"../../errors/config-error.ts\";\n\nimport type { InputOptions } from \"../index.ts\";\nimport { transformFileSync } from \"../../transform-file.ts\";\n\nconst debug = buildDebug(\"babel:config:loading:files:module-types\");\n\nconst require = createRequire(import.meta.url);\n\nif (!process.env.BABEL_8_BREAKING) {\n  try {\n    // Old Node.js versions don't support import() syntax.\n    // eslint-disable-next-line no-var\n    var import_:\n      | ((specifier: string | URL) => any)\n      | undefined = require(\"./import.cjs\");\n  } catch {}\n}\n\nexport const supportsESM = semver.satisfies(\n  process.versions.node,\n  // older versions, starting from 10, support the dynamic\n  // import syntax but always return a rejected promise.\n  \"^12.17 || >=13.2\",\n);\n\nconst LOADING_CJS_FILES = new Set();\n\nfunction loadCjsDefault(filepath: string) {\n  // The `require()` call below can make this code reentrant if a require hook\n  // like @babel/register has been loaded into the system. That would cause\n  // Babel to attempt to compile the `.babelrc.js` file as it loads below. To\n  // cover this case, we auto-ignore re-entrant config processing. ESM loaders\n  // do not have this problem, because loaders do not apply to themselves.\n  if (LOADING_CJS_FILES.has(filepath)) {\n    debug(\"Auto-ignoring usage of config %o.\", filepath);\n    return {};\n  }\n\n  let module;\n  try {\n    LOADING_CJS_FILES.add(filepath);\n    module = endHiddenCallStack(require)(filepath);\n  } finally {\n    LOADING_CJS_FILES.delete(filepath);\n  }\n\n  if (process.env.BABEL_8_BREAKING) {\n    return module?.__esModule ? module.default : module;\n  } else {\n    return module?.__esModule\n      ? module.default ||\n          /* fallbackToTranspiledModule */ (arguments[1] ? module : undefined)\n      : module;\n  }\n}\n\nconst loadMjsDefault = endHiddenCallStack(async function loadMjsDefault(\n  filepath: string,\n) {\n  const url = pathToFileURL(filepath).toString();\n\n  if (process.env.BABEL_8_BREAKING) {\n    return (await import(url)).default;\n  } else {\n    if (!import_) {\n      throw new ConfigError(\n        \"Internal error: Native ECMAScript modules aren't supported by this platform.\\n\",\n        filepath,\n      );\n    }\n\n    return (await import_(url)).default;\n  }\n});\n\nexport default function* loadCodeDefault(\n  filepath: string,\n  asyncError: string,\n): Handler<unknown> {\n  switch (path.extname(filepath)) {\n    case \".cjs\":\n      if (process.env.BABEL_8_BREAKING) {\n        return loadCjsDefault(filepath);\n      } else {\n        return loadCjsDefault(\n          filepath,\n          // @ts-ignore(Babel 7 vs Babel 8) Removed in Babel 8\n          /* fallbackToTranspiledModule */ arguments[2],\n        );\n      }\n    case \".mjs\":\n      break;\n    case \".cts\":\n      return loadCtsDefault(filepath);\n    default:\n      try {\n        if (process.env.BABEL_8_BREAKING) {\n          return loadCjsDefault(filepath);\n        } else {\n          return loadCjsDefault(\n            filepath,\n            // @ts-ignore(Babel 7 vs Babel 8) Removed in Babel 8\n            /* fallbackToTranspiledModule */ arguments[2],\n          );\n        }\n      } catch (e) {\n        if (e.code !== \"ERR_REQUIRE_ESM\") throw e;\n      }\n  }\n  if (yield* isAsync()) {\n    return yield* waitFor(loadMjsDefault(filepath));\n  }\n  throw new ConfigError(asyncError, filepath);\n}\n\nfunction loadCtsDefault(filepath: string) {\n  const ext = \".cts\";\n  const hasTsSupport = !!(\n    require.extensions[\".ts\"] ||\n    require.extensions[\".cts\"] ||\n    require.extensions[\".mts\"]\n  );\n\n  let handler: NodeJS.RequireExtensions[\"\"];\n\n  if (!hasTsSupport) {\n    const opts: InputOptions = {\n      babelrc: false,\n      configFile: false,\n      sourceType: \"unambiguous\",\n      sourceMaps: \"inline\",\n      sourceFileName: path.basename(filepath),\n      presets: [\n        [\n          getTSPreset(filepath),\n          {\n            onlyRemoveTypeImports: true,\n            optimizeConstEnums: true,\n            ...(process.env.BABEL_8_BREAKING\n              ? {}\n              : { allowDeclareFields: true }),\n          },\n        ],\n      ],\n    };\n\n    handler = function (m, filename) {\n      // If we want to support `.ts`, `.d.ts` must be handled specially.\n      if (handler && filename.endsWith(ext)) {\n        try {\n          // @ts-expect-error Undocumented API\n          return m._compile(\n            transformFileSync(filename, {\n              ...opts,\n              filename,\n            }).code,\n            filename,\n          );\n        } catch (error) {\n          if (!hasTsSupport) {\n            // TODO(Babel 8): Add this as an optional peer dependency\n            // eslint-disable-next-line import/no-extraneous-dependencies\n            const packageJson = require(\"@babel/preset-typescript/package.json\");\n            if (semver.lt(packageJson.version, \"7.21.4\")) {\n              console.error(\n                \"`.cts` configuration file failed to load, please try to update `@babel/preset-typescript`.\",\n              );\n            }\n          }\n          throw error;\n        }\n      }\n      return require.extensions[\".js\"](m, filename);\n    };\n    require.extensions[ext] = handler;\n  }\n  try {\n    return loadCjsDefault(filepath);\n  } finally {\n    if (!hasTsSupport) {\n      if (require.extensions[ext] === handler) delete require.extensions[ext];\n      handler = undefined;\n    }\n  }\n}\n\nfunction getTSPreset(filepath: string) {\n  try {\n    // eslint-disable-next-line import/no-extraneous-dependencies\n    return require(\"@babel/preset-typescript\");\n  } catch (error) {\n    if (error.code !== \"MODULE_NOT_FOUND\") throw error;\n\n    let message =\n      \"You appear to be using a .cts file as Babel configuration, but the `@babel/preset-typescript` package was not found: please install it!\";\n\n    if (!process.env.BABEL_8_BREAKING) {\n      if (process.versions.pnp) {\n        // Using Yarn PnP, which doesn't allow requiring packages that are not\n        // explicitly specified as dependencies.\n        message += `\nIf you are using Yarn Plug'n'Play, you may also need to add the following configuration to your .yarnrc.yml file:\n\npackageExtensions:\n\\t\"@babel/core@*\":\n\\t\\tpeerDependencies:\n\\t\\t\\t\"@babel/preset-typescript\": \"*\"\n`;\n      }\n    }\n\n    throw new ConfigError(message, filepath);\n  }\n}\n"], "mappings": ";;;;;;;AAAA,IAAAA,MAAA,GAAAC,OAAA;AAEA,SAAAC,MAAA;EAAA,MAAAC,IAAA,GAAAF,OAAA;EAAAC,KAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAC,KAAA;EAAA,MAAAD,IAAA,GAAAF,OAAA;EAAAG,IAAA,YAAAA,CAAA;IAAA,OAAAD,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,SAAAE,QAAA;EAAA,MAAAF,IAAA,GAAAF,OAAA;EAAAI,OAAA,YAAAA,CAAA;IAAA,OAAAF,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAG,OAAA;EAAA,MAAAH,IAAA,GAAAF,OAAA;EAAAK,MAAA,YAAAA,CAAA;IAAA,OAAAH,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,IAAAI,kBAAA,GAAAN,OAAA;AACA,IAAAO,YAAA,GAAAP,OAAA;AAGA,IAAAQ,cAAA,GAAAR,OAAA;AAA4D,SAAAS,mBAAAC,GAAA,EAAAC,OAAA,EAAAC,MAAA,EAAAC,KAAA,EAAAC,MAAA,EAAAC,GAAA,EAAAC,GAAA,cAAAC,IAAA,GAAAP,GAAA,CAAAK,GAAA,EAAAC,GAAA,OAAAE,KAAA,GAAAD,IAAA,CAAAC,KAAA,WAAAC,KAAA,IAAAP,MAAA,CAAAO,KAAA,iBAAAF,IAAA,CAAAG,IAAA,IAAAT,OAAA,CAAAO,KAAA,YAAAG,OAAA,CAAAV,OAAA,CAAAO,KAAA,EAAAI,IAAA,CAAAT,KAAA,EAAAC,MAAA;AAAA,SAAAS,kBAAAC,EAAA,6BAAAC,IAAA,SAAAC,IAAA,GAAAC,SAAA,aAAAN,OAAA,WAAAV,OAAA,EAAAC,MAAA,QAAAF,GAAA,GAAAc,EAAA,CAAAI,KAAA,CAAAH,IAAA,EAAAC,IAAA,YAAAb,MAAAK,KAAA,IAAAT,kBAAA,CAAAC,GAAA,EAAAC,OAAA,EAAAC,MAAA,EAAAC,KAAA,EAAAC,MAAA,UAAAI,KAAA,cAAAJ,OAAAe,GAAA,IAAApB,kBAAA,CAAAC,GAAA,EAAAC,OAAA,EAAAC,MAAA,EAAAC,KAAA,EAAAC,MAAA,WAAAe,GAAA,KAAAhB,KAAA,CAAAiB,SAAA;AAE5D,MAAMC,KAAK,GAAGC,OAASA,CAAC,CAAC,yCAAyC,CAAC;AAIhC;EACjC,IAAI;IAGF,IAAIC,OAES,GAAGjC,OAAO,CAAC,cAAc,CAAC;EACzC,CAAC,CAAC,OAAAkC,OAAA,EAAM,CAAC;AACX;AAEO,MAAMC,WAAW,GAAAC,OAAA,CAAAD,WAAA,GAAGE,QAAKA,CAAC,CAACC,SAAS,CACzCC,OAAO,CAACC,QAAQ,CAACC,IAAI,EAGrB,kBACF,CAAC;AAED,MAAMC,iBAAiB,GAAG,IAAIC,GAAG,CAAC,CAAC;AAEnC,SAASC,cAAcA,CAACC,QAAgB,EAAE;EAMxC,IAAIH,iBAAiB,CAACI,GAAG,CAACD,QAAQ,CAAC,EAAE;IACnCd,KAAK,CAAC,mCAAmC,EAAEc,QAAQ,CAAC;IACpD,OAAO,CAAC,CAAC;EACX;EAEA,IAAIE,MAAM;EACV,IAAI;IACFL,iBAAiB,CAACM,GAAG,CAACH,QAAQ,CAAC;IAC/BE,MAAM,GAAG,IAAAE,qCAAkB,EAACjD,OAAO,CAAC,CAAC6C,QAAQ,CAAC;EAChD,CAAC,SAAS;IACRH,iBAAiB,CAACQ,MAAM,CAACL,QAAQ,CAAC;EACpC;EAIO;IAAA,IAAAM,OAAA;IACL,OAAO,CAAAA,OAAA,GAAAJ,MAAM,aAANI,OAAA,CAAQC,UAAU,GACrBL,MAAM,CAACM,OAAO,KACsB1B,SAAS,CAAC,CAAC,CAAC,GAAGoB,MAAM,GAAGjB,SAAS,CAAC,GACtEiB,MAAM;EACZ;AACF;AAEA,MAAMO,cAAc,GAAG,IAAAL,qCAAkB;EAAA,IAAAM,eAAA,GAAAhC,iBAAA,CAAC,WACxCsB,QAAgB,EAChB;IACA,MAAMW,GAAG,GAAG,IAAAC,oBAAa,EAACZ,QAAQ,CAAC,CAACa,QAAQ,CAAC,CAAC;IAIvC;MACL,IAAI,CAACzB,OAAO,EAAE;QACZ,MAAM,IAAI0B,oBAAW,CACnB,gFAAgF,EAChFd,QACF,CAAC;MACH;MAEA,OAAO,OAAOZ,OAAO,CAACuB,GAAG,CAAC,EAAEH,OAAO;IACrC;EACF,CAAC;EAAA,SAjBwDC,cAAcA,CAAAM,EAAA;IAAA,OAAAL,eAAA,CAAA3B,KAAA,OAAAD,SAAA;EAAA;EAAA,OAAd2B,cAAc;AAAA,GAiBtE,CAAC;AAEa,UAAUO,eAAeA,CACtChB,QAAgB,EAChBiB,UAAkB,EACA;EAClB,QAAQC,MAAGA,CAAC,CAACC,OAAO,CAACnB,QAAQ,CAAC;IAC5B,KAAK,MAAM;MAGF;QACL,OAAOD,cAAc,CACnBC,QAAQ,EAEyBlB,SAAS,CAAC,CAAC,CAC9C,CAAC;MACH;IACF,KAAK,MAAM;MACT;IACF,KAAK,MAAM;MACT,OAAOsC,cAAc,CAACpB,QAAQ,CAAC;IACjC;MACE,IAAI;QAGK;UACL,OAAOD,cAAc,CACnBC,QAAQ,EAEyBlB,SAAS,CAAC,CAAC,CAC9C,CAAC;QACH;MACF,CAAC,CAAC,OAAOuC,CAAC,EAAE;QACV,IAAIA,CAAC,CAACC,IAAI,KAAK,iBAAiB,EAAE,MAAMD,CAAC;MAC3C;EACJ;EACA,IAAI,OAAO,IAAAE,cAAO,EAAC,CAAC,EAAE;IACpB,OAAO,OAAO,IAAAC,cAAO,EAACf,cAAc,CAACT,QAAQ,CAAC,CAAC;EACjD;EACA,MAAM,IAAIc,oBAAW,CAACG,UAAU,EAAEjB,QAAQ,CAAC;AAC7C;AAEA,SAASoB,cAAcA,CAACpB,QAAgB,EAAE;EACxC,MAAMyB,GAAG,GAAG,MAAM;EAClB,MAAMC,YAAY,GAAG,CAAC,EACpBvE,OAAO,CAACwE,UAAU,CAAC,KAAK,CAAC,IACzBxE,OAAO,CAACwE,UAAU,CAAC,MAAM,CAAC,IAC1BxE,OAAO,CAACwE,UAAU,CAAC,MAAM,CAAC,CAC3B;EAED,IAAIC,OAAqC;EAEzC,IAAI,CAACF,YAAY,EAAE;IACjB,MAAMG,IAAkB,GAAG;MACzBC,OAAO,EAAE,KAAK;MACdC,UAAU,EAAE,KAAK;MACjBC,UAAU,EAAE,aAAa;MACzBC,UAAU,EAAE,QAAQ;MACpBC,cAAc,EAAEhB,MAAGA,CAAC,CAACiB,QAAQ,CAACnC,QAAQ,CAAC;MACvCoC,OAAO,EAAE,CACP,CACEC,WAAW,CAACrC,QAAQ,CAAC,EAAAsC,MAAA,CAAAC,MAAA;QAEnBC,qBAAqB,EAAE,IAAI;QAC3BC,kBAAkB,EAAE;MAAI,GAGpB;QAAEC,kBAAkB,EAAE;MAAK,CAAC,EAEnC;IAEL,CAAC;IAEDd,OAAO,GAAG,SAAAA,CAAUe,CAAC,EAAEC,QAAQ,EAAE;MAE/B,IAAIhB,OAAO,IAAIgB,QAAQ,CAACC,QAAQ,CAACpB,GAAG,CAAC,EAAE;QACrC,IAAI;UAEF,OAAOkB,CAAC,CAACG,QAAQ,CACf,IAAAC,gCAAiB,EAACH,QAAQ,EAAAN,MAAA,CAAAC,MAAA,KACrBV,IAAI;YACPe;UAAQ,EACT,CAAC,CAACtB,IAAI,EACPsB,QACF,CAAC;QACH,CAAC,CAAC,OAAOtE,KAAK,EAAE;UACd,IAAI,CAACoD,YAAY,EAAE;YAGjB,MAAMsB,WAAW,GAAG7F,OAAO,CAAC,uCAAuC,CAAC;YACpE,IAAIqC,QAAKA,CAAC,CAACyD,EAAE,CAACD,WAAW,CAACE,OAAO,EAAE,QAAQ,CAAC,EAAE;cAC5CC,OAAO,CAAC7E,KAAK,CACX,4FACF,CAAC;YACH;UACF;UACA,MAAMA,KAAK;QACb;MACF;MACA,OAAOnB,OAAO,CAACwE,UAAU,CAAC,KAAK,CAAC,CAACgB,CAAC,EAAEC,QAAQ,CAAC;IAC/C,CAAC;IACDzF,OAAO,CAACwE,UAAU,CAACF,GAAG,CAAC,GAAGG,OAAO;EACnC;EACA,IAAI;IACF,OAAO7B,cAAc,CAACC,QAAQ,CAAC;EACjC,CAAC,SAAS;IACR,IAAI,CAAC0B,YAAY,EAAE;MACjB,IAAIvE,OAAO,CAACwE,UAAU,CAACF,GAAG,CAAC,KAAKG,OAAO,EAAE,OAAOzE,OAAO,CAACwE,UAAU,CAACF,GAAG,CAAC;MACvEG,OAAO,GAAG3C,SAAS;IACrB;EACF;AACF;AAEA,SAASoD,WAAWA,CAACrC,QAAgB,EAAE;EACrC,IAAI;IAEF,OAAO7C,OAAO,CAAC,0BAA0B,CAAC;EAC5C,CAAC,CAAC,OAAOmB,KAAK,EAAE;IACd,IAAIA,KAAK,CAACgD,IAAI,KAAK,kBAAkB,EAAE,MAAMhD,KAAK;IAElD,IAAI8E,OAAO,GACT,yIAAyI;IAExG;MACjC,IAAI1D,OAAO,CAACC,QAAQ,CAAC0D,GAAG,EAAE;QAGxBD,OAAO,IAAI;AACnB;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;MACK;IACF;IAEA,MAAM,IAAItC,oBAAW,CAACsC,OAAO,EAAEpD,QAAQ,CAAC;EAC1C;AACF;AAAC", "ignoreList": []}