{"version": 3, "sources": ["../../src/index.ts"], "sourcesContent": ["/* istanbul ignore file */\n\n// Re-export core\nexport * from '@tanstack/query-core'\n\n// React Query\nexport * from './types'\nexport { useQueries } from './useQueries'\nexport type { QueriesResults, QueriesOptions } from './useQueries'\nexport { useQuery } from './useQuery'\nexport { useSuspenseQuery } from './useSuspenseQuery'\nexport { useSuspenseInfiniteQuery } from './useSuspenseInfiniteQuery'\nexport { useSuspenseQueries } from './useSuspenseQueries'\nexport type {\n  SuspenseQueriesResults,\n  SuspenseQueriesOptions,\n} from './useSuspenseQueries'\nexport { usePrefetchQuery } from './usePrefetchQuery'\nexport { usePrefetchInfiniteQuery } from './usePrefetchInfiniteQuery'\nexport { queryOptions } from './queryOptions'\nexport type {\n  DefinedInitialDataOptions,\n  UndefinedInitialDataOptions,\n  UnusedSkipTokenOptions,\n} from './queryOptions'\nexport { infiniteQueryOptions } from './infiniteQueryOptions'\nexport type {\n  DefinedInitialDataInfiniteOptions,\n  UndefinedInitialDataInfiniteOptions,\n  UnusedSkipTokenInfiniteOptions,\n} from './infiniteQueryOptions'\nexport {\n  QueryClientContext,\n  QueryClientProvider,\n  useQueryClient,\n} from './QueryClientProvider'\nexport type { QueryClientProviderProps } from './QueryClientProvider'\nexport type { QueryErrorResetBoundaryProps } from './QueryErrorResetBoundary'\nexport { HydrationBoundary } from './HydrationBoundary'\nexport type { HydrationBoundaryProps } from './HydrationBoundary'\nexport type {\n  QueryErrorClearResetFunction,\n  QueryErrorIsResetFunction,\n  QueryErrorResetBoundaryFunction,\n  QueryErrorResetFunction,\n} from './QueryErrorResetBoundary'\nexport {\n  QueryErrorResetBoundary,\n  useQueryErrorResetBoundary,\n} from './QueryErrorResetBoundary'\nexport { useIsFetching } from './useIsFetching'\nexport { useIsMutating, useMutationState } from './useMutationState'\nexport { useMutation } from './useMutation'\nexport { useInfiniteQuery } from './useInfiniteQuery'\nexport { useIsRestoring, IsRestoringProvider } from './isRestoring'\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAGA,wBAAc,iCAHd;AAMA,wBAAc,wBANd;AAOA,wBAA2B;AAE3B,sBAAyB;AACzB,8BAAiC;AACjC,sCAAyC;AACzC,gCAAmC;AAKnC,8BAAiC;AACjC,sCAAyC;AACzC,0BAA6B;AAM7B,kCAAqC;AAMrC,iCAIO;AAGP,+BAAkC;AAQlC,qCAGO;AACP,2BAA8B;AAC9B,8BAAgD;AAChD,yBAA4B;AAC5B,8BAAiC;AACjC,yBAAoD;", "names": []}