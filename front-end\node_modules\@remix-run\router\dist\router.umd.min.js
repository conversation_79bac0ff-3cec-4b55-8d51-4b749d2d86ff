/**
 * @remix-run/router v1.19.2
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */
!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define(["exports"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).RemixRouter={})}(this,(function(e){"use strict";function t(){return t=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var a in r)Object.prototype.hasOwnProperty.call(r,a)&&(e[a]=r[a])}return e},t.apply(this,arguments)}let r=function(e){return e.Pop="POP",e.Push="PUSH",e.Replace="REPLACE",e}({});const a="popstate";function n(e,t){if(!1===e||null==e)throw new Error(t)}function o(e,t){if(!e){"undefined"!=typeof console&&console.warn(t);try{throw new Error(t)}catch(e){}}}function i(e,t){return{usr:e.state,key:e.key,idx:t}}function s(e,r,a,n){return void 0===a&&(a=null),t({pathname:"string"==typeof e?e:e.pathname,search:"",hash:""},"string"==typeof r?u(r):r,{state:a,key:r&&r.key||n||Math.random().toString(36).substr(2,8)})}function l(e){let{pathname:t="/",search:r="",hash:a=""}=e;return r&&"?"!==r&&(t+="?"===r.charAt(0)?r:"?"+r),a&&"#"!==a&&(t+="#"===a.charAt(0)?a:"#"+a),t}function u(e){let t={};if(e){let r=e.indexOf("#");r>=0&&(t.hash=e.substr(r),e=e.substr(0,r));let a=e.indexOf("?");a>=0&&(t.search=e.substr(a),e=e.substr(0,a)),e&&(t.pathname=e)}return t}function c(e,o,u,c){void 0===c&&(c={});let{window:d=document.defaultView,v5Compat:h=!1}=c,f=d.history,p=r.Pop,m=null,y=v();function v(){return(f.state||{idx:null}).idx}function g(){p=r.Pop;let e=v(),t=null==e?null:e-y;y=e,m&&m({action:p,location:w.location,delta:t})}function b(e){let t="null"!==d.location.origin?d.location.origin:d.location.href,r="string"==typeof e?e:l(e);return r=r.replace(/ $/,"%20"),n(t,"No window.location.(origin|href) available to create URL for href: "+r),new URL(r,t)}null==y&&(y=0,f.replaceState(t({},f.state,{idx:y}),""));let w={get action(){return p},get location(){return e(d,f)},listen(e){if(m)throw new Error("A history only accepts one active listener");return d.addEventListener(a,g),m=e,()=>{d.removeEventListener(a,g),m=null}},createHref:e=>o(d,e),createURL:b,encodeLocation(e){let t=b(e);return{pathname:t.pathname,search:t.search,hash:t.hash}},push:function(e,t){p=r.Push;let a=s(w.location,e,t);u&&u(a,e),y=v()+1;let n=i(a,y),o=w.createHref(a);try{f.pushState(n,"",o)}catch(e){if(e instanceof DOMException&&"DataCloneError"===e.name)throw e;d.location.assign(o)}h&&m&&m({action:p,location:w.location,delta:1})},replace:function(e,t){p=r.Replace;let a=s(w.location,e,t);u&&u(a,e),y=v();let n=i(a,y),o=w.createHref(a);f.replaceState(n,"",o),h&&m&&m({action:p,location:w.location,delta:0})},go:e=>f.go(e)};return w}let d=function(e){return e.data="data",e.deferred="deferred",e.redirect="redirect",e.error="error",e}({});const h=new Set(["lazy","caseSensitive","path","id","index","children"]);function f(e,r,a,o){return void 0===a&&(a=[]),void 0===o&&(o={}),e.map(((e,i)=>{let s=[...a,String(i)],l="string"==typeof e.id?e.id:s.join("-");if(n(!0!==e.index||!e.children,"Cannot specify children on an index route"),n(!o[l],'Found a route id collision on id "'+l+"\".  Route id's must be globally unique within Data Router usages"),function(e){return!0===e.index}(e)){let a=t({},e,r(e),{id:l});return o[l]=a,a}{let a=t({},e,r(e),{id:l,children:void 0});return o[l]=a,e.children&&(a.children=f(e.children,r,s,o)),a}}))}function p(e,t,r){return void 0===r&&(r="/"),m(e,t,r,!1)}function m(e,t,r,a){let n=P(("string"==typeof t?u(t):t).pathname||"/",r);if(null==n)return null;let o=v(e);!function(e){e.sort(((e,t)=>e.score!==t.score?t.score-e.score:function(e,t){return e.length===t.length&&e.slice(0,-1).every(((e,r)=>e===t[r]))?e[e.length-1]-t[t.length-1]:0}(e.routesMeta.map((e=>e.childrenIndex)),t.routesMeta.map((e=>e.childrenIndex)))))}(o);let i=null;for(let e=0;null==i&&e<o.length;++e){let t=E(n);i=S(o[e],t,a)}return i}function y(e,t){let{route:r,pathname:a,params:n}=e;return{id:r.id,pathname:a,params:n,data:t[r.id],handle:r.handle}}function v(e,t,r,a){void 0===t&&(t=[]),void 0===r&&(r=[]),void 0===a&&(a="");let o=(e,o,i)=>{let s={relativePath:void 0===i?e.path||"":i,caseSensitive:!0===e.caseSensitive,childrenIndex:o,route:e};s.relativePath.startsWith("/")&&(n(s.relativePath.startsWith(a),'Absolute route path "'+s.relativePath+'" nested under path "'+a+'" is not valid. An absolute child route path must start with the combined path of all its parent routes.'),s.relativePath=s.relativePath.slice(a.length));let l=k([a,s.relativePath]),u=r.concat(s);e.children&&e.children.length>0&&(n(!0!==e.index,'Index routes must not have child routes. Please remove all child routes from route path "'+l+'".'),v(e.children,t,u,l)),(null!=e.path||e.index)&&t.push({path:l,score:D(l,e.index),routesMeta:u})};return e.forEach(((e,t)=>{var r;if(""!==e.path&&null!=(r=e.path)&&r.includes("?"))for(let r of g(e.path))o(e,t,r);else o(e,t)})),t}function g(e){let t=e.split("/");if(0===t.length)return[];let[r,...a]=t,n=r.endsWith("?"),o=r.replace(/\?$/,"");if(0===a.length)return n?[o,""]:[o];let i=g(a.join("/")),s=[];return s.push(...i.map((e=>""===e?o:[o,e].join("/")))),n&&s.push(...i),s.map((t=>e.startsWith("/")&&""===t?"/":t))}const b=/^:[\w-]+$/,w=e=>"*"===e;function D(e,t){let r=e.split("/"),a=r.length;return r.some(w)&&(a+=-2),t&&(a+=2),r.filter((e=>!w(e))).reduce(((e,t)=>e+(b.test(t)?3:""===t?1:10)),a)}function S(e,t,r){void 0===r&&(r=!1);let{routesMeta:a}=e,n={},o="/",i=[];for(let e=0;e<a.length;++e){let s=a[e],l=e===a.length-1,u="/"===o?t:t.slice(o.length)||"/",c=R({path:s.relativePath,caseSensitive:s.caseSensitive,end:l},u),d=s.route;if(!c&&l&&r&&!a[a.length-1].route.index&&(c=R({path:s.relativePath,caseSensitive:s.caseSensitive,end:!1},u)),!c)return null;Object.assign(n,c.params),i.push({params:n,pathname:k([o,c.pathname]),pathnameBase:_(k([o,c.pathnameBase])),route:d}),"/"!==c.pathnameBase&&(o=k([o,c.pathnameBase]))}return i}function R(e,t){"string"==typeof e&&(e={path:e,caseSensitive:!1,end:!0});let[r,a]=function(e,t,r){void 0===t&&(t=!1);void 0===r&&(r=!0);o("*"===e||!e.endsWith("*")||e.endsWith("/*"),'Route path "'+e+'" will be treated as if it were "'+e.replace(/\*$/,"/*")+'" because the `*` character must always follow a `/` in the pattern. To get rid of this warning, please change the route path to "'+e.replace(/\*$/,"/*")+'".');let a=[],n="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,((e,t,r)=>(a.push({paramName:t,isOptional:null!=r}),r?"/?([^\\/]+)?":"/([^\\/]+)")));e.endsWith("*")?(a.push({paramName:"*"}),n+="*"===e||"/*"===e?"(.*)$":"(?:\\/(.+)|\\/*)$"):r?n+="\\/*$":""!==e&&"/"!==e&&(n+="(?:(?=\\/|$))");return[new RegExp(n,t?void 0:"i"),a]}(e.path,e.caseSensitive,e.end),n=t.match(r);if(!n)return null;let i=n[0],s=i.replace(/(.)\/+$/,"$1"),l=n.slice(1);return{params:a.reduce(((e,t,r)=>{let{paramName:a,isOptional:n}=t;if("*"===a){let e=l[r]||"";s=i.slice(0,i.length-e.length).replace(/(.)\/+$/,"$1")}const o=l[r];return e[a]=n&&!o?void 0:(o||"").replace(/%2F/g,"/"),e}),{}),pathname:i,pathnameBase:s,pattern:e}}function E(e){try{return e.split("/").map((e=>decodeURIComponent(e).replace(/\//g,"%2F"))).join("/")}catch(t){return o(!1,'The URL path "'+e+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent encoding ('+t+")."),e}}function P(e,t){if("/"===t)return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let r=t.endsWith("/")?t.length-1:t.length,a=e.charAt(r);return a&&"/"!==a?null:e.slice(r)||"/"}function x(e,t){void 0===t&&(t="/");let{pathname:r,search:a="",hash:n=""}="string"==typeof e?u(e):e,o=r?r.startsWith("/")?r:function(e,t){let r=t.replace(/\/+$/,"").split("/");return e.split("/").forEach((e=>{".."===e?r.length>1&&r.pop():"."!==e&&r.push(e)})),r.length>1?r.join("/"):"/"}(r,t):t;return{pathname:o,search:C(a),hash:T(n)}}function A(e,t,r,a){return"Cannot include a '"+e+"' character in a manually specified `to."+t+"` field ["+JSON.stringify(a)+"].  Please separate it out to the `to."+r+'` field. Alternatively you may provide the full path as a string in <Link to="..."> and the router will parse it for you.'}function L(e){return e.filter(((e,t)=>0===t||e.route.path&&e.route.path.length>0))}function M(e,t){let r=L(e);return t?r.map(((e,t)=>t===r.length-1?e.pathname:e.pathnameBase)):r.map((e=>e.pathnameBase))}function j(e,r,a,o){let i;void 0===o&&(o=!1),"string"==typeof e?i=u(e):(i=t({},e),n(!i.pathname||!i.pathname.includes("?"),A("?","pathname","search",i)),n(!i.pathname||!i.pathname.includes("#"),A("#","pathname","hash",i)),n(!i.search||!i.search.includes("#"),A("#","search","hash",i)));let s,l=""===e||""===i.pathname,c=l?"/":i.pathname;if(null==c)s=a;else{let e=r.length-1;if(!o&&c.startsWith("..")){let t=c.split("/");for(;".."===t[0];)t.shift(),e-=1;i.pathname=t.join("/")}s=e>=0?r[e]:"/"}let d=x(i,s),h=c&&"/"!==c&&c.endsWith("/"),f=(l||"."===c)&&a.endsWith("/");return d.pathname.endsWith("/")||!h&&!f||(d.pathname+="/"),d}const k=e=>e.join("/").replace(/\/\/+/g,"/"),_=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),C=e=>e&&"?"!==e?e.startsWith("?")?e:"?"+e:"",T=e=>e&&"#"!==e?e.startsWith("#")?e:"#"+e:"";class O{constructor(e,t){this.type="DataWithResponseInit",this.data=e,this.init=t||null}}class U extends Error{}class I{constructor(e,t){let r;this.pendingKeysSet=new Set,this.subscribers=new Set,this.deferredKeys=[],n(e&&"object"==typeof e&&!Array.isArray(e),"defer() only accepts plain objects"),this.abortPromise=new Promise(((e,t)=>r=t)),this.controller=new AbortController;let a=()=>r(new U("Deferred data aborted"));this.unlistenAbortSignal=()=>this.controller.signal.removeEventListener("abort",a),this.controller.signal.addEventListener("abort",a),this.data=Object.entries(e).reduce(((e,t)=>{let[r,a]=t;return Object.assign(e,{[r]:this.trackPromise(r,a)})}),{}),this.done&&this.unlistenAbortSignal(),this.init=t}trackPromise(e,t){if(!(t instanceof Promise))return t;this.deferredKeys.push(e),this.pendingKeysSet.add(e);let r=Promise.race([t,this.abortPromise]).then((t=>this.onSettle(r,e,void 0,t)),(t=>this.onSettle(r,e,t)));return r.catch((()=>{})),Object.defineProperty(r,"_tracked",{get:()=>!0}),r}onSettle(e,t,r,a){if(this.controller.signal.aborted&&r instanceof U)return this.unlistenAbortSignal(),Object.defineProperty(e,"_error",{get:()=>r}),Promise.reject(r);if(this.pendingKeysSet.delete(t),this.done&&this.unlistenAbortSignal(),void 0===r&&void 0===a){let r=new Error('Deferred data for key "'+t+'" resolved/rejected with `undefined`, you must resolve/reject with a value or `null`.');return Object.defineProperty(e,"_error",{get:()=>r}),this.emit(!1,t),Promise.reject(r)}return void 0===a?(Object.defineProperty(e,"_error",{get:()=>r}),this.emit(!1,t),Promise.reject(r)):(Object.defineProperty(e,"_data",{get:()=>a}),this.emit(!1,t),a)}emit(e,t){this.subscribers.forEach((r=>r(e,t)))}subscribe(e){return this.subscribers.add(e),()=>this.subscribers.delete(e)}cancel(){this.controller.abort(),this.pendingKeysSet.forEach(((e,t)=>this.pendingKeysSet.delete(t))),this.emit(!0)}async resolveData(e){let t=!1;if(!this.done){let r=()=>this.cancel();e.addEventListener("abort",r),t=await new Promise((t=>{this.subscribe((a=>{e.removeEventListener("abort",r),(a||this.done)&&t(a)}))}))}return t}get done(){return 0===this.pendingKeysSet.size}get unwrappedData(){return n(null!==this.data&&this.done,"Can only unwrap data on initialized and settled deferreds"),Object.entries(this.data).reduce(((e,t)=>{let[r,a]=t;return Object.assign(e,{[r]:H(a)})}),{})}get pendingKeys(){return Array.from(this.pendingKeysSet)}}function H(e){if(!function(e){return e instanceof Promise&&!0===e._tracked}(e))return e;if(e._error)throw e._error;return e._data}const z=function(e,r){void 0===r&&(r=302);let a=r;"number"==typeof a?a={status:a}:void 0===a.status&&(a.status=302);let n=new Headers(a.headers);return n.set("Location",e),new Response(null,t({},a,{headers:n}))};class F{constructor(e,t,r,a){void 0===a&&(a=!1),this.status=e,this.statusText=t||"",this.internal=a,r instanceof Error?(this.data=r.toString(),this.error=r):this.data=r}}function N(e){return null!=e&&"number"==typeof e.status&&"string"==typeof e.statusText&&"boolean"==typeof e.internal&&"data"in e}const B=["post","put","patch","delete"],W=new Set(B),$=["get",...B],q=new Set($),K=new Set([301,302,303,307,308]),Y=new Set([307,308]),J={state:"idle",location:void 0,formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0},V={state:"idle",data:void 0,formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0},X={state:"unblocked",proceed:void 0,reset:void 0,location:void 0},G=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,Q=e=>({hasErrorBoundary:Boolean(e.hasErrorBoundary)}),Z="remix-router-transitions";const ee=Symbol("deferred");function te(e,t,r){if(r.v7_throwAbortReason&&void 0!==e.signal.reason)throw e.signal.reason;throw new Error((t?"queryRoute":"query")+"() call aborted: "+e.method+" "+e.url)}function re(e,t,r,a,n,o,i,s){let u,c;if(i){u=[];for(let e of t)if(u.push(e),e.route.id===i){c=e;break}}else u=t,c=t[t.length-1];let d=j(n||".",M(u,o),P(e.pathname,r)||e.pathname,"path"===s);return null==n&&(d.search=e.search,d.hash=e.hash),null!=n&&""!==n&&"."!==n||!c||!c.route.index||ze(d.search)||(d.search=d.search?d.search.replace(/^\?/,"?index&"):"?index"),a&&"/"!==r&&(d.pathname="/"===d.pathname?r:k([r,d.pathname])),l(d)}function ae(e,t,r,a){if(!a||!function(e){return null!=e&&("formData"in e&&null!=e.formData||"body"in e&&void 0!==e.body)}(a))return{path:r};if(a.formMethod&&!Te(a.formMethod))return{path:r,error:Ee(405,{method:a.formMethod})};let o,i,s=()=>({path:r,error:Ee(400,{type:"invalid-body"})}),c=a.formMethod||"get",d=e?c.toUpperCase():c.toLowerCase(),h=xe(r);if(void 0!==a.body){if("text/plain"===a.formEncType){if(!Oe(d))return s();let e="string"==typeof a.body?a.body:a.body instanceof FormData||a.body instanceof URLSearchParams?Array.from(a.body.entries()).reduce(((e,t)=>{let[r,a]=t;return""+e+r+"="+a+"\n"}),""):String(a.body);return{path:r,submission:{formMethod:d,formAction:h,formEncType:a.formEncType,formData:void 0,json:void 0,text:e}}}if("application/json"===a.formEncType){if(!Oe(d))return s();try{let e="string"==typeof a.body?JSON.parse(a.body):a.body;return{path:r,submission:{formMethod:d,formAction:h,formEncType:a.formEncType,formData:void 0,json:e,text:void 0}}}catch(e){return s()}}}if(n("function"==typeof FormData,"FormData is not available in this environment"),a.formData)o=ye(a.formData),i=a.formData;else if(a.body instanceof FormData)o=ye(a.body),i=a.body;else if(a.body instanceof URLSearchParams)o=a.body,i=ve(o);else if(null==a.body)o=new URLSearchParams,i=new FormData;else try{o=new URLSearchParams(a.body),i=ve(o)}catch(e){return s()}let f={formMethod:d,formAction:h,formEncType:a&&a.formEncType||"application/x-www-form-urlencoded",formData:i,json:void 0,text:void 0};if(Oe(f.formMethod))return{path:r,submission:f};let p=u(r);return t&&p.search&&ze(p.search)&&o.append("index",""),p.search="?"+o,{path:l(p),submission:f}}function ne(e,t){let r=e;if(t){let a=e.findIndex((e=>e.route.id===t));a>=0&&(r=e.slice(0,a))}return r}function oe(e,r,a,n,o,i,s,l,u,c,d,h,f,m,y,v){let g=v?Me(v[1])?v[1].error:v[1].data:void 0,b=e.createURL(r.location),w=e.createURL(o),D=v&&Me(v[1])?v[0]:void 0,S=D?ne(a,D):a,R=v?v[1].statusCode:void 0,E=s&&R&&R>=400,P=S.filter(((e,a)=>{let{route:o}=e;if(o.lazy)return!0;if(null==o.loader)return!1;if(i)return!("function"==typeof o.loader&&!o.loader.hydrate)||void 0===r.loaderData[o.id]&&(!r.errors||void 0===r.errors[o.id]);if(function(e,t,r){let a=!t||r.route.id!==t.route.id,n=void 0===e[r.route.id];return a||n}(r.loaderData,r.matches[a],e)||u.some((t=>t===e.route.id)))return!0;let s=r.matches[a],c=e;return se(e,t({currentUrl:b,currentParams:s.params,nextUrl:w,nextParams:c.params},n,{actionResult:g,actionStatus:R,defaultShouldRevalidate:!E&&(l||b.pathname+b.search===w.pathname+w.search||b.search!==w.search||ie(s,c))}))})),x=[];return h.forEach(((e,o)=>{if(i||!a.some((t=>t.route.id===e.routeId))||d.has(o))return;let s=p(m,e.path,y);if(!s)return void x.push({key:o,routeId:e.routeId,path:e.path,matches:null,match:null,controller:null});let u=r.fetchers.get(o),h=Fe(s,e.path),v=!1;f.has(o)?v=!1:c.has(o)?(c.delete(o),v=!0):v=u&&"idle"!==u.state&&void 0===u.data?l:se(h,t({currentUrl:b,currentParams:r.matches[r.matches.length-1].params,nextUrl:w,nextParams:a[a.length-1].params},n,{actionResult:g,actionStatus:R,defaultShouldRevalidate:!E&&l})),v&&x.push({key:o,routeId:e.routeId,path:e.path,matches:s,match:h,controller:new AbortController})})),[P,x]}function ie(e,t){let r=e.route.path;return e.pathname!==t.pathname||null!=r&&r.endsWith("*")&&e.params["*"]!==t.params["*"]}function se(e,t){if(e.route.shouldRevalidate){let r=e.route.shouldRevalidate(t);if("boolean"==typeof r)return r}return t.defaultShouldRevalidate}async function le(e,t,r,a,n,o,i,s){let l=[t,...r.map((e=>e.route.id))].join("-");try{let c=i.get(l);c||(c=e({path:t,matches:r,patch:(e,t)=>{s.aborted||ue(e,t,a,n,o)}}),i.set(l,c)),c&&("object"==typeof(u=c)&&null!=u&&"then"in u)&&await c}finally{i.delete(l)}var u}function ue(e,t,r,a,o){if(e){var i;let r=a[e];n(r,"No route found to patch children into: routeId = "+e);let s=f(t,o,[e,"patch",String((null==(i=r.children)?void 0:i.length)||"0")],a);r.children?r.children.push(...s):r.children=s}else{let e=f(t,o,["patch",String(r.length||"0")],a);r.push(...e)}}async function ce(e){let{matches:t}=e,r=t.filter((e=>e.shouldLoad));return(await Promise.all(r.map((e=>e.resolve())))).reduce(((e,t,a)=>Object.assign(e,{[r[a].route.id]:t})),{})}async function de(e,r,a,i,s,l,u,c,f,p){let m=l.map((e=>e.route.lazy?async function(e,r,a){if(!e.lazy)return;let i=await e.lazy();if(!e.lazy)return;let s=a[e.id];n(s,"No route found in manifest");let l={};for(let e in i){let t=void 0!==s[e]&&"hasErrorBoundary"!==e;o(!t,'Route "'+s.id+'" has a static property "'+e+'" defined but its lazy function is also returning a value for this property. The lazy route property "'+e+'" will be ignored.'),t||h.has(e)||(l[e]=i[e])}Object.assign(s,l),Object.assign(s,t({},r(s),{lazy:void 0}))}(e.route,f,c):void 0)),y=l.map(((e,a)=>{let o=m[a],l=s.some((t=>t.route.id===e.route.id));return t({},e,{shouldLoad:l,resolve:async t=>(t&&"GET"===i.method&&(e.route.lazy||e.route.loader)&&(l=!0),l?async function(e,t,r,a,o,i){let s,l,u=a=>{let n,s=new Promise(((e,t)=>n=t));l=()=>n(),t.signal.addEventListener("abort",l);let u=n=>"function"!=typeof a?Promise.reject(new Error('You cannot call the handler for a route which defines a boolean "'+e+'" [routeId: '+r.route.id+"]")):a({request:t,params:r.params,context:i},...void 0!==n?[n]:[]),c=(async()=>{try{return{type:"data",result:await(o?o((e=>u(e))):u())}}catch(e){return{type:"error",result:e}}})();return Promise.race([c,s])};try{let o=r.route[e];if(a)if(o){let e,[t]=await Promise.all([u(o).catch((t=>{e=t})),a]);if(void 0!==e)throw e;s=t}else{if(await a,o=r.route[e],!o){if("action"===e){let e=new URL(t.url),a=e.pathname+e.search;throw Ee(405,{method:t.method,pathname:a,routeId:r.route.id})}return{type:d.data,result:void 0}}s=await u(o)}else{if(!o){let e=new URL(t.url);throw Ee(404,{pathname:e.pathname+e.search})}s=await u(o)}n(void 0!==s.result,"You defined "+("action"===e?"an action":"a loader")+' for route "'+r.route.id+"\" but didn't return anything from your `"+e+"` function. Please return a value or `null`.")}catch(e){return{type:d.error,result:e}}finally{l&&t.signal.removeEventListener("abort",l)}return s}(r,i,e,o,t,p):Promise.resolve({type:d.data,result:void 0}))})})),v=await e({matches:y,request:i,params:l[0].params,fetcherKey:u,context:p});try{await Promise.all(m)}catch(e){}return v}async function he(e){let{result:t,type:r}=e;if(Ce(t)){let e;try{let r=t.headers.get("Content-Type");e=r&&/\bapplication\/json\b/.test(r)?null==t.body?null:await t.json():await t.text()}catch(e){return{type:d.error,error:e}}return r===d.error?{type:d.error,error:new F(t.status,t.statusText,e),statusCode:t.status,headers:t.headers}:{type:d.data,data:e,statusCode:t.status,headers:t.headers}}if(r===d.error){if(ke(t)){var a,n;if(t.data instanceof Error)return{type:d.error,error:t.data,statusCode:null==(n=t.init)?void 0:n.status};t=new F((null==(a=t.init)?void 0:a.status)||500,void 0,t.data)}return{type:d.error,error:t,statusCode:N(t)?t.status:void 0}}var o,i,s,l;return _e(t)?{type:d.deferred,deferredData:t,statusCode:null==(o=t.init)?void 0:o.status,headers:(null==(i=t.init)?void 0:i.headers)&&new Headers(t.init.headers)}:ke(t)?{type:d.data,data:t.data,statusCode:null==(s=t.init)?void 0:s.status,headers:null!=(l=t.init)&&l.headers?new Headers(t.init.headers):void 0}:{type:d.data,data:t}}function fe(e,t,r,a,o,i){let s=e.headers.get("Location");if(n(s,"Redirects returned/thrown from loaders/actions must have a Location header"),!G.test(s)){let n=a.slice(0,a.findIndex((e=>e.route.id===r))+1);s=re(new URL(t.url),n,o,!0,s,i),e.headers.set("Location",s)}return e}function pe(e,t,r){if(G.test(e)){let a=e,n=a.startsWith("//")?new URL(t.protocol+a):new URL(a),o=null!=P(n.pathname,r);if(n.origin===t.origin&&o)return n.pathname+n.search+n.hash}return e}function me(e,t,r,a){let n=e.createURL(xe(t)).toString(),o={signal:r};if(a&&Oe(a.formMethod)){let{formMethod:e,formEncType:t}=a;o.method=e.toUpperCase(),"application/json"===t?(o.headers=new Headers({"Content-Type":t}),o.body=JSON.stringify(a.json)):"text/plain"===t?o.body=a.text:"application/x-www-form-urlencoded"===t&&a.formData?o.body=ye(a.formData):o.body=a.formData}return new Request(n,o)}function ye(e){let t=new URLSearchParams;for(let[r,a]of e.entries())t.append(r,"string"==typeof a?a:a.name);return t}function ve(e){let t=new FormData;for(let[r,a]of e.entries())t.append(r,a);return t}function ge(e,t,r,a,o){let i,s={},l=null,u=!1,c={},d=r&&Me(r[1])?r[1].error:void 0;return e.forEach((r=>{if(!(r.route.id in t))return;let h=r.route.id,f=t[h];if(n(!je(f),"Cannot handle redirect results in processLoaderData"),Me(f)){let t=f.error;if(void 0!==d&&(t=d,d=void 0),l=l||{},o)l[h]=t;else{let r=Se(e,h);null==l[r.route.id]&&(l[r.route.id]=t)}s[h]=void 0,u||(u=!0,i=N(f.error)?f.error.status:500),f.headers&&(c[h]=f.headers)}else Le(f)?(a.set(h,f.deferredData),s[h]=f.deferredData.data,null==f.statusCode||200===f.statusCode||u||(i=f.statusCode),f.headers&&(c[h]=f.headers)):(s[h]=f.data,f.statusCode&&200!==f.statusCode&&!u&&(i=f.statusCode),f.headers&&(c[h]=f.headers))})),void 0!==d&&r&&(l={[r[0]]:d},s[r[0]]=void 0),{loaderData:s,errors:l,statusCode:i||200,loaderHeaders:c}}function be(e,r,a,o,i,s,l,u){let{loaderData:c,errors:d}=ge(r,o,i,u,!1);return s.forEach((r=>{let{key:a,match:o,controller:i}=r,s=l[a];if(n(s,"Did not find corresponding fetcher result"),!i||!i.signal.aborted)if(Me(s)){let r=Se(e.matches,null==o?void 0:o.route.id);d&&d[r.route.id]||(d=t({},d,{[r.route.id]:s.error})),e.fetchers.delete(a)}else if(je(s))n(!1,"Unhandled fetcher revalidation redirect");else if(Le(s))n(!1,"Unhandled fetcher deferred data");else{let t=qe(s.data);e.fetchers.set(a,t)}})),{loaderData:c,errors:d}}function we(e,r,a,n){let o=t({},r);for(let t of a){let a=t.route.id;if(r.hasOwnProperty(a)?void 0!==r[a]&&(o[a]=r[a]):void 0!==e[a]&&t.route.loader&&(o[a]=e[a]),n&&n.hasOwnProperty(a))break}return o}function De(e){return e?Me(e[1])?{actionData:{}}:{actionData:{[e[0]]:e[1].data}}:{}}function Se(e,t){return(t?e.slice(0,e.findIndex((e=>e.route.id===t))+1):[...e]).reverse().find((e=>!0===e.route.hasErrorBoundary))||e[0]}function Re(e){let t=1===e.length?e[0]:e.find((e=>e.index||!e.path||"/"===e.path))||{id:"__shim-error-route__"};return{matches:[{params:{},pathname:"",pathnameBase:"",route:t}],route:t}}function Ee(e,t){let{pathname:r,routeId:a,method:n,type:o,message:i}=void 0===t?{}:t,s="Unknown Server Error",l="Unknown @remix-run/router error";return 400===e?(s="Bad Request","route-discovery"===o?l='Unable to match URL "'+r+'" - the `unstable_patchRoutesOnNavigation()` function threw the following error:\n'+i:n&&r&&a?l="You made a "+n+' request to "'+r+'" but did not provide a `loader` for route "'+a+'", so there is no way to handle the request.':"defer-action"===o?l="defer() is not supported in actions":"invalid-body"===o&&(l="Unable to encode submission body")):403===e?(s="Forbidden",l='Route "'+a+'" does not match URL "'+r+'"'):404===e?(s="Not Found",l='No route matches URL "'+r+'"'):405===e&&(s="Method Not Allowed",n&&r&&a?l="You made a "+n.toUpperCase()+' request to "'+r+'" but did not provide an `action` for route "'+a+'", so there is no way to handle the request.':n&&(l='Invalid request method "'+n.toUpperCase()+'"')),new F(e||500,s,new Error(l),!0)}function Pe(e){let t=Object.entries(e);for(let e=t.length-1;e>=0;e--){let[r,a]=t[e];if(je(a))return{key:r,result:a}}}function xe(e){return l(t({},"string"==typeof e?u(e):e,{hash:""}))}function Ae(e){return Ce(e.result)&&K.has(e.result.status)}function Le(e){return e.type===d.deferred}function Me(e){return e.type===d.error}function je(e){return(e&&e.type)===d.redirect}function ke(e){return"object"==typeof e&&null!=e&&"type"in e&&"data"in e&&"init"in e&&"DataWithResponseInit"===e.type}function _e(e){let t=e;return t&&"object"==typeof t&&"object"==typeof t.data&&"function"==typeof t.subscribe&&"function"==typeof t.cancel&&"function"==typeof t.resolveData}function Ce(e){return null!=e&&"number"==typeof e.status&&"string"==typeof e.statusText&&"object"==typeof e.headers&&void 0!==e.body}function Te(e){return q.has(e.toLowerCase())}function Oe(e){return W.has(e.toLowerCase())}async function Ue(e,t,r,a,n){let o=Object.entries(t);for(let i=0;i<o.length;i++){let[s,l]=o[i],u=e.find((e=>(null==e?void 0:e.route.id)===s));if(!u)continue;let c=a.find((e=>e.route.id===u.route.id)),d=null!=c&&!ie(c,u)&&void 0!==(n&&n[u.route.id]);Le(l)&&d&&await He(l,r,!1).then((e=>{e&&(t[s]=e)}))}}async function Ie(e,t,r){for(let a=0;a<r.length;a++){let{key:o,routeId:i,controller:s}=r[a],l=t[o];e.find((e=>(null==e?void 0:e.route.id)===i))&&(Le(l)&&(n(s,"Expected an AbortController for revalidating fetcher deferred result"),await He(l,s.signal,!0).then((e=>{e&&(t[o]=e)}))))}}async function He(e,t,r){if(void 0===r&&(r=!1),!await e.deferredData.resolveData(t)){if(r)try{return{type:d.data,data:e.deferredData.unwrappedData}}catch(e){return{type:d.error,error:e}}return{type:d.data,data:e.deferredData.data}}}function ze(e){return new URLSearchParams(e).getAll("index").some((e=>""===e))}function Fe(e,t){let r="string"==typeof t?u(t).search:t.search;if(e[e.length-1].route.index&&ze(r||""))return e[e.length-1];let a=L(e);return a[a.length-1]}function Ne(e){let{formMethod:t,formAction:r,formEncType:a,text:n,formData:o,json:i}=e;if(t&&r&&a)return null!=n?{formMethod:t,formAction:r,formEncType:a,formData:void 0,json:void 0,text:n}:null!=o?{formMethod:t,formAction:r,formEncType:a,formData:o,json:void 0,text:void 0}:void 0!==i?{formMethod:t,formAction:r,formEncType:a,formData:void 0,json:i,text:void 0}:void 0}function Be(e,t){if(t){return{state:"loading",location:e,formMethod:t.formMethod,formAction:t.formAction,formEncType:t.formEncType,formData:t.formData,json:t.json,text:t.text}}return{state:"loading",location:e,formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0}}function We(e,t){return{state:"submitting",location:e,formMethod:t.formMethod,formAction:t.formAction,formEncType:t.formEncType,formData:t.formData,json:t.json,text:t.text}}function $e(e,t){if(e){return{state:"loading",formMethod:e.formMethod,formAction:e.formAction,formEncType:e.formEncType,formData:e.formData,json:e.json,text:e.text,data:t}}return{state:"loading",formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0,data:t}}function qe(e){return{state:"idle",formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0,data:e}}e.AbortedDeferredError=U,e.Action=r,e.IDLE_BLOCKER=X,e.IDLE_FETCHER=V,e.IDLE_NAVIGATION=J,e.UNSAFE_DEFERRED_SYMBOL=ee,e.UNSAFE_DeferredData=I,e.UNSAFE_ErrorResponseImpl=F,e.UNSAFE_convertRouteMatchToUiMatch=y,e.UNSAFE_convertRoutesToDataRoutes=f,e.UNSAFE_decodePath=E,e.UNSAFE_getResolveToMatches=M,e.UNSAFE_invariant=n,e.UNSAFE_warning=o,e.createBrowserHistory=function(e){return void 0===e&&(e={}),c((function(e,t){let{pathname:r,search:a,hash:n}=e.location;return s("",{pathname:r,search:a,hash:n},t.state&&t.state.usr||null,t.state&&t.state.key||"default")}),(function(e,t){return"string"==typeof t?t:l(t)}),null,e)},e.createHashHistory=function(e){return void 0===e&&(e={}),c((function(e,t){let{pathname:r="/",search:a="",hash:n=""}=u(e.location.hash.substr(1));return r.startsWith("/")||r.startsWith(".")||(r="/"+r),s("",{pathname:r,search:a,hash:n},t.state&&t.state.usr||null,t.state&&t.state.key||"default")}),(function(e,t){let r=e.document.querySelector("base"),a="";if(r&&r.getAttribute("href")){let t=e.location.href,r=t.indexOf("#");a=-1===r?t:t.slice(0,r)}return a+"#"+("string"==typeof t?t:l(t))}),(function(e,t){o("/"===e.pathname.charAt(0),"relative pathnames are not supported in hash history.push("+JSON.stringify(t)+")")}),e)},e.createMemoryHistory=function(e){void 0===e&&(e={});let t,{initialEntries:a=["/"],initialIndex:n,v5Compat:i=!1}=e;t=a.map(((e,t)=>m(e,"string"==typeof e?null:e.state,0===t?"default":void 0)));let c=f(null==n?t.length-1:n),d=r.Pop,h=null;function f(e){return Math.min(Math.max(e,0),t.length-1)}function p(){return t[c]}function m(e,r,a){void 0===r&&(r=null);let n=s(t?p().pathname:"/",e,r,a);return o("/"===n.pathname.charAt(0),"relative pathnames are not supported in memory history: "+JSON.stringify(e)),n}function y(e){return"string"==typeof e?e:l(e)}return{get index(){return c},get action(){return d},get location(){return p()},createHref:y,createURL:e=>new URL(y(e),"http://localhost"),encodeLocation(e){let t="string"==typeof e?u(e):e;return{pathname:t.pathname||"",search:t.search||"",hash:t.hash||""}},push(e,a){d=r.Push;let n=m(e,a);c+=1,t.splice(c,t.length,n),i&&h&&h({action:d,location:n,delta:1})},replace(e,a){d=r.Replace;let n=m(e,a);t[c]=n,i&&h&&h({action:d,location:n,delta:0})},go(e){d=r.Pop;let a=f(c+e),n=t[a];c=a,h&&h({action:d,location:n,delta:e})},listen:e=>(h=e,()=>{h=null})}},e.createPath=l,e.createRouter=function(e){const a=e.window?e.window:"undefined"!=typeof window?window:void 0,i=void 0!==a&&void 0!==a.document&&void 0!==a.document.createElement,l=!i;let u;if(n(e.routes.length>0,"You must provide a non-empty routes array to createRouter"),e.mapRouteProperties)u=e.mapRouteProperties;else if(e.detectErrorBoundary){let t=e.detectErrorBoundary;u=e=>({hasErrorBoundary:t(e)})}else u=Q;let c,h,v,g={},b=f(e.routes,u,void 0,g),w=e.basename||"/",D=e.unstable_dataStrategy||ce,S=e.unstable_patchRoutesOnNavigation,R=t({v7_fetcherPersist:!1,v7_normalizeFormMethod:!1,v7_partialHydration:!1,v7_prependBasename:!1,v7_relativeSplatPath:!1,v7_skipActionErrorRevalidation:!1},e.future),E=null,x=new Set,A=new Set,L=null,M=null,j=null,k=null!=e.hydrationData,_=p(b,e.history.location,w),C=null;if(null==_&&!S){let t=Ee(404,{pathname:e.history.location.pathname}),{matches:r,route:a}=Re(b);_=r,C={[a.id]:t}}if(_&&!e.hydrationData){ft(_,b,e.history.location.pathname).active&&(_=null)}if(_)if(_.some((e=>e.route.lazy)))h=!1;else if(_.some((e=>e.route.loader)))if(R.v7_partialHydration){let t=e.hydrationData?e.hydrationData.loaderData:null,r=e.hydrationData?e.hydrationData.errors:null,a=e=>!e.route.loader||("function"!=typeof e.route.loader||!0!==e.route.loader.hydrate)&&(t&&void 0!==t[e.route.id]||r&&void 0!==r[e.route.id]);if(r){let e=_.findIndex((e=>void 0!==r[e.route.id]));h=_.slice(0,e+1).every(a)}else h=_.every(a)}else h=null!=e.hydrationData;else h=!0;else if(h=!1,_=[],R.v7_partialHydration){let t=ft(null,b,e.history.location.pathname);t.active&&t.matches&&(_=t.matches)}let T,O,U={historyAction:e.history.action,location:e.history.location,matches:_,initialized:h,navigation:J,restoreScrollPosition:null==e.hydrationData&&null,preventScrollReset:!1,revalidation:"idle",loaderData:e.hydrationData&&e.hydrationData.loaderData||{},actionData:e.hydrationData&&e.hydrationData.actionData||null,errors:e.hydrationData&&e.hydrationData.errors||C,fetchers:new Map,blockers:new Map},I=r.Pop,H=!1,z=!1,F=new Map,B=null,W=!1,$=!1,q=[],K=new Set,ee=new Map,te=0,ne=-1,ie=new Map,se=new Set,ye=new Map,ve=new Map,ge=new Set,xe=new Map,ke=new Map,_e=new Map;function Ce(e,r){void 0===r&&(r={}),U=t({},U,e);let a=[],n=[];R.v7_fetcherPersist&&U.fetchers.forEach(((e,t)=>{"idle"===e.state&&(ge.has(t)?n.push(t):a.push(t))})),[...x].forEach((e=>e(U,{deletedFetchers:n,unstable_viewTransitionOpts:r.viewTransitionOpts,unstable_flushSync:!0===r.flushSync}))),R.v7_fetcherPersist&&(a.forEach((e=>U.fetchers.delete(e))),n.forEach((e=>et(e))))}function Te(a,n,o){var i,s;let l,{flushSync:u}=void 0===o?{}:o,d=null!=U.actionData&&null!=U.navigation.formMethod&&Oe(U.navigation.formMethod)&&"loading"===U.navigation.state&&!0!==(null==(i=a.state)?void 0:i._isRedirect);l=n.actionData?Object.keys(n.actionData).length>0?n.actionData:null:d?U.actionData:null;let h=n.loaderData?we(U.loaderData,n.loaderData,n.matches||[],n.errors):U.loaderData,f=U.blockers;f.size>0&&(f=new Map(f),f.forEach(((e,t)=>f.set(t,X))));let p,m=!0===H||null!=U.navigation.formMethod&&Oe(U.navigation.formMethod)&&!0!==(null==(s=a.state)?void 0:s._isRedirect);if(c&&(b=c,c=void 0),W||I===r.Pop||(I===r.Push?e.history.push(a,a.state):I===r.Replace&&e.history.replace(a,a.state)),I===r.Pop){let e=F.get(U.location.pathname);e&&e.has(a.pathname)?p={currentLocation:U.location,nextLocation:a}:F.has(a.pathname)&&(p={currentLocation:a,nextLocation:U.location})}else if(z){let e=F.get(U.location.pathname);e?e.add(a.pathname):(e=new Set([a.pathname]),F.set(U.location.pathname,e)),p={currentLocation:U.location,nextLocation:a}}Ce(t({},n,{actionData:l,loaderData:h,historyAction:I,location:a,initialized:!0,navigation:J,revalidation:"idle",restoreScrollPosition:ht(a,n.matches||U.matches),preventScrollReset:m,blockers:f}),{viewTransitionOpts:p,flushSync:!0===u}),I=r.Pop,H=!1,z=!1,W=!1,$=!1,q=[]}async function ze(a,n,o){T&&T.abort(),T=null,I=a,W=!0===(o&&o.startUninterruptedRevalidation),function(e,t){if(L&&j){let r=dt(e,t);L[r]=j()}}(U.location,U.matches),H=!0===(o&&o.preventScrollReset),z=!0===(o&&o.enableViewTransition);let i=c||b,s=o&&o.overrideNavigation,l=p(i,n,w),u=!0===(o&&o.flushSync),h=ft(l,i,n.pathname);if(h.active&&h.matches&&(l=h.matches),!l){let{error:e,notFoundMatches:t,route:r}=lt(n.pathname);return void Te(n,{matches:t,loaderData:{},errors:{[r.id]:e}},{flushSync:u})}if(U.initialized&&!$&&function(e,t){if(e.pathname!==t.pathname||e.search!==t.search)return!1;if(""===e.hash)return""!==t.hash;if(e.hash===t.hash)return!0;if(""!==t.hash)return!0;return!1}(U.location,n)&&!(o&&o.submission&&Oe(o.submission.formMethod)))return void Te(n,{matches:l},{flushSync:u});T=new AbortController;let f,m=me(e.history,n,T.signal,o&&o.submission);if(o&&o.pendingError)f=[Se(l).route.id,{type:d.error,error:o.pendingError}];else if(o&&o.submission&&Oe(o.submission.formMethod)){let t=await async function(e,t,a,n,o,i){void 0===i&&(i={});let s;if(Xe(),Ce({navigation:We(t,a)},{flushSync:!0===i.flushSync}),o){let r=await pt(n,t.pathname,e.signal);if("aborted"===r.type)return{shortCircuited:!0};if("error"===r.type){let{boundaryId:e,error:a}=ut(t.pathname,r);return{matches:r.partialMatches,pendingActionResult:[e,{type:d.error,error:a}]}}if(!r.matches){let{notFoundMatches:e,error:r,route:a}=lt(t.pathname);return{matches:e,pendingActionResult:[a.id,{type:d.error,error:r}]}}n=r.matches}let l=Fe(n,t);if(l.route.action||l.route.lazy){if(s=(await Je("action",U,e,[l],n,null))[l.route.id],e.signal.aborted)return{shortCircuited:!0}}else s={type:d.error,error:Ee(405,{method:e.method,pathname:t.pathname,routeId:l.route.id})};if(je(s)){let t;if(i&&null!=i.replace)t=i.replace;else{t=pe(s.response.headers.get("Location"),new URL(e.url),w)===U.location.pathname+U.location.search}return await Ye(e,s,!0,{submission:a,replace:t}),{shortCircuited:!0}}if(Le(s))throw Ee(400,{type:"defer-action"});if(Me(s)){let e=Se(n,l.route.id);return!0!==(i&&i.replace)&&(I=r.Push),{matches:n,pendingActionResult:[e.route.id,s]}}return{matches:n,pendingActionResult:[l.route.id,s]}}(m,n,o.submission,l,h.active,{replace:o.replace,flushSync:u});if(t.shortCircuited)return;if(t.pendingActionResult){let[e,r]=t.pendingActionResult;if(Me(r)&&N(r.error)&&404===r.error.status)return T=null,void Te(n,{matches:t.matches,loaderData:{},errors:{[e]:r.error}})}l=t.matches||l,f=t.pendingActionResult,s=Be(n,o.submission),u=!1,h.active=!1,m=me(e.history,m.url,m.signal)}let{shortCircuited:y,matches:v,loaderData:g,errors:D}=await async function(r,a,n,o,i,s,l,u,d,h,f){let p=i||Be(a,s),m=s||l||Ne(p),y=!(W||R.v7_partialHydration&&d);if(o){if(y){let e=Ke(f);Ce(t({navigation:p},void 0!==e?{actionData:e}:{}),{flushSync:h})}let e=await pt(n,a.pathname,r.signal);if("aborted"===e.type)return{shortCircuited:!0};if("error"===e.type){let{boundaryId:t,error:r}=ut(a.pathname,e);return{matches:e.partialMatches,loaderData:{},errors:{[t]:r}}}if(!e.matches){let{error:e,notFoundMatches:t,route:r}=lt(a.pathname);return{matches:t,loaderData:{},errors:{[r.id]:e}}}n=e.matches}let v=c||b,[g,D]=oe(e.history,U,n,m,a,R.v7_partialHydration&&!0===d,R.v7_skipActionErrorRevalidation,$,q,K,ge,ye,se,v,w,f);if(ct((e=>!(n&&n.some((t=>t.route.id===e)))||g&&g.some((t=>t.route.id===e)))),ne=++te,0===g.length&&0===D.length){let e=at();return Te(a,t({matches:n,loaderData:{},errors:f&&Me(f[1])?{[f[0]]:f[1].error}:null},De(f),e?{fetchers:new Map(U.fetchers)}:{}),{flushSync:h}),{shortCircuited:!0}}if(y){let e={};if(!o){e.navigation=p;let t=Ke(f);void 0!==t&&(e.actionData=t)}D.length>0&&(e.fetchers=function(e){return e.forEach((e=>{let t=U.fetchers.get(e.key),r=$e(void 0,t?t.data:void 0);U.fetchers.set(e.key,r)})),new Map(U.fetchers)}(D)),Ce(e,{flushSync:h})}D.forEach((e=>{ee.has(e.key)&&tt(e.key),e.controller&&ee.set(e.key,e.controller)}));let S=()=>D.forEach((e=>tt(e.key)));T&&T.signal.addEventListener("abort",S);let{loaderResults:E,fetcherResults:P}=await Ve(U,n,g,D,r);if(r.signal.aborted)return{shortCircuited:!0};T&&T.signal.removeEventListener("abort",S);D.forEach((e=>ee.delete(e.key)));let x=Pe(E);if(x)return await Ye(r,x.result,!0,{replace:u}),{shortCircuited:!0};if(x=Pe(P),x)return se.add(x.key),await Ye(r,x.result,!0,{replace:u}),{shortCircuited:!0};let{loaderData:A,errors:L}=be(U,n,g,E,f,D,P,xe);xe.forEach(((e,t)=>{e.subscribe((r=>{(r||e.done)&&xe.delete(t)}))})),R.v7_partialHydration&&d&&U.errors&&Object.entries(U.errors).filter((e=>{let[t]=e;return!g.some((e=>e.route.id===t))})).forEach((e=>{let[t,r]=e;L=Object.assign(L||{},{[t]:r})}));let M=at(),j=nt(ne),k=M||j||D.length>0;return t({matches:n,loaderData:A,errors:L},k?{fetchers:new Map(U.fetchers)}:{})}(m,n,l,h.active,s,o&&o.submission,o&&o.fetcherSubmission,o&&o.replace,o&&!0===o.initialHydration,u,f);y||(T=null,Te(n,t({matches:v||l},De(f),{loaderData:g,errors:D})))}function Ke(e){return e&&!Me(e[1])?{[e[0]]:e[1].data}:U.actionData?0===Object.keys(U.actionData).length?null:U.actionData:void 0}async function Ye(o,l,u,c){let{submission:d,fetcherSubmission:h,replace:f}=void 0===c?{}:c;l.response.headers.has("X-Remix-Revalidate")&&($=!0);let p=l.response.headers.get("Location");n(p,"Expected a Location header on the redirect Response"),p=pe(p,new URL(o.url),w);let m=s(U.location,p,{_isRedirect:!0});if(i){let t=!1;if(l.response.headers.has("X-Remix-Reload-Document"))t=!0;else if(G.test(p)){const r=e.history.createURL(p);t=r.origin!==a.location.origin||null==P(r.pathname,w)}if(t)return void(f?a.location.replace(p):a.location.assign(p))}T=null;let y=!0===f||l.response.headers.has("X-Remix-Replace")?r.Replace:r.Push,{formMethod:v,formAction:g,formEncType:b}=U.navigation;!d&&!h&&v&&g&&b&&(d=Ne(U.navigation));let D=d||h;if(Y.has(l.response.status)&&D&&Oe(D.formMethod))await ze(y,m,{submission:t({},D,{formAction:p}),preventScrollReset:H,enableViewTransition:u?z:void 0});else{let e=Be(m,d);await ze(y,m,{overrideNavigation:e,fetcherSubmission:h,preventScrollReset:H,enableViewTransition:u?z:void 0})}}async function Je(e,t,r,a,n,o){let i,s={};try{i=await de(D,e,t,r,a,n,o,g,u)}catch(e){return a.forEach((t=>{s[t.route.id]={type:d.error,error:e}})),s}for(let[e,t]of Object.entries(i))if(Ae(t)){let a=t.result;s[e]={type:d.redirect,response:fe(a,r,e,n,w,R.v7_relativeSplatPath)}}else s[e]=await he(t);return s}async function Ve(t,r,a,n,o){let i=t.matches,s=Je("loader",t,o,a,r,null),l=Promise.all(n.map((async r=>{if(r.matches&&r.match&&r.controller){let a=(await Je("loader",t,me(e.history,r.path,r.controller.signal),[r.match],r.matches,r.key))[r.match.route.id];return{[r.key]:a}}return Promise.resolve({[r.key]:{type:d.error,error:Ee(404,{pathname:r.path})}})}))),u=await s,c=(await l).reduce(((e,t)=>Object.assign(e,t)),{});return await Promise.all([Ue(r,u,o.signal,i,t.loaderData),Ie(r,c,n)]),{loaderResults:u,fetcherResults:c}}function Xe(){$=!0,q.push(...ct()),ye.forEach(((e,t)=>{ee.has(t)&&(K.add(t),tt(t))}))}function Ge(e,t,r){void 0===r&&(r={}),U.fetchers.set(e,t),Ce({fetchers:new Map(U.fetchers)},{flushSync:!0===(r&&r.flushSync)})}function Qe(e,t,r,a){void 0===a&&(a={});let n=Se(U.matches,t);et(e),Ce({errors:{[n.route.id]:r},fetchers:new Map(U.fetchers)},{flushSync:!0===(a&&a.flushSync)})}function Ze(e){return R.v7_fetcherPersist&&(ve.set(e,(ve.get(e)||0)+1),ge.has(e)&&ge.delete(e)),U.fetchers.get(e)||V}function et(e){let t=U.fetchers.get(e);!ee.has(e)||t&&"loading"===t.state&&ie.has(e)||tt(e),ye.delete(e),ie.delete(e),se.delete(e),ge.delete(e),K.delete(e),U.fetchers.delete(e)}function tt(e){let t=ee.get(e);n(t,"Expected fetch controller: "+e),t.abort(),ee.delete(e)}function rt(e){for(let t of e){let e=qe(Ze(t).data);U.fetchers.set(t,e)}}function at(){let e=[],t=!1;for(let r of se){let a=U.fetchers.get(r);n(a,"Expected fetcher: "+r),"loading"===a.state&&(se.delete(r),e.push(r),t=!0)}return rt(e),t}function nt(e){let t=[];for(let[r,a]of ie)if(a<e){let e=U.fetchers.get(r);n(e,"Expected fetcher: "+r),"loading"===e.state&&(tt(r),ie.delete(r),t.push(r))}return rt(t),t.length>0}function ot(e){U.blockers.delete(e),ke.delete(e)}function it(e,t){let r=U.blockers.get(e)||X;n("unblocked"===r.state&&"blocked"===t.state||"blocked"===r.state&&"blocked"===t.state||"blocked"===r.state&&"proceeding"===t.state||"blocked"===r.state&&"unblocked"===t.state||"proceeding"===r.state&&"unblocked"===t.state,"Invalid blocker state transition: "+r.state+" -> "+t.state);let a=new Map(U.blockers);a.set(e,t),Ce({blockers:a})}function st(e){let{currentLocation:t,nextLocation:r,historyAction:a}=e;if(0===ke.size)return;ke.size>1&&o(!1,"A router only supports one blocker at a time");let n=Array.from(ke.entries()),[i,s]=n[n.length-1],l=U.blockers.get(i);return l&&"proceeding"===l.state?void 0:s({currentLocation:t,nextLocation:r,historyAction:a})?i:void 0}function lt(e){let t=Ee(404,{pathname:e}),r=c||b,{matches:a,route:n}=Re(r);return ct(),{notFoundMatches:a,route:n,error:t}}function ut(e,t){return{boundaryId:Se(t.partialMatches).route.id,error:Ee(400,{type:"route-discovery",pathname:e,message:null!=t.error&&"message"in t.error?t.error:String(t.error)})}}function ct(e){let t=[];return xe.forEach(((r,a)=>{e&&!e(a)||(r.cancel(),t.push(a),xe.delete(a))})),t}function dt(e,t){if(M){return M(e,t.map((e=>y(e,U.loaderData))))||e.key}return e.key}function ht(e,t){if(L){let r=dt(e,t),a=L[r];if("number"==typeof a)return a}return null}function ft(e,t,r){if(S){if(A.has(r))return{active:!1,matches:e};if(!e){return{active:!0,matches:m(t,r,w,!0)||[]}}if(Object.keys(e[0].params).length>0){return{active:!0,matches:m(t,r,w,!0)}}}return{active:!1,matches:null}}async function pt(e,t,r){let a=e;for(;;){let e=null==c,n=c||b;try{await le(S,t,a,n,g,u,_e,r)}catch(e){return{type:"error",error:e,partialMatches:a}}finally{e&&(b=[...b])}if(r.aborted)return{type:"aborted"};let o=p(n,t,w);if(o)return mt(t,A),{type:"success",matches:o};let i=m(n,t,w,!0);if(!i||a.length===i.length&&a.every(((e,t)=>e.route.id===i[t].route.id)))return mt(t,A),{type:"success",matches:null};a=i}}function mt(e,t){if(t.size>=1e3){let e=t.values().next().value;t.delete(e)}t.add(e)}return v={get basename(){return w},get future(){return R},get state(){return U},get routes(){return b},get window(){return a},initialize:function(){if(E=e.history.listen((t=>{let{action:r,location:a,delta:n}=t;if(O)return O(),void(O=void 0);o(0===ke.size||null!=n,"You are trying to use a blocker on a POP navigation to a location that was not created by @remix-run/router. This will fail silently in production. This can happen if you are navigating outside the router via `window.history.pushState`/`window.location.hash` instead of using router navigation APIs.  This can also happen if you are using createHashRouter and the user manually changes the URL.");let i=st({currentLocation:U.location,nextLocation:a,historyAction:r});if(i&&null!=n){let t=new Promise((e=>{O=e}));return e.history.go(-1*n),void it(i,{state:"blocked",location:a,proceed(){it(i,{state:"proceeding",proceed:void 0,reset:void 0,location:a}),t.then((()=>e.history.go(n)))},reset(){let e=new Map(U.blockers);e.set(i,X),Ce({blockers:e})}})}return ze(r,a)})),i){!function(e,t){try{let r=e.sessionStorage.getItem(Z);if(r){let e=JSON.parse(r);for(let[r,a]of Object.entries(e||{}))a&&Array.isArray(a)&&t.set(r,new Set(a||[]))}}catch(e){}}(a,F);let e=()=>function(e,t){if(t.size>0){let r={};for(let[e,a]of t)r[e]=[...a];try{e.sessionStorage.setItem(Z,JSON.stringify(r))}catch(e){o(!1,"Failed to save applied view transitions in sessionStorage ("+e+").")}}}(a,F);a.addEventListener("pagehide",e),B=()=>a.removeEventListener("pagehide",e)}return U.initialized||ze(r.Pop,U.location,{initialHydration:!0}),v},subscribe:function(e){return x.add(e),()=>x.delete(e)},enableScrollRestoration:function(e,t,r){if(L=e,j=t,M=r||null,!k&&U.navigation===J){k=!0;let e=ht(U.location,U.matches);null!=e&&Ce({restoreScrollPosition:e})}return()=>{L=null,j=null,M=null}},navigate:async function a(n,o){if("number"==typeof n)return void e.history.go(n);let i=re(U.location,U.matches,w,R.v7_prependBasename,n,R.v7_relativeSplatPath,null==o?void 0:o.fromRouteId,null==o?void 0:o.relative),{path:l,submission:u,error:c}=ae(R.v7_normalizeFormMethod,!1,i,o),d=U.location,h=s(U.location,l,o&&o.state);h=t({},h,e.history.encodeLocation(h));let f=o&&null!=o.replace?o.replace:void 0,p=r.Push;!0===f?p=r.Replace:!1===f||null!=u&&Oe(u.formMethod)&&u.formAction===U.location.pathname+U.location.search&&(p=r.Replace);let m=o&&"preventScrollReset"in o?!0===o.preventScrollReset:void 0,y=!0===(o&&o.unstable_flushSync),v=st({currentLocation:d,nextLocation:h,historyAction:p});if(!v)return await ze(p,h,{submission:u,pendingError:c,preventScrollReset:m,replace:o&&o.replace,enableViewTransition:o&&o.unstable_viewTransition,flushSync:y});it(v,{state:"blocked",location:h,proceed(){it(v,{state:"proceeding",proceed:void 0,reset:void 0,location:h}),a(n,o)},reset(){let e=new Map(U.blockers);e.set(v,X),Ce({blockers:e})}})},fetch:function(t,r,a,o){if(l)throw new Error("router.fetch() was called during the server render, but it shouldn't be. You are likely calling a useFetcher() method in the body of your component. Try moving it to a useEffect or a callback.");ee.has(t)&&tt(t);let i=!0===(o&&o.unstable_flushSync),s=c||b,u=re(U.location,U.matches,w,R.v7_prependBasename,a,R.v7_relativeSplatPath,r,null==o?void 0:o.relative),d=p(s,u,w),h=ft(d,s,u);if(h.active&&h.matches&&(d=h.matches),!d)return void Qe(t,r,Ee(404,{pathname:u}),{flushSync:i});let{path:f,submission:m,error:y}=ae(R.v7_normalizeFormMethod,!0,u,o);if(y)return void Qe(t,r,y,{flushSync:i});let v=Fe(d,f);H=!0===(o&&o.preventScrollReset),m&&Oe(m.formMethod)?async function(t,r,a,o,i,s,l,u){function d(e){if(!e.route.action&&!e.route.lazy){let e=Ee(405,{method:u.formMethod,pathname:a,routeId:r});return Qe(t,r,e,{flushSync:l}),!0}return!1}if(Xe(),ye.delete(t),!s&&d(o))return;let h=U.fetchers.get(t);Ge(t,function(e,t){return{state:"submitting",formMethod:e.formMethod,formAction:e.formAction,formEncType:e.formEncType,formData:e.formData,json:e.json,text:e.text,data:t?t.data:void 0}}(u,h),{flushSync:l});let f=new AbortController,m=me(e.history,a,f.signal,u);if(s){let e=await pt(i,a,m.signal);if("aborted"===e.type)return;if("error"===e.type){let{error:n}=ut(a,e);return void Qe(t,r,n,{flushSync:l})}if(!e.matches)return void Qe(t,r,Ee(404,{pathname:a}),{flushSync:l});if(d(o=Fe(i=e.matches,a)))return}ee.set(t,f);let y=te,v=(await Je("action",U,m,[o],i,t))[o.route.id];if(m.signal.aborted)return void(ee.get(t)===f&&ee.delete(t));if(R.v7_fetcherPersist&&ge.has(t)){if(je(v)||Me(v))return void Ge(t,qe(void 0))}else{if(je(v))return ee.delete(t),ne>y?void Ge(t,qe(void 0)):(se.add(t),Ge(t,$e(u)),Ye(m,v,!1,{fetcherSubmission:u}));if(Me(v))return void Qe(t,r,v.error)}if(Le(v))throw Ee(400,{type:"defer-action"});let g=U.navigation.location||U.location,D=me(e.history,g,f.signal),S=c||b,E="idle"!==U.navigation.state?p(S,U.navigation.location,w):U.matches;n(E,"Didn't find any matches after fetcher action");let P=++te;ie.set(t,P);let x=$e(u,v.data);U.fetchers.set(t,x);let[A,L]=oe(e.history,U,E,u,g,!1,R.v7_skipActionErrorRevalidation,$,q,K,ge,ye,se,S,w,[o.route.id,v]);L.filter((e=>e.key!==t)).forEach((e=>{let t=e.key,r=U.fetchers.get(t),a=$e(void 0,r?r.data:void 0);U.fetchers.set(t,a),ee.has(t)&&tt(t),e.controller&&ee.set(t,e.controller)})),Ce({fetchers:new Map(U.fetchers)});let M=()=>L.forEach((e=>tt(e.key)));f.signal.addEventListener("abort",M);let{loaderResults:j,fetcherResults:k}=await Ve(U,E,A,L,D);if(f.signal.aborted)return;f.signal.removeEventListener("abort",M),ie.delete(t),ee.delete(t),L.forEach((e=>ee.delete(e.key)));let _=Pe(j);if(_)return Ye(D,_.result,!1);if(_=Pe(k),_)return se.add(_.key),Ye(D,_.result,!1);let{loaderData:C,errors:O}=be(U,E,A,j,void 0,L,k,xe);if(U.fetchers.has(t)){let e=qe(v.data);U.fetchers.set(t,e)}nt(P),"loading"===U.navigation.state&&P>ne?(n(I,"Expected pending action"),T&&T.abort(),Te(U.navigation.location,{matches:E,loaderData:C,errors:O,fetchers:new Map(U.fetchers)})):(Ce({errors:O,loaderData:we(U.loaderData,C,E,O),fetchers:new Map(U.fetchers)}),$=!1)}(t,r,f,v,d,h.active,i,m):(ye.set(t,{routeId:r,path:f}),async function(t,r,a,o,i,s,l,u){let c=U.fetchers.get(t);Ge(t,$e(u,c?c.data:void 0),{flushSync:l});let d=new AbortController,h=me(e.history,a,d.signal);if(s){let e=await pt(i,a,h.signal);if("aborted"===e.type)return;if("error"===e.type){let{error:n}=ut(a,e);return void Qe(t,r,n,{flushSync:l})}if(!e.matches)return void Qe(t,r,Ee(404,{pathname:a}),{flushSync:l});o=Fe(i=e.matches,a)}ee.set(t,d);let f=te,p=(await Je("loader",U,h,[o],i,t))[o.route.id];Le(p)&&(p=await He(p,h.signal,!0)||p);ee.get(t)===d&&ee.delete(t);if(h.signal.aborted)return;if(ge.has(t))return void Ge(t,qe(void 0));if(je(p))return ne>f?void Ge(t,qe(void 0)):(se.add(t),void await Ye(h,p,!1));if(Me(p))return void Qe(t,r,p.error);n(!Le(p),"Unhandled fetcher deferred data"),Ge(t,qe(p.data))}(t,r,f,v,d,h.active,i,m))},revalidate:function(){Xe(),Ce({revalidation:"loading"}),"submitting"!==U.navigation.state&&("idle"!==U.navigation.state?ze(I||U.historyAction,U.navigation.location,{overrideNavigation:U.navigation,enableViewTransition:!0===z}):ze(U.historyAction,U.location,{startUninterruptedRevalidation:!0}))},createHref:t=>e.history.createHref(t),encodeLocation:t=>e.history.encodeLocation(t),getFetcher:Ze,deleteFetcher:function(e){if(R.v7_fetcherPersist){let t=(ve.get(e)||0)-1;t<=0?(ve.delete(e),ge.add(e)):ve.set(e,t)}else et(e);Ce({fetchers:new Map(U.fetchers)})},dispose:function(){E&&E(),B&&B(),x.clear(),T&&T.abort(),U.fetchers.forEach(((e,t)=>et(t))),U.blockers.forEach(((e,t)=>ot(t)))},getBlocker:function(e,t){let r=U.blockers.get(e)||X;return ke.get(e)!==t&&ke.set(e,t),r},deleteBlocker:ot,patchRoutes:function(e,t){let r=null==c;ue(e,t,c||b,g,u),r&&(b=[...b],Ce({}))},_internalFetchControllers:ee,_internalActiveDeferreds:xe,_internalSetRoutes:function(e){g={},c=f(e,u,void 0,g)}},v},e.createStaticHandler=function(e,r){n(e.length>0,"You must provide a non-empty routes array to createStaticHandler");let a,o={},i=(r?r.basename:null)||"/";if(null!=r&&r.mapRouteProperties)a=r.mapRouteProperties;else if(null!=r&&r.detectErrorBoundary){let e=r.detectErrorBoundary;a=t=>({hasErrorBoundary:e(t)})}else a=Q;let u=t({v7_relativeSplatPath:!1,v7_throwAbortReason:!1},r?r.future:null),c=f(e,a,void 0,o);async function h(e,r,a,o,i,s,l){n(e.signal,"query()/queryRoute() requests must contain an AbortController signal");try{if(Oe(e.method.toLowerCase())){let n=await async function(e,r,a,n,o,i,s){let l;if(a.route.action||a.route.lazy){l=(await y("action",e,[a],r,s,n,o))[a.route.id],e.signal.aborted&&te(e,s,u)}else{let t=Ee(405,{method:e.method,pathname:new URL(e.url).pathname,routeId:a.route.id});if(s)throw t;l={type:d.error,error:t}}if(je(l))throw new Response(null,{status:l.response.status,headers:{Location:l.response.headers.get("Location")}});if(Le(l)){let e=Ee(400,{type:"defer-action"});if(s)throw e;l={type:d.error,error:e}}if(s){if(Me(l))throw l.error;return{matches:[a],loaderData:{},actionData:{[a.route.id]:l.data},errors:null,statusCode:200,loaderHeaders:{},actionHeaders:{},activeDeferreds:null}}let c=new Request(e.url,{headers:e.headers,redirect:e.redirect,signal:e.signal});if(Me(l)){let e=i?a:Se(r,a.route.id);return t({},await m(c,r,n,o,i,null,[e.route.id,l]),{statusCode:N(l.error)?l.error.status:null!=l.statusCode?l.statusCode:500,actionData:null,actionHeaders:t({},l.headers?{[a.route.id]:l.headers}:{})})}return t({},await m(c,r,n,o,i,null),{actionData:{[a.route.id]:l.data}},l.statusCode?{statusCode:l.statusCode}:{},{actionHeaders:l.headers?{[a.route.id]:l.headers}:{}})}(e,a,l||Fe(a,r),o,i,s,null!=l);return n}let n=await m(e,a,o,i,s,l);return Ce(n)?n:t({},n,{actionData:null,actionHeaders:{}})}catch(e){if(function(e){return null!=e&&"object"==typeof e&&"type"in e&&"result"in e&&(e.type===d.data||e.type===d.error)}(e)&&Ce(e.result)){if(e.type===d.error)throw e.result;return e.result}if(function(e){if(!Ce(e))return!1;let t=e.status,r=e.headers.get("Location");return t>=300&&t<=399&&null!=r}(e))return e;throw e}}async function m(e,r,a,n,o,i,s){let l=null!=i;if(l&&(null==i||!i.route.loader)&&(null==i||!i.route.lazy))throw Ee(400,{method:e.method,pathname:new URL(e.url).pathname,routeId:null==i?void 0:i.route.id});let c=(i?[i]:s&&Me(s[1])?ne(r,s[0]):r).filter((e=>e.route.loader||e.route.lazy));if(0===c.length)return{matches:r,loaderData:r.reduce(((e,t)=>Object.assign(e,{[t.route.id]:null})),{}),errors:s&&Me(s[1])?{[s[0]]:s[1].error}:null,statusCode:200,loaderHeaders:{},activeDeferreds:null};let d=await y("loader",e,c,r,l,a,n);e.signal.aborted&&te(e,l,u);let h=new Map,f=ge(r,d,s,h,o),p=new Set(c.map((e=>e.route.id)));return r.forEach((e=>{p.has(e.route.id)||(f.loaderData[e.route.id]=null)})),t({},f,{matches:r,activeDeferreds:h.size>0?Object.fromEntries(h.entries()):null})}async function y(e,t,r,n,s,l,c){let d=await de(c||ce,e,null,t,r,n,null,o,a,l),h={};return await Promise.all(n.map((async e=>{if(!(e.route.id in d))return;let r=d[e.route.id];if(Ae(r)){throw fe(r.result,t,e.route.id,n,i,u.v7_relativeSplatPath)}if(Ce(r.result)&&s)throw r;h[e.route.id]=await he(r)}))),h}return{dataRoutes:c,query:async function(e,r){let{requestContext:a,skipLoaderErrorBubbling:n,unstable_dataStrategy:o}=void 0===r?{}:r,u=new URL(e.url),d=e.method,f=s("",l(u),null,"default"),m=p(c,f,i);if(!Te(d)&&"HEAD"!==d){let e=Ee(405,{method:d}),{matches:t,route:r}=Re(c);return{basename:i,location:f,matches:t,loaderData:{},actionData:null,errors:{[r.id]:e},statusCode:e.status,loaderHeaders:{},actionHeaders:{},activeDeferreds:null}}if(!m){let e=Ee(404,{pathname:f.pathname}),{matches:t,route:r}=Re(c);return{basename:i,location:f,matches:t,loaderData:{},actionData:null,errors:{[r.id]:e},statusCode:e.status,loaderHeaders:{},actionHeaders:{},activeDeferreds:null}}let y=await h(e,f,m,a,o||null,!0===n,null);return Ce(y)?y:t({location:f,basename:i},y)},queryRoute:async function(e,t){let{routeId:r,requestContext:a,unstable_dataStrategy:n}=void 0===t?{}:t,o=new URL(e.url),u=e.method,d=s("",l(o),null,"default"),f=p(c,d,i);if(!Te(u)&&"HEAD"!==u&&"OPTIONS"!==u)throw Ee(405,{method:u});if(!f)throw Ee(404,{pathname:d.pathname});let m=r?f.find((e=>e.route.id===r)):Fe(f,d);if(r&&!m)throw Ee(403,{pathname:d.pathname,routeId:r});if(!m)throw Ee(404,{pathname:d.pathname});let y=await h(e,d,f,a,n||null,!1,m);if(Ce(y))return y;let v=y.errors?Object.values(y.errors)[0]:void 0;if(void 0!==v)throw v;if(y.actionData)return Object.values(y.actionData)[0];if(y.loaderData){var g;let e=Object.values(y.loaderData)[0];return null!=(g=y.activeDeferreds)&&g[m.route.id]&&(e[ee]=y.activeDeferreds[m.route.id]),e}}}},e.defer=function(e,t){return void 0===t&&(t={}),new I(e,"number"==typeof t?{status:t}:t)},e.generatePath=function(e,t){void 0===t&&(t={});let r=e;r.endsWith("*")&&"*"!==r&&!r.endsWith("/*")&&(o(!1,'Route path "'+r+'" will be treated as if it were "'+r.replace(/\*$/,"/*")+'" because the `*` character must always follow a `/` in the pattern. To get rid of this warning, please change the route path to "'+r.replace(/\*$/,"/*")+'".'),r=r.replace(/\*$/,"/*"));const a=r.startsWith("/")?"/":"",i=e=>null==e?"":"string"==typeof e?e:String(e);return a+r.split(/\/+/).map(((e,r,a)=>{if(r===a.length-1&&"*"===e){return i(t["*"])}const o=e.match(/^:([\w-]+)(\??)$/);if(o){const[,e,r]=o;let a=t[e];return n("?"===r||null!=a,'Missing ":'+e+'" param'),i(a)}return e.replace(/\?$/g,"")})).filter((e=>!!e)).join("/")},e.getStaticContextFromError=function(e,r,a){return t({},r,{statusCode:N(a)?a.status:500,errors:{[r._deepestRenderedBoundaryId||e[0].id]:a}})},e.getToPathname=function(e){return""===e||""===e.pathname?"/":"string"==typeof e?u(e).pathname:e.pathname},e.isDataWithResponseInit=ke,e.isDeferredData=_e,e.isRouteErrorResponse=N,e.joinPaths=k,e.json=function(e,r){void 0===r&&(r={});let a="number"==typeof r?{status:r}:r,n=new Headers(a.headers);return n.has("Content-Type")||n.set("Content-Type","application/json; charset=utf-8"),new Response(JSON.stringify(e),t({},a,{headers:n}))},e.matchPath=R,e.matchRoutes=p,e.normalizePathname=_,e.parsePath=u,e.redirect=z,e.redirectDocument=(e,t)=>{let r=z(e,t);return r.headers.set("X-Remix-Reload-Document","true"),r},e.replace=(e,t)=>{let r=z(e,t);return r.headers.set("X-Remix-Replace","true"),r},e.resolvePath=x,e.resolveTo=j,e.stripBasename=P,e.unstable_data=function(e,t){return new O(e,"number"==typeof t?{status:t}:t)},Object.defineProperty(e,"__esModule",{value:!0})}));
//# sourceMappingURL=router.umd.min.js.map
