"use strict";
exports.nlBE = void 0;
var _index = require("./nl-BE/_lib/formatDistance.js");
var _index2 = require("./nl-BE/_lib/formatLong.js");
var _index3 = require("./nl-BE/_lib/formatRelative.js");
var _index4 = require("./nl-BE/_lib/localize.js");
var _index5 = require("./nl-BE/_lib/match.js");

/**
 * @category Locales
 * @summary Dutch locale.
 * @language Dutch
 * @iso-639-2 nld
 * <AUTHOR> [@jtangelder](https://github.com/jtangelder)
 * <AUTHOR> [@rubenstolk](https://github.com/rubenstolk)
 * <AUTHOR> [@bitcrumb](https://github.com/bitcrumb)
 * <AUTHOR> [@dcbn](https://github.com/dcbn)
 */
const nlBE = (exports.nlBE = {
  code: "nl-BE",
  formatDistance: _index.formatDistance,
  formatLong: _index2.formatLong,
  formatRelative: _index3.formatRelative,
  localize: _index4.localize,
  match: _index5.match,
  options: {
    weekStartsOn: 1 /* Monday */,
    firstWeekContainsDate: 4,
  },
});
