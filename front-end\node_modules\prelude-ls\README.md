# prelude.ls [![Build Status](https://travis-ci.org/gkz/prelude-ls.png?branch=master)](https://travis-ci.org/gkz/prelude-ls)

is a functionally oriented utility library. It is powerful and flexible. Almost all of its functions are curried. It is written in, and is the recommended base library for, <a href="http://livescript.net">LiveScript</a>.

See **[the prelude.ls site](http://preludels.com)** for examples, a reference, and more.

You can install via npm `npm install prelude-ls`

### Development

`make test` to test

`make build` to build `lib` from `src`

`make build-browser` to build browser versions
