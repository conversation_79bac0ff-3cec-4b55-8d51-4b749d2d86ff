import { formatDistance } from "./en-US/_lib/formatDistance.mjs";
import { formatLong } from "./en-US/_lib/formatLong.mjs";
import { formatRelative } from "./en-US/_lib/formatRelative.mjs";
import { localize } from "./en-US/_lib/localize.mjs";
import { match } from "./en-US/_lib/match.mjs";

/**
 * @category Locales
 * @summary English locale (United States).
 * @language English
 * @iso-639-2 eng
 * <AUTHOR> [@kossnocorp](https://github.com/kossnocorp)
 * <AUTHOR> [@leshakoss](https://github.com/leshakoss)
 */
export const enUS = {
  code: "en-US",
  formatDistance: formatDistance,
  formatLong: formatLong,
  formatRelative: formatRelative,
  localize: localize,
  match: match,
  options: {
    weekStartsOn: 0 /* Sunday */,
    firstWeekContainsDate: 1,
  },
};

// Fallback for modularized imports:
export default enUS;
