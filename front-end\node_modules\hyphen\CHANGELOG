# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [Unreleased]

## [1.10.6]

### Changed

- Optimized patterns dictionary size

## [1.10.5]

### Fixed

- Fixed the issue with bad hyphenation, that started in version 1.7.0

## [1.10.4]

### Fixed

- Fixed problem with `en-us.cjs.js` in package build

## [1.10.3]

### Changed

- Optimized text parsing when `html` option is `false`

## [1.10.2]

### Changed

- Destructured text reader into composable verifiers

- Optimized patterns storage to reduce patterns file size

## [1.10.1]

### Changed

- Optimized patterns storage to reduce patterns file size

### Fixed

- Fixed hyphenation exceptions for Norwegian-bokmål, Norwegian-nynorsk language patterns; minor patterns fixes

## [1.10.0]

### Changed

- BREAKING CHANGE: Option `html` default value changed to `true`

## [1.9.1] - 2023-11-17

### Changed

- Simplified patterns export container

### Fixed

- Fixed broken patterns for Norwegian-bokmål, Norwegian-nynorsk language patterns

## [1.9.0] - 2023-11-13

### Changed

- Moved patterns trie generation step to build phase

## [1.8.0] - 2023-11-08

### Added

- Added `exceptions` option

### Changed

- Exclude debug code from production build

### Fixed

- Fixed broken patterns exceptions

## [1.7.2] - 2023-11-07

### Changed

- Reworked text reader in favour of if statements

## [1.7.1] - 2023-10-29

### Fixed

- Fixed bad hyphenation of words with apostrophe symbol

## [1.7.0] - 2023-10-11

### Changed

- Prebuild a dictionary trie to improve performance of a hyphenation algorithm

## [1.6.6] - 2023-06-04

### Fixed

- Minor performance fix

## [1.6.5] - 2023-03-11

### Added

- Add async web worker support

## [1.6.4] - 2021-04-04

### Fixed

- Handle the edge case of a word “constructor” being interpreted as a JavaScript keyword. - by [@arseni-mourzenko](https://github.com/arseni-mourzenko)

## [1.6.3] - 2021-04-04

### Fixed

- Change undesired behavior of option `minWordLength`: value now can be set to less than 5 - by [@kamilmielnik](https://github.com/kamilmielnik)

## [1.6.2] - 2020-06-03

### Fixed

- Prevent hyphenation of HTML attributes in hyphenateHTML

## [1.6.0] - 2020-05-08

### Added

- Add option `minWordLength`

## [1.5.0] - 2020-04-03

### Added

- Add ability to configure initialized hyphenator function
- Add exports for nodejs and webpack environments

### Changed

- Skip HTML syntax only when `html` option is set to true
- License changed to ISC

### Fixed

- Fix bad script behavior in async mode

## [1.3.1] - 2020-03-28

### Changed

- Split source code into several files with following bundling

### Fixed

- Replaced template literals for ES3 compatibility

## [1.3.0] - 2020-03-25

### Added

- Add HTML sections exclusion from hyphenation
- Add async mode
- Protect hyphenated text from repeated hyphenation

## [1.2.1] - 2020-03-18

### Changed

- Reduced NPM package size

## [1.2.0] - 2020-03-18

### Fixed

- Fixed badly processed repeated patterns in a word by [@jaumeortola](https://github.com/jaumeortola)
- Fixed a case when a special character is considered a letter

## [1.1.1] - 2019-03-20

### Changed

- Replaced a unicode literal char with a corresponding code by [@oskarkook](https://github.com/oskarkook)

### Fixed

- Bugfix by [@krisztianb](https://github.com/krisztianb)

## [1.1.0] - 2019-02-10

### Changed

- Rebuilt patterns with tex2js translator

### Fixed

- Fixed bad patternData by [@datakurre](https://github.com/datakurre)
- Fixed typos by [@millette](https://github.com/millette)

## [1.0.2] - 2016-08-28

### Fixed

- Fixed bad return value when EOF by [@nicosierra](https://github.com/nicosierra)

## [1.0.1] - 2016-08-18

### Added

- Output stats in debug mode

### Fixed

- Fixed typos by [@jhwohlgemuth](https://github.com/jhwohlgemuth)
- Fixed bad word extraction

## [1.0.0] - 2016-08-07

### Added

- First working version with 75 language patterns

[unreleased]: https://github.com/ytiurin/hyphen/compare/v1.10.6...HEAD
[v1.10.6]: https://github.com/ytiurin/hyphen/compare/v1.10.5...v1.10.6
[v1.10.5]: https://github.com/ytiurin/hyphen/compare/v1.10.4...v1.10.5
[v1.10.4]: https://github.com/ytiurin/hyphen/compare/v1.10.3...v1.10.4
[v1.10.3]: https://github.com/ytiurin/hyphen/compare/v1.10.2...v1.10.3
[v1.10.2]: https://github.com/ytiurin/hyphen/compare/v1.10.1...v1.10.2
[v1.10.1]: https://github.com/ytiurin/hyphen/compare/v1.10.0...v1.10.1
[v1.10.0]: https://github.com/ytiurin/hyphen/compare/v1.9.1...v1.10.0
[v1.9.1]: https://github.com/ytiurin/hyphen/compare/v1.9.0...v1.9.1
[v1.9.0]: https://github.com/ytiurin/hyphen/compare/v1.8.0...v1.9.0
[v1.8.0]: https://github.com/ytiurin/hyphen/compare/v1.7.2...v1.8.0
[v1.7.2]: https://github.com/ytiurin/hyphen/compare/v1.7.1...v1.7.2
[v1.7.1]: https://github.com/ytiurin/hyphen/compare/v1.7.0...v1.7.1
[v1.7.0]: https://github.com/ytiurin/hyphen/compare/v1.6.6...v1.7.0
[v1.6.6]: https://github.com/ytiurin/hyphen/compare/v1.6.5...v1.6.6
[v1.6.5]: https://github.com/ytiurin/hyphen/compare/v1.6.4...v1.6.5
[v1.6.4]: https://github.com/ytiurin/hyphen/compare/v1.6.3...v1.6.4
[v1.6.3]: https://github.com/ytiurin/hyphen/compare/v1.6.2...v1.6.3
[v1.6.2]: https://github.com/ytiurin/hyphen/compare/v1.6.1...v1.6.2
[v1.6.1]: https://github.com/ytiurin/hyphen/compare/v1.6.0...v1.6.1
[v1.6.0]: https://github.com/ytiurin/hyphen/compare/v1.5.1...v1.6.0
[v1.5.1]: https://github.com/ytiurin/hyphen/compare/v1.5.0...v1.5.1
[v1.5.0]: https://github.com/ytiurin/hyphen/compare/v1.3.1...v1.5.0
[v1.3.1]: https://github.com/ytiurin/hyphen/compare/v1.3.0...v1.3.1
[v1.3.0]: https://github.com/ytiurin/hyphen/compare/v1.2.1...v1.3.0
[v1.2.1]: https://github.com/ytiurin/hyphen/compare/v1.2.0...v1.2.1
[v1.2.0]: https://github.com/ytiurin/hyphen/compare/v1.1.1...v1.2.0
[v1.1.1]: https://github.com/ytiurin/hyphen/compare/v1.1.0...v1.1.1
[v1.1.0]: https://github.com/ytiurin/hyphen/compare/1.0.2...v1.1.0
[1.0.2]: https://github.com/ytiurin/hyphen/compare/1.0.1...1.0.2
[1.0.1]: https://github.com/ytiurin/hyphen/compare/1.0.0...1.0.1
[1.0.0]: https://github.com/ytiurin/hyphen/releases/tag/1.0.0
