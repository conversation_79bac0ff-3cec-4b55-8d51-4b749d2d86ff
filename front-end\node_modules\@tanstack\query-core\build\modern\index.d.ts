export { aV as CancelOptions, C as CancelledError, E as DataTag, z as DefaultError, aU as DefaultOptions, a9 as DefaultedInfiniteQueryObserverOptions, a7 as DefaultedQueryObserverOptions, aC as DefinedInfiniteQueryObserverResult, au as DefinedQueryObserverResult, D as DehydrateOptions, x as DehydratedState, I as Enabled, ac as EnsureInfiniteQueryDataOptions, ab as EnsureQueryDataOptions, ad as FetchInfiniteQueryOptions, ak as FetchNextPageOptions, al as FetchPreviousPageOptions, aa as FetchQueryOptions, an as FetchStatus, X as GetNextPageParamFunction, W as GetPreviousPageParamFunction, H as HydrateOptions, Y as InfiniteData, aw as InfiniteQueryObserverBaseResult, az as InfiniteQueryObserverLoadingErrorResult, ay as InfiniteQueryObserverLoadingResult, a8 as InfiniteQueryObserverOptions, ax as InfiniteQueryObserverPendingResult, aA as InfiniteQueryObserverRefetchErrorResult, aD as InfiniteQueryObserverResult, aB as InfiniteQueryObserverSuccessResult, a2 as InfiniteQueryPageParamsOptions, L as InitialDataFunction, a1 as InitialPageParam, ai as InvalidateOptions, ag as InvalidateQueryFilters, aM as MutateFunction, aL as MutateOptions, w as Mutation, M as MutationCache, d as MutationCacheNotifyEvent, g as MutationFilters, aI as MutationFunction, aE as MutationKey, aH as MutationMeta, e as MutationObserver, aN as MutationObserverBaseResult, aQ as MutationObserverErrorResult, aO as MutationObserverIdleResult, aP as MutationObserverLoadingResult, aK as MutationObserverOptions, aS as MutationObserverResult, aR as MutationObserverSuccessResult, aJ as MutationOptions, aG as MutationScope, v as MutationState, aF as MutationStatus, _ as NetworkMode, N as NoInfer, aY as NotifyEvent, aX as NotifyEventType, $ as NotifyOnChangeProps, O as OmitKeyof, a6 as Optional, y as Override, P as PlaceholderDataFunction, T as QueriesPlaceholderDataFunction, u as Query, Q as QueryCache, a as QueryCacheNotifyEvent, b as QueryClient, aT as QueryClientConfig, j as QueryFilters, F as QueryFunction, K as QueryFunctionContext, A as QueryKey, V as QueryKeyHashFunction, Z as QueryMeta, c as QueryObserver, ao as QueryObserverBaseResult, ar as QueryObserverLoadingErrorResult, aq as QueryObserverLoadingResult, a4 as QueryObserverOptions, ap as QueryObserverPendingResult, as as QueryObserverRefetchErrorResult, av as QueryObserverResult, at as QueryObserverSuccessResult, a0 as QueryOptions, J as QueryPersister, t as QueryState, am as QueryStatus, af as RefetchOptions, ah as RefetchQueryFilters, R as Register, aj as ResetOptions, ae as ResultOptions, aW as SetDataOptions, S as SkipToken, G as StaleTime, a3 as ThrowOnError, U as Updater, a5 as WithRequired, B as dataTagSymbol, q as defaultShouldDehydrateMutation, p as defaultShouldDehydrateQuery, n as dehydrate, h as hashKey, o as hydrate, l as isCancelledError, i as isServer, k as keepPreviousData, f as matchMutation, m as matchQuery, r as replaceEqualDeep, s as skipToken } from './hydration-mKPlgzt9.js';
export { QueriesObserver, QueriesObserverOptions } from './queriesObserver.js';
export { InfiniteQueryObserver } from './infiniteQueryObserver.js';
export { notifyManager } from './notifyManager.js';
export { focusManager } from './focusManager.js';
export { onlineManager } from './onlineManager.js';
import './removable.js';
import './subscribable.js';
