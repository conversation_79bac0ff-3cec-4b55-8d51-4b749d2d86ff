"use strict";
exports.isThursday = isThursday;
var _index = require("./toDate.cjs");

/**
 * The {@link isThursday} function options.
 */

/**
 * @name isThursday
 * @category Weekday Helpers
 * @summary Is the given date Thursday?
 *
 * @description
 * Is the given date Thursday?
 *
 * @param date - The date to check
 * @param options - An object with options
 *
 * @returns The date is Thursday
 *
 * @example
 * // Is 25 September 2014 Thursday?
 * const result = isThursday(new Date(2014, 8, 25))
 * //=> true
 */
function isThursday(date, options) {
  return (0, _index.toDate)(date, options?.in).getDay() === 4;
}
