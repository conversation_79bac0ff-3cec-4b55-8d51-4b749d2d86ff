{"version": 3, "file": "parser.js", "sourceRoot": "", "sources": ["../src/parser.ts"], "names": [], "mappings": ";;;;;AAoRE,sBAAK;AACL,4DAAwB;AAExB,0EAA+B;AAC/B,8CAAiB;AACjB,gFAAkC;AAzRpC,kDAA0B;AAG1B,mDAA+C;AAC/C,uCAAyC;AACzC,kFAA+E;AAC/E,gFAA6E;AAC7E,wEAG2C;AAC3C,8FAA2F;AAE3F,8EAG8C;AAC9C,iEAA8D;AAO9D,6EAA0E;AAC1E,iFAAkF;AAElF,iFAA8E;AAE9E,MAAM,GAAG,GAAG,IAAA,eAAK,EAAC,4CAA4C,CAAC,CAAC;AAEhE;;;;GAIG;AACH,MAAM,gBAAgB,GAAG,IAAI,GAAG,EAA6B,CAAC;AAC9D,SAAS,iBAAiB;IACxB,gBAAgB,CAAC,KAAK,EAAE,CAAC;AAC3B,CAAC;AAED,MAAM,0BAA0B,GAAG,IAAI,GAAG,EAAU,CAAC;AACrD,SAAS,+BAA+B;IACtC,0BAA0B,CAAC,KAAK,EAAE,CAAC;AACrC,CAAC;AAED;;;;GAIG;AACH,SAAS,gBAAgB,CACvB,aAA4B,EAC5B,sBAA+B;IAE/B,IAAI,aAAa,CAAC,cAAc,EAAE,CAAC;QACjC,MAAM,kBAAkB,GAAG,IAAA,2DAA4B,EACrD,aAAa,CAAC,cAAc,EAC5B,aAAa,EACb,sBAAsB,EACtB,0BAA0B,CAC3B,CAAC;QACF,IAAI,kBAAkB,EAAE,CAAC;YACvB,OAAO,kBAAkB,CAAC;QAC5B,CAAC;IACH,CAAC;IAED,IAAI,aAAa,CAAC,QAAQ,EAAE,CAAC;QAC3B,MAAM,oBAAoB,GAAG,IAAA,yCAAmB,EAC9C,aAAa,CAAC,QAAQ,EACtB,aAAa,CACd,CAAC;QACF,IAAI,oBAAoB,EAAE,CAAC;YACzB,OAAO,oBAAoB,CAAC;QAC9B,CAAC;IACH,CAAC;IAED,qFAAqF;IACrF,6DAA6D;IAC7D,IAAI,CAAC,sBAAsB,EAAE,CAAC;QAC5B,OAAO,IAAA,kCAAe,EAAC,aAAa,CAAC,CAAC;IACxC,CAAC;IAED,OAAO,IAAA,2CAAoB,EACzB,aAAa,EACb,IAAA,yDAA2B,EAAC,aAAa,CAAC,CAC3C,CAAC;AACJ,CAAC;AAiBD,SAAS,KAAK,CACZ,IAAY,EACZ,OAAW;IAEX,MAAM,EAAE,GAAG,EAAE,GAAG,yBAAyB,CAAC,IAAI,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;IAChE,OAAO,GAAG,CAAC;AACb,CAAC;AAED,SAAS,yBAAyB,CAChC,IAA4B,EAC5B,OAAsB,EACtB,sBAA+B;IAE/B;;OAEG;IACH,MAAM,aAAa,GAAG,IAAA,yCAAmB,EAAC,IAAI,EAAE,OAAO,CAAC,CAAC;IAEzD;;OAEG;IACH,IAAI,OAAO,EAAE,2CAA2C,EAAE,CAAC;QACzD,MAAM,IAAI,KAAK,CACb,gGAAgG,CACjG,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,MAAM,GAAG,GAAG,IAAA,mCAAgB,EAAC,aAAa,CAAC,CAAC;IAE5C;;OAEG;IACH,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG,IAAA,4BAAY,EACtC,GAAG,EACH,aAAa,EACb,sBAAsB,CACvB,CAAC;IAEF,OAAO;QACL,GAAG,EAAE,MAAgB;QACrB,qBAAqB,EAAE,OAAO,CAAC,qBAAqB;QACpD,qBAAqB,EAAE,OAAO,CAAC,qBAAqB;KACrD,CAAC;AACJ,CAAC;AAED,IAAI,6BAA6B,GAA2B,EAAE,CAAC;AAC/D,mFAAmF;AACnF,SAAS,kCAAkC;IACzC,6BAA6B,GAAG,EAAE,CAAC;AACrC,CAAC;AAED,SAAS,wBAAwB,CAC/B,IAA4B,EAC5B,eAAkB;IAElB;;OAEG;IACH,MAAM,aAAa,GAAG,IAAA,yCAAmB,EAAC,IAAI,EAAE,eAAe,CAAC,CAAC;IAEjE;;;;OAIG;IACH,IACE,aAAa,CAAC,SAAS;QACvB,CAAC,aAAa,CAAC,QAAQ;QACvB,aAAa,CAAC,QAAQ,CAAC,IAAI,GAAG,CAAC,EAC/B,CAAC;QACD,aAAa,CAAC,QAAQ,GAAG;YACvB,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC;gBAChB,KAAK,MAAM,UAAU,IAAI,aAAa,CAAC,QAAQ,EAAE,CAAC;oBAChD,MAAM,eAAe,GAAG,gBAAgB,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC5D,IAAI,eAAe,EAAE,CAAC;wBACpB,MAAM,eAAe,CAAC;oBACxB,CAAC;yBAAM,CAAC;wBACN,GAAG,CACD,oFAAoF,EACpF,UAAU,CACX,CAAC;wBACF,MAAM,UAAU,GAAG,IAAA,iDAA2B,EAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;wBAC9D,gBAAgB,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC;wBAChD,MAAM,UAAU,CAAC;oBACnB,CAAC;gBACH,CAAC;YACH,CAAC;SACF,CAAC;IACJ,CAAC;IAED,MAAM,sBAAsB,GAC1B,aAAa,CAAC,QAAQ,IAAI,IAAI;QAC9B,aAAa,CAAC,QAAQ,CAAC,IAAI,GAAG,CAAC;QAC/B,CAAC,CAAC,aAAa,CAAC,cAAc,CAAC;IAEjC,IACE,OAAO,eAAe,CAAC,2CAA2C;QAChE,SAAS;QACX,eAAe,CAAC,2CAA2C,EAC3D,CAAC;QACD,aAAa,CAAC,2CAA2C,GAAG,IAAI,CAAC;IACnE,CAAC;IAED,IACE,aAAa,CAAC,2CAA2C;QACzD,CAAC,sBAAsB,EACvB,CAAC;QACD,MAAM,IAAI,KAAK,CACb,sEAAsE,CACvE,CAAC;IACJ,CAAC;IAED;;;;;;;OAOG;IACH,IAAI,aAAa,CAAC,SAAS,IAAI,eAAe,CAAC,QAAQ,EAAE,CAAC;QACxD,6BAA6B,CAAC,eAAe,CAAC,QAAQ,CAAC;YACrD,CAAC,6BAA6B,CAAC,eAAe,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;IACvE,CAAC;IAED,MAAM,EAAE,GAAG,EAAE,OAAO,EAAE,GACpB,aAAa,CAAC,SAAS;QACvB,eAAe,CAAC,QAAQ;QACxB,6BAA6B,CAAC,eAAe,CAAC,QAAQ,CAAC,GAAG,CAAC;QACzD,CAAC,CAAC,IAAA,6CAAqB,EAAC,aAAa,CAAC;QACtC,CAAC,CAAC,gBAAgB,CAAC,aAAa,EAAE,sBAAsB,CAAC,CAAC;IAE9D;;;OAGG;IACH,MAAM,sBAAsB,GAC1B,OAAO,aAAa,CAAC,gBAAgB,KAAK,SAAS;QACjD,CAAC,CAAC,aAAa,CAAC,gBAAgB;QAChC,CAAC,CAAC,IAAI,CAAC;IAEX,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG,IAAA,4BAAY,EACtC,GAAG,EACH,aAAa,EACb,sBAAsB,CACvB,CAAC;IAEF;;;OAGG;IACH,IAAI,OAAO,IAAI,aAAa,CAAC,2CAA2C,EAAE,CAAC;QACzE,MAAM,KAAK,GAAG,IAAA,+DAAgC,EAAC,OAAO,EAAE,GAAG,CAAC,CAAC;QAC7D,IAAI,KAAK,EAAE,CAAC;YACV,MAAM,IAAA,sBAAY,EAAC,KAAK,CAAC,CAAC;QAC5B,CAAC;IACH,CAAC;IAED;;OAEG;IACH,OAAO;QACL,GAAG,EAAE,MAAgB;QACrB,QAAQ,EAAE,IAAA,2CAAoB,EAAC,OAAO,EAAE,OAAO,CAAC;KACjD,CAAC;AACJ,CAAC"}