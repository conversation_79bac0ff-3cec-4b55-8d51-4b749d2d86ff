# @react-pdf/types

## 2.9.0

### Minor Changes

- [#3112](https://github.com/diegomura/react-pdf/pull/3112) [`f89f75c1`](https://github.com/diegomura/react-pdf/commit/f89f75c1f132ba19b54847c3ac23efec675f8d0a) Thanks [@diegomura](https://github.com/diegomura)! - feat: add xLinkHref, gradientTransform and gradientUnits support

### Patch Changes

- Updated dependencies [[`f89f75c1`](https://github.com/diegomura/react-pdf/commit/f89f75c1f132ba19b54847c3ac23efec675f8d0a)]:
  - @react-pdf/stylesheet@6.1.0
  - @react-pdf/font@4.0.2

## 2.8.2

### Patch Changes

- Updated dependencies [[`e257f7ee`](https://github.com/diegomura/react-pdf/commit/e257f7ee18ff09f4895cd16c0b21b824678384c8)]:
  - @react-pdf/font@4.0.1
  - @react-pdf/stylesheet@6.0.2

## 2.8.1

### Patch Changes

- [#3087](https://github.com/diegomura/react-pdf/pull/3087) [`7cd66e4f`](https://github.com/diegomura/react-pdf/commit/7cd66e4fc37cd1393adc6250a919fe2629812082) Thanks [@diegomura](https://github.com/diegomura)! - fix: font exported types

- Updated dependencies [[`2a4f1bfc`](https://github.com/diegomura/react-pdf/commit/2a4f1bfca6b84e6c6bbde683447ce8079a1febbe), [`7cd66e4f`](https://github.com/diegomura/react-pdf/commit/7cd66e4fc37cd1393adc6250a919fe2629812082), [`bfb51ec0`](https://github.com/diegomura/react-pdf/commit/bfb51ec09b851c52659ce16fed1286173e9516a9), [`a2de2685`](https://github.com/diegomura/react-pdf/commit/a2de2685b25c797f266775c2e35a6535090393b7), [`24690f52`](https://github.com/diegomura/react-pdf/commit/24690f5238e4eacf28351cf9996856a7d196d29e), [`481b536f`](https://github.com/diegomura/react-pdf/commit/481b536f4ad145fb227829399b85a35838a506f8)]:
  - @react-pdf/stylesheet@6.0.1
  - @react-pdf/font@4.0.0

## 2.8.0

### Minor Changes

- [#3082](https://github.com/diegomura/react-pdf/pull/3082) [`24fe4bf8`](https://github.com/diegomura/react-pdf/commit/24fe4bf894fff055121926488b30d0bf212a9c45) Thanks [@diegomura](https://github.com/diegomura)! - feat: rework and type stylesheet package

- [#3079](https://github.com/diegomura/react-pdf/pull/3079) [`700535c5`](https://github.com/diegomura/react-pdf/commit/700535c57ff1b105d923be70f4fc4bfdf4479f91) Thanks [@diegomura](https://github.com/diegomura)! - refactor: convert font package to TS

### Patch Changes

- [#3067](https://github.com/diegomura/react-pdf/pull/3067) [`96c2464d`](https://github.com/diegomura/react-pdf/commit/96c2464dfaa7294e0d79b7ed64743bfd7b1a8c72) Thanks [@diegomura](https://github.com/diegomura)! - refactor: convert primitives package to TS

- Updated dependencies [[`96c2464d`](https://github.com/diegomura/react-pdf/commit/96c2464dfaa7294e0d79b7ed64743bfd7b1a8c72), [`24fe4bf8`](https://github.com/diegomura/react-pdf/commit/24fe4bf894fff055121926488b30d0bf212a9c45), [`700535c5`](https://github.com/diegomura/react-pdf/commit/700535c57ff1b105d923be70f4fc4bfdf4479f91)]:
  - @react-pdf/primitives@4.1.1
  - @react-pdf/stylesheet@6.0.0
  - @react-pdf/font@3.1.0

## 2.7.1

### Patch Changes

- [#3019](https://github.com/diegomura/react-pdf/pull/3019) [`01944231`](https://github.com/diegomura/react-pdf/commit/01944231a342d502b832aeecb4c313020b8360c8) Thanks [@wojtekmaj](https://github.com/wojtekmaj)! - Fixed HyphenationCallback type (confusing argument name, removed extra argument)

## 2.7.0

### Minor Changes

- [#2875](https://github.com/diegomura/react-pdf/pull/2875) [`46c3047d`](https://github.com/diegomura/react-pdf/commit/46c3047de56ae82f062b72c4910a4e6096eee99f) Thanks [@diegomura](https://github.com/diegomura)! - feat: add position: static support

- [#2874](https://github.com/diegomura/react-pdf/pull/2874) [`55973278`](https://github.com/diegomura/react-pdf/commit/55973278ac8bc8f703b63844f57d6f155ae8d86f) Thanks [@diegomura](https://github.com/diegomura)! - feat: add align-content: space-evenly support

## 2.6.0

### Minor Changes

- [#2771](https://github.com/diegomura/react-pdf/pull/2771) [`8e6a832`](https://github.com/diegomura/react-pdf/commit/8e6a8320f86354aff950c296a96bc41a33e9dab2) Thanks [@nikischin](https://github.com/nikischin)! - fix: fix dpi

### Patch Changes

- [#2727](https://github.com/diegomura/react-pdf/pull/2727) [`4bafab8`](https://github.com/diegomura/react-pdf/commit/4bafab8455c9003759f48bad20a720baf4ed189b) Thanks [@hendrikmolder](https://github.com/hendrikmolder)! - accept string array for `fontFamily` in stylesheets

## 2.5.0

### Minor Changes

- [#2723](https://github.com/diegomura/react-pdf/pull/2723) [`22a34a9`](https://github.com/diegomura/react-pdf/commit/22a34a91b16a201cd8288e0dbea9368b12ca73f5) Thanks [@hendrikmolder](https://github.com/hendrikmolder)! - Allow passing a string array to fontFamily

## 2.4.1

### Patch Changes

- [#2573](https://github.com/diegomura/react-pdf/pull/2573) [`9af07fe`](https://github.com/diegomura/react-pdf/commit/9af07feb59c2fe9c1d8960ac95f6fa6e03d16235) Thanks [@davbrito](https://github.com/davbrito)! - feat: add support for resolving blob images

## 2.4.0

### Minor Changes

- [#2539](https://github.com/diegomura/react-pdf/pull/2539) [`fb5273d`](https://github.com/diegomura/react-pdf/commit/fb5273d8d80d919f7b9c214e02d67b79ce23fa19) Thanks [@diegomura](https://github.com/diegomura)! - feat: add creation and modification dates

## 2.3.6

### Patch Changes

- [#2525](https://github.com/diegomura/react-pdf/pull/2525) [`9e5842b`](https://github.com/diegomura/react-pdf/commit/9e5842bbecca6e249af2c5fc50078bb7ddd5420f) Thanks [@wojtekmaj](https://github.com/wojtekmaj)! - Fix rowGap and columnGap style properties not accepting strings

## 2.3.5

### Patch Changes

- [#2453](https://github.com/diegomura/react-pdf/pull/2453) [`e5c8fde`](https://github.com/diegomura/react-pdf/commit/e5c8fde9379a9a85ecac7e3d6273953e39d65f8d) Thanks [@chuckspencer-aiq](https://github.com/chuckspencer-aiq)! - fix: add clipPath to typing for SVGPresentationAttributes

* [#2467](https://github.com/diegomura/react-pdf/pull/2467) [`1f987cc`](https://github.com/diegomura/react-pdf/commit/1f987cc27c3fd1ef1b6748ebe58a289a78b538d2) Thanks [@JaeSeoKim](https://github.com/JaeSeoKim)! - feat: add withVariationSelectors option to registerEmojiSource [#2466](https://github.com/diegomura/react-pdf/issues/2466)

- [#2416](https://github.com/diegomura/react-pdf/pull/2416) [`4c40b14`](https://github.com/diegomura/react-pdf/commit/4c40b149cfed42f2513e1dd330a92ccc3363c04f) Thanks [@TheMikeyRoss](https://github.com/TheMikeyRoss)! - Updated svg.d.ts to include strokeLinejoin

## 2.3.4

### Patch Changes

- [#2130](https://github.com/diegomura/react-pdf/pull/2130) [`4a55c1b`](https://github.com/diegomura/react-pdf/commit/4a55c1b2ed19e460ccae6e749ed94c16729a23c4) Thanks [@PoornakumarRasiraju](https://github.com/PoornakumarRasiraju)! - fix: allow credentials option in Image

## 2.3.3

### Patch Changes

- [#2276](https://github.com/diegomura/react-pdf/pull/2276) [`1e1fbdc`](https://github.com/diegomura/react-pdf/commit/****************************************) Thanks [@renchap](https://github.com/renchap)! - add `aspectRatio` style property

* [#2293](https://github.com/diegomura/react-pdf/pull/2293) [`8636812`](https://github.com/diegomura/react-pdf/commit/86368122ed87621d19ae3bc248080e17703d9fcb) Thanks [@BryanAbate](https://github.com/BryanAbate)! - fix type for `getRegisteredFonts` method

## 2.3.2

### Patch Changes

- [#2310](https://github.com/diegomura/react-pdf/pull/2310) [`a25dbcb`](https://github.com/diegomura/react-pdf/commit/a25dbcb32b65c300f5b088e8b210bb0c1abca5c2) Thanks [@wojtekmaj](https://github.com/wojtekmaj)! - Fixed all strings allowed in `fontWeight` in `Font.register`

## 2.3.1

### Patch Changes

- [#2252](https://github.com/diegomura/react-pdf/pull/2252) [`47e91cb`](https://github.com/diegomura/react-pdf/commit/47e91cbd8016046bb4e8389ba0d1c7ede9edce59) Thanks [@jeetiss](https://github.com/jeetiss)! - implement function as emoji source to build more complex urls

## 2.3.0

### Minor Changes

- [#2214](https://github.com/diegomura/react-pdf/pull/2214) [`2db67a3`](https://github.com/diegomura/react-pdf/commit/2db67a38b9be98b7816a2b5aa4733446b95e3724) Thanks [@KODIKAS-NL](https://github.com/KODIKAS-NL)! - added base support for verticalAlign "super" and "sub"

## 2.2.0

### Minor Changes

- [#2160](https://github.com/diegomura/react-pdf/pull/2160) [`a743c90`](https://github.com/diegomura/react-pdf/commit/a743c905fb5d201d2382bc9175fa36b83cc47284) Thanks [@jeetiss](https://github.com/jeetiss)! - implement flex gap

## 2.1.1

### Patch Changes

- [#2106](https://github.com/diegomura/react-pdf/pull/2106) [`24bb5de`](https://github.com/diegomura/react-pdf/commit/24bb5de969a854cc0226438985b34ef8ae2d7581) Thanks [@RDO34](https://github.com/RDO34)! - Add `dpi` and `bookmark` page prop types

## 2.1.0

### Minor Changes

- [#1862](https://github.com/diegomura/react-pdf/pull/1862) [`1411d16`](https://github.com/diegomura/react-pdf/commit/1411d162e04ca237bad93729695c363fdf4bdbeb) Thanks [@diegomura](https://github.com/diegomura)! - feat: bookmarks support

* [#1867](https://github.com/diegomura/react-pdf/pull/1867) [`4fadb48`](https://github.com/diegomura/react-pdf/commit/4fadb48983d7269452f89f80c7e341ece859aaee) Thanks [@diegomura](https://github.com/diegomura)! - feat: add page layout support

- [#1868](https://github.com/diegomura/react-pdf/pull/1868) [`ce8762f`](https://github.com/diegomura/react-pdf/commit/ce8762f6de5c796e69ec5a225c7f3ff9c619a960) Thanks [@diegomura](https://github.com/diegomura)! - feat: add page mode support

* [#1869](https://github.com/diegomura/react-pdf/pull/1869) [`5d2c308`](https://github.com/diegomura/react-pdf/commit/5d2c3088cf438a8abf1038b14a21117fecf59d57) Thanks [@diegomura](https://github.com/diegomura)! - feat: variable dpi

## 2.0.9

### Patch Changes

- [#1843](https://github.com/diegomura/react-pdf/pull/1843) [`fe0f214`](https://github.com/diegomura/react-pdf/commit/fe0f214dbbf2f632b852ebfe65f886ecc4dd6953) Thanks [@diegomura](https://github.com/diegomura)! - fix: add ID1 type

## 2.0.8

### Patch Changes

- [#1581](https://github.com/diegomura/react-pdf/pull/1581) [`04449ab`](https://github.com/diegomura/react-pdf/commit/04449ab352db0cca2155024dd3e8c690e42193ca) Thanks [@jeetiss](https://github.com/jeetiss)! - added changelog with changesets
