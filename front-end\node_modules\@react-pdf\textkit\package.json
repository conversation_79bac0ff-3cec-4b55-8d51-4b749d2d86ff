{"name": "@react-pdf/textkit", "license": "MIT", "version": "6.0.0", "description": "An advanced text layout framework", "type": "module", "main": "./lib/textkit.js", "types": "./lib/index.d.ts", "repository": {"type": "git", "url": "https://github.com/diegomura/react-pdf.git", "directory": "packages/textkit"}, "contributors": ["<PERSON> Go<PERSON>t <<EMAIL>>", "<PERSON> <<EMAIL>>"], "scripts": {"test": "vitest", "build": "rimraf ./lib && rollup -c", "watch": "rimraf ./lib && rollup -c -w", "typecheck": "tsc --noEmit"}, "files": ["lib"], "dependencies": {"@react-pdf/fns": "3.1.2", "bidi-js": "^1.0.2", "hyphen": "^1.6.4", "unicode-properties": "^1.4.1"}, "devDependencies": {"@types/fontkit": "^2.0.7"}}