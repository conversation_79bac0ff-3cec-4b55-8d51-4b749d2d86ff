{"name": "@react-pdf/pdfkit", "version": "4.0.3", "license": "MIT", "description": "A PDF generation library for Node.js", "type": "module", "main": "./lib/pdfkit.js", "browser": {"./lib/pdfkit.js": "./lib/pdfkit.browser.js"}, "repository": {"type": "git", "url": "https://github.com/diegomura/react-pdf.git", "directory": "packages/pdfkit"}, "author": {"name": "Devon Govett", "email": "<EMAIL>", "url": "http://badassjs.com/"}, "scripts": {"clear": "rimraf ./lib ./src/font/data/*.json", "parse:afm": "node ./src/font/data/compressData.js", "build": "npm run clear && npm run parse:afm && rollup -c ", "watch": "npm run clear && npm run parse:afm && rollup -c -w"}, "files": ["lib"], "dependencies": {"@babel/runtime": "^7.20.13", "@react-pdf/png-js": "^3.0.0", "browserify-zlib": "^0.2.0", "crypto-js": "^4.2.0", "fontkit": "^2.0.2", "jay-peg": "^1.1.1", "linebreak": "^1.1.0", "vite-compatible-readable-stream": "^3.6.1"}, "devDependencies": {"iconv-lite": "^0.4.13"}}