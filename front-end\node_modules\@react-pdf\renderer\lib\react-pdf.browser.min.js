import*as e from"@react-pdf/primitives";export*from"@react-pdf/primitives";import r from"queue";import{useRef as o,useState as t,useEffect as n,useCallback as i}from"react";import a from"@react-pdf/font";import l from"@react-pdf/render";import c from"@react-pdf/pdfkit";import d from"@react-pdf/layout";import{upperFirst as s}from"@react-pdf/fns";import u from"@react-pdf/reconciler";import{jsx as p}from"react/jsx-runtime";const m=(e,r)=>{let{style:o,children:t,...n}=r;return{type:e,box:{},style:o||{},props:n||{},children:[]}},f=e=>({type:"TEXT_INSTANCE",value:e}),v=(e,r)=>{const o="TEXT"===e.type||"LINK"===e.type||"TSPAN"===e.type||"NOTE"===e.type;"TEXT_INSTANCE"!==r.type||o?e.children.push(r):console.warn(`Invalid '${r.value}' string child outside <Text> component`)},h=(e,r)=>{"ROOT"===e.type?e.document=r:v(e,r)},y=(e,r,o)=>{var t;const n=null===(t=e.children)||void 0===t?void 0:t.indexOf(o);void 0!==n&&-1!==n&&r&&e.children.splice(n,0,r)},T=(e,r)=>{var o;const t=null===(o=e.children)||void 0===o?void 0:o.indexOf(r);void 0!==t&&-1!==t&&e.children.splice(t,1)},b=(e,r)=>{var o;const t=null===(o=e.children)||void 0===o?void 0:o.indexOf(r);void 0!==t&&-1!==t&&e.children.splice(t,1)},w=(e,r,o)=>{e.value=o},g=(e,r,o,t,n)=>{const{style:i,...a}=n;e.props=a,e.style=i},C=e=>{let{onChange:r=(()=>{})}=e;return u({appendChild:v,appendChildToContainer:h,commitTextUpdate:w,commitUpdate:g,createInstance:m,createTextInstance:f,insertBefore:y,removeChild:T,removeChildFromContainer:b,resetAfterCommit:r})};var S={version:"4.3.0"};const{version:N}=S,_=new a;let A;const O={},L=e=>{const r={type:"ROOT",document:null};A=A||C({onChange:()=>{var e;const r=(null===(e=O.change)||void 0===e?void 0:e.slice())||[];for(let e=0;e<r.length;e+=1)r[e]()}});const o=A.createContainer(r),t=(e,r)=>{A.updateContainer(e,o,null,r)};e&&t(e);const n=async function(e){void 0===e&&(e=!0);const o=r.document.props||{},{pdfVersion:t,language:n,pageLayout:i,pageMode:a,title:u,author:p,subject:m,keyboards:f,creator:v="react-pdf",producer:h="react-pdf",creationDate:y=new Date,modificationDate:T}=o,b=new c({compress:e,pdfVersion:t,lang:n,displayTitle:!0,autoFirstPage:!1,info:(w={Title:u,Author:p,Subject:m,Keywords:f,Creator:v,Producer:h,CreationDate:y,ModificationDate:T},Object.fromEntries(Object.entries(w).filter((e=>{let[,r]=e;return void 0!==r}))))});var w;i&&(b._root.data.PageLayout=s(i)),a&&(b._root.data.PageMode=s(a));const g=await d(r.document,_);return{layout:g,fileStream:l(b,g)}},i=function(e){void 0===e&&(e={}),r.document.props.onRender&&r.document.props.onRender(e)};return{on:(e,r)=>{O[e]||(O[e]=[]),O[e].push(r)},container:r,toBlob:async()=>{const e=[],{layout:r,fileStream:o}=await n();return new Promise(((t,n)=>{o.on("data",(r=>{e.push(r instanceof Uint8Array?r:new Uint8Array(r))})),o.on("end",(()=>{try{const o=new Blob(e,{type:"application/pdf"});i({blob:o,_INTERNAL__LAYOUT__DATA_:r}),t(o)}catch(e){n(e)}}))}))},toBuffer:async()=>{const{layout:e,fileStream:r}=await n();return i({_INTERNAL__LAYOUT__DATA_:e}),r},toString:async()=>{"development"===process.env.NODE_ENV&&console.warn("`toString` is deprecated and will be removed in next major release");let e="";const{fileStream:r}=await n(!1);return new Promise(((o,t)=>{try{r.on("data",(r=>{e+=r})),r.on("end",(()=>{i(),o(e)}))}catch(e){t(e)}}))},removeListener:(e,r)=>{if(!O[e])return;const o=O[e].indexOf(r);o>-1&&O[e].splice(o,1)},updateContainer:t}},P=_,D={create:e=>e},x=function(e){let{document:a}=void 0===e?{}:e;const l=o(null),[c,d]=t({url:null,blob:null,error:null,loading:!!a});n((()=>{const e=r({autostart:!0,concurrency:1}),o=()=>{d((e=>({...e,loading:!0}))),e.splice(0,e.length,(()=>c.error?Promise.resolve():l.current.toBlob()))};return l.current=L(),l.current.on("change",o),a&&l.current.updateContainer(a),e.on("error",(e=>{console.error(e),d((r=>({...r,loading:!1,error:e})))})),e.on("success",(e=>{d({blob:e,error:null,loading:!1,url:URL.createObjectURL(e)})})),()=>{e.end(),l.current.removeListener("change",o)}}),[]),n((()=>()=>{c.url&&URL.revokeObjectURL(c.url)}),[c.url]);const s=i((e=>{l.current.updateContainer(e)}),[]);return[c,s]},E=e=>{let{title:r,style:o,className:t,children:i,innerRef:a,showToolbar:l=!0,...c}=e;const[d,s]=x();n((()=>s(i)),[i]);const u=d.url?`${d.url}#toolbar=${l?1:0}`:null;return p("iframe",{src:u,title:r,ref:a,style:o,className:t,...c})},R=e=>{let{document:r,children:o}=e;const[t,i]=x();return n((()=>i(r)),[r]),r?o(t):(console.warn("You should pass a valid document to BlobProvider"),null)},B=e=>{let{fileName:r="document.pdf",document:o,children:t,onClick:i,href:a,...l}=e;const[c,d]=x();if(n((()=>d(o)),[o]),!o)return console.warn("You should pass a valid document to PDFDownloadLink"),null;return p("a",{href:c.url,download:r,onClick:e=>{c&&window.navigator.msSaveBlob&&window.navigator.msSaveBlob(c.blob,r),"function"==typeof i&&i(e,c)},...l,children:"function"==typeof t?t(c):t})},U=e=>{throw new Error(`${e} is a Node specific API. You're either using this method in a browser, or your bundler is not loading react-pdf from the appropriate web build.`)},F=()=>{U("renderToStream")},I=()=>{U("renderToBuffer")},j=()=>{U("renderToString")},k=()=>{U("renderToFile")},Y=()=>{U("render")};var V={pdf:L,usePDF:x,Font:P,version:N,StyleSheet:D,PDFViewer:E,BlobProvider:R,PDFDownloadLink:B,renderToStream:F,renderToString:j,renderToFile:k,render:Y,...e};export{R as BlobProvider,P as Font,B as PDFDownloadLink,E as PDFViewer,D as StyleSheet,C as createRenderer,V as default,L as pdf,Y as render,I as renderToBuffer,k as renderToFile,F as renderToStream,j as renderToString,x as usePDF,N as version};
