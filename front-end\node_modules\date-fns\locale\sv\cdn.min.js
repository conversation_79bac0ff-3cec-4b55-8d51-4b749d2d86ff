var q=function(G){return q=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(C){return typeof C}:function(C){return C&&typeof Symbol=="function"&&C.constructor===Symbol&&C!==Symbol.prototype?"symbol":typeof C},q(G)},D=function(G,C){var J=Object.keys(G);if(Object.getOwnPropertySymbols){var Y=Object.getOwnPropertySymbols(G);C&&(Y=Y.filter(function(N){return Object.getOwnPropertyDescriptor(G,N).enumerable})),J.push.apply(J,Y)}return J},K=function(G){for(var C=1;C<arguments.length;C++){var J=arguments[C]!=null?arguments[C]:{};C%2?D(Object(J),!0).forEach(function(Y){xE(G,Y,J[Y])}):Object.getOwnPropertyDescriptors?Object.defineProperties(G,Object.getOwnPropertyDescriptors(J)):D(Object(J)).forEach(function(Y){Object.defineProperty(G,Y,Object.getOwnPropertyDescriptor(J,Y))})}return G},xE=function(G,C,J){if(C=XE(C),C in G)Object.defineProperty(G,C,{value:J,enumerable:!0,configurable:!0,writable:!0});else G[C]=J;return G},XE=function(G){var C=BE(G,"string");return q(C)=="symbol"?C:String(C)},BE=function(G,C){if(q(G)!="object"||!G)return G;var J=G[Symbol.toPrimitive];if(J!==void 0){var Y=J.call(G,C||"default");if(q(Y)!="object")return Y;throw new TypeError("@@toPrimitive must return a primitive value.")}return(C==="string"?String:Number)(G)};(function(G){var C=Object.defineProperty,J=function E(B,x){for(var X in x)C(B,X,{get:x[X],enumerable:!0,configurable:!0,set:function H(U){return x[X]=function(){return U}}})},Y={lessThanXSeconds:{one:"mindre \xE4n en sekund",other:"mindre \xE4n {{count}} sekunder"},xSeconds:{one:"en sekund",other:"{{count}} sekunder"},halfAMinute:"en halv minut",lessThanXMinutes:{one:"mindre \xE4n en minut",other:"mindre \xE4n {{count}} minuter"},xMinutes:{one:"en minut",other:"{{count}} minuter"},aboutXHours:{one:"ungef\xE4r en timme",other:"ungef\xE4r {{count}} timmar"},xHours:{one:"en timme",other:"{{count}} timmar"},xDays:{one:"en dag",other:"{{count}} dagar"},aboutXWeeks:{one:"ungef\xE4r en vecka",other:"ungef\xE4r {{count}} veckor"},xWeeks:{one:"en vecka",other:"{{count}} veckor"},aboutXMonths:{one:"ungef\xE4r en m\xE5nad",other:"ungef\xE4r {{count}} m\xE5nader"},xMonths:{one:"en m\xE5nad",other:"{{count}} m\xE5nader"},aboutXYears:{one:"ungef\xE4r ett \xE5r",other:"ungef\xE4r {{count}} \xE5r"},xYears:{one:"ett \xE5r",other:"{{count}} \xE5r"},overXYears:{one:"\xF6ver ett \xE5r",other:"\xF6ver {{count}} \xE5r"},almostXYears:{one:"n\xE4stan ett \xE5r",other:"n\xE4stan {{count}} \xE5r"}},N=["noll","en","tv\xE5","tre","fyra","fem","sex","sju","\xE5tta","nio","tio","elva","tolv"],S=function E(B,x,X){var H,U=Y[B];if(typeof U==="string")H=U;else if(x===1)H=U.one;else H=U.other.replace("{{count}}",x<13?N[x]:String(x));if(X!==null&&X!==void 0&&X.addSuffix)if(X.comparison&&X.comparison>0)return"om "+H;else return H+" sedan";return H};function W(E){return function(){var B=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},x=B.width?String(B.width):E.defaultWidth,X=E.formats[x]||E.formats[E.defaultWidth];return X}}var $={full:"EEEE d MMMM y",long:"d MMMM y",medium:"d MMM y",short:"y-MM-dd"},M={full:"'kl'. HH:mm:ss zzzz",long:"HH:mm:ss z",medium:"HH:mm:ss",short:"HH:mm"},R={full:"{{date}} 'kl.' {{time}}",long:"{{date}} 'kl.' {{time}}",medium:"{{date}} {{time}}",short:"{{date}} {{time}}"},j={date:W({formats:$,defaultWidth:"full"}),time:W({formats:M,defaultWidth:"full"}),dateTime:W({formats:R,defaultWidth:"full"})},L={lastWeek:"'i' EEEE's kl.' p",yesterday:"'ig\xE5r kl.' p",today:"'idag kl.' p",tomorrow:"'imorgon kl.' p",nextWeek:"EEEE 'kl.' p",other:"P"},V=function E(B,x,X,H){return L[B]};function T(E){return function(B,x){var X=x!==null&&x!==void 0&&x.context?String(x.context):"standalone",H;if(X==="formatting"&&E.formattingValues){var U=E.defaultFormattingWidth||E.defaultWidth,Z=x!==null&&x!==void 0&&x.width?String(x.width):U;H=E.formattingValues[Z]||E.formattingValues[U]}else{var I=E.defaultWidth,A=x!==null&&x!==void 0&&x.width?String(x.width):E.defaultWidth;H=E.values[A]||E.values[I]}var O=E.argumentCallback?E.argumentCallback(B):B;return H[O]}}var f={narrow:["f.Kr.","e.Kr."],abbreviated:["f.Kr.","e.Kr."],wide:["f\xF6re Kristus","efter Kristus"]},P={narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["1:a kvartalet","2:a kvartalet","3:e kvartalet","4:e kvartalet"]},v={narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],abbreviated:["jan.","feb.","mars","apr.","maj","juni","juli","aug.","sep.","okt.","nov.","dec."],wide:["januari","februari","mars","april","maj","juni","juli","augusti","september","oktober","november","december"]},_={narrow:["S","M","T","O","T","F","L"],short:["s\xF6","m\xE5","ti","on","to","fr","l\xF6"],abbreviated:["s\xF6n","m\xE5n","tis","ons","tors","fre","l\xF6r"],wide:["s\xF6ndag","m\xE5ndag","tisdag","onsdag","torsdag","fredag","l\xF6rdag"]},w={narrow:{am:"fm",pm:"em",midnight:"midnatt",noon:"middag",morning:"morg.",afternoon:"efterm.",evening:"kv\xE4ll",night:"natt"},abbreviated:{am:"f.m.",pm:"e.m.",midnight:"midnatt",noon:"middag",morning:"morgon",afternoon:"efterm.",evening:"kv\xE4ll",night:"natt"},wide:{am:"f\xF6rmiddag",pm:"eftermiddag",midnight:"midnatt",noon:"middag",morning:"morgon",afternoon:"eftermiddag",evening:"kv\xE4ll",night:"natt"}},F={narrow:{am:"fm",pm:"em",midnight:"midnatt",noon:"middag",morning:"p\xE5 morg.",afternoon:"p\xE5 efterm.",evening:"p\xE5 kv\xE4llen",night:"p\xE5 natten"},abbreviated:{am:"fm",pm:"em",midnight:"midnatt",noon:"middag",morning:"p\xE5 morg.",afternoon:"p\xE5 efterm.",evening:"p\xE5 kv\xE4llen",night:"p\xE5 natten"},wide:{am:"fm",pm:"em",midnight:"midnatt",noon:"middag",morning:"p\xE5 morgonen",afternoon:"p\xE5 eftermiddagen",evening:"p\xE5 kv\xE4llen",night:"p\xE5 natten"}},k=function E(B,x){var X=Number(B),H=X%100;if(H>20||H<10)switch(H%10){case 1:case 2:return X+":a"}return X+":e"},h={ordinalNumber:k,era:T({values:f,defaultWidth:"wide"}),quarter:T({values:P,defaultWidth:"wide",argumentCallback:function E(B){return B-1}}),month:T({values:v,defaultWidth:"wide"}),day:T({values:_,defaultWidth:"wide"}),dayPeriod:T({values:w,defaultWidth:"wide",formattingValues:F,defaultFormattingWidth:"wide"})};function Q(E){return function(B){var x=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},X=x.width,H=X&&E.matchPatterns[X]||E.matchPatterns[E.defaultMatchWidth],U=B.match(H);if(!U)return null;var Z=U[0],I=X&&E.parsePatterns[X]||E.parsePatterns[E.defaultParseWidth],A=Array.isArray(I)?m(I,function(z){return z.test(Z)}):b(I,function(z){return z.test(Z)}),O;O=E.valueCallback?E.valueCallback(A):A,O=x.valueCallback?x.valueCallback(O):O;var EE=B.slice(Z.length);return{value:O,rest:EE}}}var b=function E(B,x){for(var X in B)if(Object.prototype.hasOwnProperty.call(B,X)&&x(B[X]))return X;return},m=function E(B,x){for(var X=0;X<B.length;X++)if(x(B[X]))return X;return};function c(E){return function(B){var x=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},X=B.match(E.matchPattern);if(!X)return null;var H=X[0],U=B.match(E.parsePattern);if(!U)return null;var Z=E.valueCallback?E.valueCallback(U[0]):U[0];Z=x.valueCallback?x.valueCallback(Z):Z;var I=B.slice(H.length);return{value:Z,rest:I}}}var y=/^(\d+)(:a|:e)?/i,g=/\d+/i,p={narrow:/^(f\.? ?Kr\.?|f\.? ?v\.? ?t\.?|e\.? ?Kr\.?|v\.? ?t\.?)/i,abbreviated:/^(f\.? ?Kr\.?|f\.? ?v\.? ?t\.?|e\.? ?Kr\.?|v\.? ?t\.?)/i,wide:/^(före Kristus|före vår tid|efter Kristus|vår tid)/i},d={any:[/^f/i,/^[ev]/i]},u={narrow:/^[1234]/i,abbreviated:/^q[1234]/i,wide:/^[1234](:a|:e)? kvartalet/i},l={any:[/1/i,/2/i,/3/i,/4/i]},i={narrow:/^[jfmasond]/i,abbreviated:/^(jan|feb|mar[s]?|apr|maj|jun[i]?|jul[i]?|aug|sep|okt|nov|dec)\.?/i,wide:/^(januari|februari|mars|april|maj|juni|juli|augusti|september|oktober|november|december)/i},n={narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^mar/i,/^ap/i,/^maj/i,/^jun/i,/^jul/i,/^au/i,/^s/i,/^o/i,/^n/i,/^d/i]},s={narrow:/^[smtofl]/i,short:/^(sö|må|ti|on|to|fr|lö)/i,abbreviated:/^(sön|mån|tis|ons|tors|fre|lör)/i,wide:/^(söndag|måndag|tisdag|onsdag|torsdag|fredag|lördag)/i},o={any:[/^s/i,/^m/i,/^ti/i,/^o/i,/^to/i,/^f/i,/^l/i]},r={any:/^([fe]\.?\s?m\.?|midn(att)?|midd(ag)?|(på) (morgonen|eftermiddagen|kvällen|natten))/i},a={any:{am:/^f/i,pm:/^e/i,midnight:/^midn/i,noon:/^midd/i,morning:/morgon/i,afternoon:/eftermiddag/i,evening:/kväll/i,night:/natt/i}},e={ordinalNumber:c({matchPattern:y,parsePattern:g,valueCallback:function E(B){return parseInt(B,10)}}),era:Q({matchPatterns:p,defaultMatchWidth:"wide",parsePatterns:d,defaultParseWidth:"any"}),quarter:Q({matchPatterns:u,defaultMatchWidth:"wide",parsePatterns:l,defaultParseWidth:"any",valueCallback:function E(B){return B+1}}),month:Q({matchPatterns:i,defaultMatchWidth:"wide",parsePatterns:n,defaultParseWidth:"any"}),day:Q({matchPatterns:s,defaultMatchWidth:"wide",parsePatterns:o,defaultParseWidth:"any"}),dayPeriod:Q({matchPatterns:r,defaultMatchWidth:"any",parsePatterns:a,defaultParseWidth:"any"})},t={code:"sv",formatDistance:S,formatLong:j,formatRelative:V,localize:h,match:e,options:{weekStartsOn:1,firstWeekContainsDate:4}};window.dateFns=K(K({},window.dateFns),{},{locale:K(K({},(G=window.dateFns)===null||G===void 0?void 0:G.locale),{},{sv:t})})})();

//# debugId=950042D2C9B4F53D64756e2164756e21
