# Brand Promise

Keeping user information safe and secure is a top priority, and we welcome the contribution of external security researchers.

# Scope

If you believe you've found a security issue in software that is maintained in this repository, we encourage you to notify us.

| Version | In scope | Source code |
| ------- | -------- | ----------- |
| >= 6.0.0 | ✅ | https://github.com/Hacker0x01/react-datepicker |
| < 2.0.0 | ❌ | https://github.com/Hacker0x01/react-datepicker/releases |

# How to Submit a Report

To submit a vulnerability report, please fill out this [form](https://hackerone.com/security). Your submission will be reviewed and validated by a member of our team.

# Safe Harbor

We support safe harbor for security researchers who:

* Make a good faith effort to avoid privacy violations, destruction of data, and interruption or degradation of our services.
* Only interact with accounts you own or with explicit permission of the account holder. If you do encounter Personally Identifiable Information (PII) contact us immediately, do not proceed with access, and immediately purge any local information.
* Provide us with a reasonable amount of time to resolve vulnerabilities prior to any disclosure to the public or a third-party.

We will consider activities conducted consistent with this policy to constitute "authorized" conduct and will not pursue civil action or initiate a complaint to law enforcement. We will help to the extent we can if legal action is initiated by a third party against you.

Please submit a report to us before engaging in conduct that may be inconsistent with or unaddressed by this policy.

# Preferences

* Please provide detailed reports with reproducible steps and a clearly defined impact.
* Include the version number of the vulnerable package in your report
* Social engineering (e.g. phishing, vishing, smishing) is prohibited.

