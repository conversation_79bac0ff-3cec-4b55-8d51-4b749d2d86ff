"use strict";
exports.eu = void 0;
var _index = require("./eu/_lib/formatDistance.js");
var _index2 = require("./eu/_lib/formatLong.js");
var _index3 = require("./eu/_lib/formatRelative.js");
var _index4 = require("./eu/_lib/localize.js");
var _index5 = require("./eu/_lib/match.js");

/**
 * @category Locales
 * @summary Basque locale.
 * @language Basque
 * @iso-639-2 eus
 * <AUTHOR> [@JacobSoderblom](https://github.com/JacobSoderblom)
 */
const eu = (exports.eu = {
  code: "eu",
  formatDistance: _index.formatDistance,
  formatLong: _index2.formatLong,
  formatRelative: _index3.formatRelative,
  localize: _index4.localize,
  match: _index5.match,
  options: {
    weekStartsOn: 1 /* Monday */,
    firstWeekContainsDate: 1,
  },
});
