{"name": "@tanstack/react-query", "version": "5.59.15", "description": "Hooks for managing, caching and syncing asynchronous and remote data in React", "author": "<PERSON><PERSON><PERSON><PERSON>", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/TanStack/query.git", "directory": "packages/react-query"}, "homepage": "https://tanstack.com/query", "funding": {"type": "github", "url": "https://github.com/sponsors/tanner<PERSON>ley"}, "type": "module", "types": "build/legacy/index.d.ts", "main": "build/legacy/index.cjs", "module": "build/legacy/index.js", "exports": {".": {"import": {"types": "./build/modern/index.d.ts", "default": "./build/modern/index.js"}, "require": {"types": "./build/modern/index.d.cts", "default": "./build/modern/index.cjs"}}, "./package.json": "./package.json"}, "sideEffects": false, "files": ["build", "src", "!build/codemods/node_modules", "!build/codemods/vite.config.ts", "!build/codemods/**/__testfixtures__", "!build/codemods/**/__tests__"], "dependencies": {"@tanstack/query-core": "5.59.13"}, "devDependencies": {"@types/react": "npm:types-react@rc", "@types/react-dom": "npm:types-react-dom@rc", "@vitejs/plugin-react": "^4.3.1", "eslint-plugin-react-compiler": "0.0.0-experimental-f8a5409-20240829", "react": "19.0.0-rc-4c2e457c7c-20240522", "react-dom": "19.0.0-rc-4c2e457c7c-20240522", "react-error-boundary": "^4.0.13"}, "peerDependencies": {"react": "^18 || ^19"}, "scripts": {}}