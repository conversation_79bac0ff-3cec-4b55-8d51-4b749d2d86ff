import React, { useState, useEffect } from 'react';
import { FaArrowLeft, FaUpload, FaSearch, FaFileAlt, FaGavel, FaBusiness, FaDownload } from 'react-icons/fa';
import { MdBusiness, MdTransferWithinAStation, MdPerson, MdAttachMoney } from 'react-icons/md';
import { PDFDownloadLink } from '@react-pdf/renderer';
import SuccessMessage from './SuccessMessage';
import AnnonceLegalePdf from '../annonces-legales/AnnonceLegalePdf';
import { generateNumeroSerie, getNextNumeroSerie } from '../../services/numeroSerieService';

interface AnnonceLegaleFormProps {
  onClose?: () => void;
}

const AnnonceLegaleForm: React.FC<AnnonceLegaleFormProps> = ({ onClose }) => {
  const [currentStep, setCurrentStep] = useState(0);
  const [showSuccess, setShowSuccess] = useState(false);
  const [numeroSerie, setNumeroSerie] = useState('');
  const [numeroSerieJournal, setNumeroSerieJournal] = useState('');
  const [pdfGenerated, setPdfGenerated] = useState(false);
  const [formData, setFormData] = useState({
    selectedService: '',
    typeAnnonce: '',
    nomEntreprise: '',
    formeJuridique: '',
    numeroRC: '',
    adresseSiege: '',
    ville: '',
    capital: '',
    dirigeant: {
      nom: '',
      prenom: '',
      cin: '',
      qualite: '',
      adresse: ''
    },
    contenuAnnonce: '',
    documentsJustificatifs: [] as File[],
    selectedOffer: null as number | null
  });

  // Générer les numéros de série de prévisualisation
  useEffect(() => {
    const nextNumero = getNextNumeroSerie('annonce-legale');
    const nextNumeroJournal = getNextNumeroSerie('journal', 'JRN');
    setNumeroSerie(nextNumero);
    setNumeroSerieJournal(nextNumeroJournal);
  }, []);

  const totalSteps = 3;

  // Services juridiques spécialisés pour annonces légales
  const annonceLegaleServices = [
    { 
      id: 'creation-societe', 
      name: 'Création de société', 
      icon: <MdBusiness className="text-4xl" />,
      description: 'Publication de l\'acte de constitution d\'une nouvelle société'
    },
    { 
      id: 'modification-statuts', 
      name: 'Modification des statuts', 
      icon: <FaFileAlt className="text-4xl" />,
      description: 'Annonce de modification des statuts de la société'
    },
    { 
      id: 'transfert-siege', 
      name: 'Transfert de siège social', 
      icon: <MdTransferWithinAStation className="text-4xl" />,
      description: 'Changement d\'adresse du siège social'
    },
    { 
      id: 'changement-dirigeant', 
      name: 'Changement de dirigeant', 
      icon: <MdPerson className="text-4xl" />,
      description: 'Nomination ou révocation d\'un dirigeant'
    },
    { 
      id: 'augmentation-capital', 
      name: 'Augmentation de capital', 
      icon: <MdAttachMoney className="text-4xl" />,
      description: 'Annonce d\'augmentation du capital social'
    },
    { 
      id: 'dissolution', 
      name: 'Dissolution de société', 
      icon: <FaGavel className="text-4xl" />,
      description: 'Annonce de dissolution et liquidation'
    }
  ];

  // Journaux d'annonces légales
  const journaux = [
    { id: 'al-alam', name: 'Al Alam', tarif: 850 },
    { id: 'leconomiste', name: 'L\'Économiste', tarif: 950 },
    { id: 'la-vie-eco', name: 'La Vie Éco', tarif: 900 },
    { id: 'aujourdhui', name: 'Aujourd\'hui le Maroc', tarif: 800 },
    { id: 'matin', name: 'Le Matin', tarif: 875 }
  ];

  const handleInputChange = (field: string, value: any) => {
    if (field.includes('.')) {
      const [parent, child] = field.split('.');
      setFormData(prev => ({
        ...prev,
        [parent]: {
          ...prev[parent as keyof typeof prev],
          [child]: value
        }
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [field]: value
      }));
    }
  };

  const handleFileUpload = (files: FileList | null) => {
    if (files) {
      setFormData(prev => ({
        ...prev,
        documentsJustificatifs: [...prev.documentsJustificatifs, ...Array.from(files)]
      }));
    }
  };

  const handleNext = () => {
    if (currentStep < totalSteps) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleServiceSelect = (serviceId: string) => {
    setFormData(prev => ({
      ...prev,
      selectedService: serviceId
    }));
  };

  const handleSubmit = () => {
    // Générer les numéros de série définitifs
    const finalNumeroSerie = generateNumeroSerie('annonce-legale');
    const finalNumeroSerieJournal = generateNumeroSerie('journal', 'JRN');
    setNumeroSerie(finalNumeroSerie);
    setNumeroSerieJournal(finalNumeroSerieJournal);

    console.log('Annonce légale soumise:', {
      ...formData,
      numeroSerie: finalNumeroSerie,
      numeroSerieJournal: finalNumeroSerieJournal
    });

    // Générer automatiquement le PDF
    generatePDF();

    setShowSuccess(true);
  };

  // Fonction pour générer le numéro de série du journal
  const generateJournalSerialNumber = () => {
    return numeroSerieJournal || getNextNumeroSerie('journal', 'JRN');
  };

  const generatePDF = () => {
    // Déclencher le téléchargement automatique du PDF
    setTimeout(() => {
      const downloadButton = document.querySelector('#annonce-legale-pdf-download-link') as HTMLAnchorElement;
      if (downloadButton) {
        downloadButton.click();
        setPdfGenerated(true);
      }
    }, 100);
  };

  // Fonction pour mapper les données du formulaire vers le format du PDF
  const mapFormDataToPdfData = () => {
    const currentDate = new Date().toLocaleDateString('fr-FR');

    return {
      numeroSerie,
      numeroSerieJournal: numeroSerieJournal || generateJournalSerialNumber(),
      selectedService: formData.selectedService,
      nomEntreprise: formData.nomEntreprise,
      formeJuridique: formData.formeJuridique,
      numeroRC: formData.numeroRC,
      capital: formData.capital,
      adresseSiege: formData.adresseSiege,
      objetSocial: '', // Champ retiré mais gardé pour compatibilité PDF
      dirigeant: formData.dirigeant,
      journalPublication: 'Journal automatique', // Valeur par défaut
      datePublication: new Date().toISOString().split('T')[0], // Date actuelle
      contenuAnnonce: formData.contenuAnnonce,
      dateGeneration: currentDate
    };
  };

  const renderStepIndicator = () => {
    if (currentStep === 0) return null;

    return (
      <div className="flex items-center justify-center mb-8">
        {[1, 2, 3].map((step) => (
          <React.Fragment key={step}>
            <div
              className={`w-10 h-10 rounded-full flex items-center justify-center text-sm font-medium ${
                step <= currentStep
                  ? 'bg-orange-500 text-white'
                  : 'bg-gray-200 text-gray-500'
              }`}
            >
              {step}
            </div>
            {step < 3 && (
              <div
                className={`w-20 h-1 mx-3 ${
                  step < currentStep ? 'bg-orange-500' : 'bg-gray-200'
                }`}
              />
            )}
          </React.Fragment>
        ))}
      </div>
    );
  };

  const renderStep0 = () => (
    <div className="bg-white rounded-lg shadow-md p-6 mb-6">
      <div className="text-center mb-8">
        <h3 className="text-2xl font-bold text-gray-900 mb-4">Type d'annonce légale</h3>
        <p className="text-gray-600">Sélectionnez le type d'annonce légale que vous souhaitez publier</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {annonceLegaleServices.map((service) => (
          <div
            key={service.id}
            onClick={() => handleServiceSelect(service.id)}
            className={`p-6 border-2 rounded-xl cursor-pointer transition-all duration-200 hover:shadow-lg ${
              formData.selectedService === service.id
                ? 'border-orange-500 bg-orange-50 shadow-md'
                : 'border-gray-200 bg-white hover:border-orange-300'
            }`}
          >
            <div className="text-center">
              <div className="text-orange-500 mb-4 flex justify-center">{service.icon}</div>
              <h4 className="text-lg font-semibold text-gray-900 mb-2">{service.name}</h4>
              <p className="text-sm text-gray-600 mb-4">{service.description}</p>
              <div className={`w-full h-1 rounded-full ${
                formData.selectedService === service.id ? 'bg-orange-500' : 'bg-gray-200'
              }`}></div>
            </div>
          </div>
        ))}
      </div>

      {formData.selectedService && (
        <div className="mt-8 p-4 bg-orange-50 rounded-lg border border-orange-200">
          <div className="flex items-center">
            <div className="text-orange-500 mr-3">
              <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
              </svg>
            </div>
            <p className="text-orange-700 font-medium">
              Service sélectionné : {annonceLegaleServices.find(s => s.id === formData.selectedService)?.name}
            </p>
          </div>
        </div>
      )}
    </div>
  );

  const renderStep1 = () => (
    <div className="bg-white rounded-lg shadow-md p-6 mb-6">
      <div className="flex items-center mb-6">
        <button
          onClick={handlePrevious}
          className="mr-4 p-2 text-gray-500 hover:text-gray-700"
        >
          <FaArrowLeft />
        </button>
        <h3 className="text-xl font-semibold text-orange-500">Informations de l'entreprise</h3>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Nom de l'entreprise *
          </label>
          <input
            type="text"
            value={formData.nomEntreprise}
            onChange={(e) => handleInputChange('nomEntreprise', e.target.value)}
            placeholder="SARL TECH SOLUTIONS"
            className="w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent bg-gray-50 text-gray-700"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Forme juridique *
          </label>
          <select
            value={formData.formeJuridique}
            onChange={(e) => handleInputChange('formeJuridique', e.target.value)}
            className="w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent bg-gray-50 text-gray-700"
          >
            <option value="">Sélectionner...</option>
            <option value="SARL">SARL</option>
            <option value="SA">SA</option>
            <option value="SNC">SNC</option>
            <option value="SCS">SCS</option>
            <option value="SCA">SCA</option>
          </select>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Numéro RC
          </label>
          <input
            type="text"
            value={formData.numeroRC}
            onChange={(e) => handleInputChange('numeroRC', e.target.value)}
            placeholder="123456"
            className="w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent bg-gray-50 text-gray-700"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Capital social (DH)
          </label>
          <input
            type="text"
            value={formData.capital}
            onChange={(e) => handleInputChange('capital', e.target.value)}
            placeholder="100 000"
            className="w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent bg-gray-50 text-gray-700"
          />
        </div>
      </div>

      <div className="mb-6">
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Adresse du siège social *
        </label>
        <textarea
          value={formData.adresseSiege}
          onChange={(e) => handleInputChange('adresseSiege', e.target.value)}
          placeholder="123 Rue Mohammed V, Casablanca"
          rows={3}
          className="w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent bg-gray-50 text-gray-700"
        />
      </div>



      {/* Informations du dirigeant */}
      <div className="bg-orange-50 rounded-lg p-6 mb-6">
        <h4 className="text-lg font-medium text-orange-600 mb-4">Informations du dirigeant</h4>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Nom *
            </label>
            <input
              type="text"
              value={formData.dirigeant.nom}
              onChange={(e) => handleInputChange('dirigeant.nom', e.target.value)}
              placeholder="EZZIANI"
              className="w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent bg-white text-gray-700"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Prénom *
            </label>
            <input
              type="text"
              value={formData.dirigeant.prenom}
              onChange={(e) => handleInputChange('dirigeant.prenom', e.target.value)}
              placeholder="Youssef"
              className="w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent bg-white text-gray-700"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              CIN *
            </label>
            <input
              type="text"
              value={formData.dirigeant.cin}
              onChange={(e) => handleInputChange('dirigeant.cin', e.target.value)}
              placeholder="AB123456"
              className="w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent bg-white text-gray-700"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Qualité *
            </label>
            <select
              value={formData.dirigeant.qualite}
              onChange={(e) => handleInputChange('dirigeant.qualite', e.target.value)}
              className="w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent bg-white text-gray-700"
            >
              <option value="">Sélectionner...</option>
              <option value="Gérant">Gérant</option>
              <option value="Président">Président</option>
              <option value="Directeur Général">Directeur Général</option>
              <option value="Administrateur">Administrateur</option>
            </select>
          </div>
        </div>
        <div className="mt-4">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Adresse *
          </label>
          <textarea
            value={formData.dirigeant.adresse}
            onChange={(e) => handleInputChange('dirigeant.adresse', e.target.value)}
            placeholder="Adresse complète du dirigeant"
            rows={2}
            className="w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent bg-white text-gray-700"
          />
        </div>
      </div>

      {/* Contenu de la publication */}
      <div className="bg-blue-50 rounded-lg p-6">
        <h4 className="text-lg font-medium text-blue-600 mb-4">Contenu de la publication</h4>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Contenu de l'annonce légale *
          </label>
          <textarea
            value={formData.contenuAnnonce}
            onChange={(e) => handleInputChange('contenuAnnonce', e.target.value)}
            placeholder="Rédigez le contenu de votre annonce légale selon les exigences légales pour le type d'annonce sélectionné..."
            rows={8}
            className="w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white text-gray-700"
          />
          <p className="text-sm text-gray-500 mt-2">
            Le contenu doit respecter les exigences légales pour le type d'annonce sélectionné.
          </p>
        </div>
      </div>
    </div>
  );

  const renderStep2 = () => (
    <div className="bg-white rounded-lg shadow-md p-6 mb-6">
      <div className="flex items-center mb-6">
        <button
          onClick={handlePrevious}
          className="mr-4 p-2 text-gray-500 hover:text-gray-700"
        >
          <FaArrowLeft />
        </button>
        <h3 className="text-xl font-semibold text-orange-500">Documents justificatifs</h3>
      </div>

      <div className="mb-6">
        <label className="block text-sm font-medium text-gray-700 mb-3">
          Documents justificatifs (optionnel)
        </label>
        <div className="border-2 border-dashed border-gray-300 rounded-xl p-6 text-center bg-gray-50">
          <input
            type="file"
            id="documents-justificatifs"
            className="hidden"
            multiple
            accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
            onChange={(e) => handleFileUpload(e.target.files)}
          />
          <label
            htmlFor="documents-justificatifs"
            className="cursor-pointer inline-flex items-center px-6 py-3 bg-orange-500 text-white rounded-lg hover:bg-orange-600 font-medium shadow-sm"
          >
            <FaUpload className="mr-2" />
            Télécharger les documents
          </label>
          <p className="mt-3 text-sm text-gray-600">
            Formats acceptés: PDF, DOC, DOCX, JPG, PNG
          </p>
          {formData.documentsJustificatifs.length > 0 && (
            <div className="mt-4">
              <p className="text-sm font-medium text-gray-700 mb-2">Documents téléchargés:</p>
              <ul className="text-sm text-gray-600">
                {formData.documentsJustificatifs.map((file, index) => (
                  <li key={index} className="flex items-center justify-center">
                    <FaFileAlt className="mr-2" />
                    {file.name}
                  </li>
                ))}
              </ul>
            </div>
          )}
        </div>
      </div>

      <div className="bg-gray-50 rounded-lg p-4">
        <h4 className="text-md font-medium text-gray-700 mb-2">Information</h4>
        <p className="text-sm text-gray-600">
          Cette étape est optionnelle. Vous pouvez télécharger des documents justificatifs
          qui accompagneront votre annonce légale (statuts, procès-verbaux, etc.).
        </p>
      </div>
    </div>
  );

  const renderStep3 = () => (
    <div className="bg-white rounded-lg shadow-md p-6 mb-6">
      <div className="flex items-center mb-6">
        <button
          onClick={handlePrevious}
          className="mr-4 p-2 text-gray-500 hover:text-gray-700"
        >
          <FaArrowLeft />
        </button>
        <h3 className="text-xl font-semibold text-orange-500">Finalisation et génération PDF</h3>
      </div>

      {/* Numéro de série de l'annonce */}
      <div className="mb-6 p-4 bg-orange-50 rounded-lg border border-orange-200">
        <div className="flex items-center justify-between">
          <div>
            <h4 className="text-lg font-medium text-orange-600">Numéro de série de l'annonce</h4>
            <p className="text-sm text-gray-600">Ce numéro sera attribué automatiquement à votre annonce légale</p>
          </div>
          <div className="text-right">
            <p className="text-2xl font-bold text-orange-600">{numeroSerie}</p>
            <p className="text-xs text-gray-500">Numéro de série annonce</p>
          </div>
        </div>
      </div>

      {/* Numéro de série du journal */}
      <div className="mb-6 p-4 bg-blue-50 rounded-lg border border-blue-200">
        <div className="flex items-center justify-between">
          <div>
            <h4 className="text-lg font-medium text-blue-600">Numéro de série du journal (quotidien)</h4>
            <p className="text-sm text-gray-600">Numéro unique par jour, inchangeable jusqu'au prochain jour</p>
            <p className="text-xs text-blue-500 mt-1">
              📅 Aujourd'hui: JRN-0001 → Demain: JRN-0002 → Après-demain: JRN-0003
            </p>
          </div>
          <div className="text-right">
            <p className="text-2xl font-bold text-blue-600">{generateJournalSerialNumber()}</p>
            <p className="text-xs text-gray-500">Format: JRN-NNNN</p>
          </div>
        </div>
      </div>

      {/* Section de génération PDF */}
      <div className="bg-green-50 rounded-lg p-6 border border-green-200">
        <h4 className="text-lg font-medium text-green-600 mb-4">Génération automatique du PDF</h4>
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm text-gray-700 mb-2">
              Un PDF sera automatiquement généré avec votre annonce légale lors de la soumission.
            </p>
            <p className="text-xs text-gray-500">
              Le PDF contiendra toutes les informations saisies et les numéros de série uniques.
            </p>
          </div>

          {/* Bouton de prévisualisation PDF */}
          <PDFDownloadLink
            document={<AnnonceLegalePdf {...mapFormDataToPdfData()} />}
            fileName={`preview-annonce-legale-${numeroSerie}.pdf`}
            className="inline-flex items-center px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors text-sm"
          >
            {({ loading }: { loading: boolean }) => (
              loading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Génération...
                </>
              ) : (
                <>
                  <FaFileAlt className="mr-2" />
                  Prévisualiser PDF
                </>
              )
            )}
          </PDFDownloadLink>
        </div>
      </div>
    </div>
  );

  // Si le message de succès doit être affiché
  if (showSuccess) {
    return (
      <div className="bg-white p-6">
        <div className="max-w-4xl mx-auto">
          <div className="text-center">
            <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-green-100 mb-4">
              <svg className="h-6 w-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
              </svg>
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">Annonce légale enregistrée avec succès</h3>
            <p className="text-sm text-gray-500 mb-6">
              Votre demande d'annonce légale a été enregistrée avec le numéro de série <strong>{numeroSerie}</strong>.
              {pdfGenerated && " Le PDF a été automatiquement téléchargé."}
            </p>

            {/* Bouton de téléchargement PDF visible */}
            <div className="flex justify-center space-x-4 mb-6">
              <PDFDownloadLink
                document={<AnnonceLegalePdf {...mapFormDataToPdfData()} />}
                fileName={`annonce-legale-${numeroSerie}-${new Date().toISOString().split('T')[0]}.pdf`}
                className="inline-flex items-center px-6 py-3 bg-orange-500 text-white rounded-lg hover:bg-orange-600 transition-colors"
              >
                {({ loading }: { loading: boolean }) => (
                  loading ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Génération...
                    </>
                  ) : (
                    <>
                      <FaDownload className="mr-2" />
                      Télécharger le PDF
                    </>
                  )
                )}
              </PDFDownloadLink>
            </div>

            <button
              onClick={onClose}
              className="inline-flex items-center px-6 py-3 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors"
            >
              Retour au tableau de bord
            </button>
          </div>
        </div>

        {/* PDF Download Link caché pour génération automatique */}
        <PDFDownloadLink
          document={<AnnonceLegalePdf {...mapFormDataToPdfData()} />}
          fileName={`annonce-legale-${numeroSerie}-${new Date().toISOString().split('T')[0]}.pdf`}
          style={{ display: 'none' }}
        >
          {({ url }: { url: string | null }) => (
            <a id="annonce-legale-pdf-download-link" href={url || '#'} style={{ display: 'none' }}>
              Télécharger PDF
            </a>
          )}
        </PDFDownloadLink>
      </div>
    );
  }

  return (
    <div className="bg-white p-6">
      <div className="max-w-4xl mx-auto">
        <div className="mb-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Nouvelle annonce légale</h2>
          <p className="text-gray-600">
            {currentStep === 0
              ? 'Sélectionnez le type d\'annonce légale'
              : 'Remplissez les informations requises pour votre annonce légale'
            }
          </p>
        </div>

        {renderStepIndicator()}

        {currentStep === 0 && renderStep0()}
        {currentStep === 1 && renderStep1()}
        {currentStep === 2 && renderStep2()}
        {currentStep === 3 && renderStep3()}

        <div className="flex justify-between items-center mt-8">
          <button
            onClick={handlePrevious}
            disabled={currentStep === 0}
            className={`px-6 py-3 rounded-lg font-medium ${
              currentStep === 0
                ? 'bg-gray-200 text-gray-400 cursor-not-allowed'
                : 'bg-gray-500 text-white hover:bg-gray-600'
            }`}
          >
            Précédent
          </button>

          {currentStep === 0 ? (
            <button
              onClick={handleNext}
              disabled={!formData.selectedService}
              className={`px-6 py-3 rounded-lg font-medium ${
                !formData.selectedService
                  ? 'bg-gray-200 text-gray-400 cursor-not-allowed'
                  : 'bg-orange-500 text-white hover:bg-orange-600'
              }`}
            >
              Commencer
            </button>
          ) : currentStep < totalSteps ? (
            <button
              onClick={handleNext}
              className="px-6 py-3 bg-orange-500 text-white rounded-lg hover:bg-orange-600 font-medium"
            >
              Suivant
            </button>
          ) : (
            <button
              onClick={handleSubmit}
              className="px-6 py-3 bg-green-500 text-white rounded-lg hover:bg-green-600 font-medium"
            >
              Publier l'annonce
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default AnnonceLegaleForm;
