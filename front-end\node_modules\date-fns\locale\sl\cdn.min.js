var x=function(H){return x=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(G){return typeof G}:function(G){return G&&typeof Symbol=="function"&&G.constructor===Symbol&&G!==Symbol.prototype?"symbol":typeof G},x(H)},D=function(H,G){var X=Object.keys(H);if(Object.getOwnPropertySymbols){var I=Object.getOwnPropertySymbols(H);G&&(I=I.filter(function(z){return Object.getOwnPropertyDescriptor(H,z).enumerable})),X.push.apply(X,I)}return X},K=function(H){for(var G=1;G<arguments.length;G++){var X=arguments[G]!=null?arguments[G]:{};G%2?D(Object(X),!0).forEach(function(I){B0(H,I,X[I])}):Object.getOwnPropertyDescriptors?Object.defineProperties(H,Object.getOwnPropertyDescriptors(X)):D(Object(X)).forEach(function(I){Object.defineProperty(H,I,Object.getOwnPropertyDescriptor(X,I))})}return H},B0=function(H,G,X){if(G=C0(G),G in H)Object.defineProperty(H,G,{value:X,enumerable:!0,configurable:!0,writable:!0});else H[G]=X;return H},C0=function(H){var G=G0(H,"string");return x(G)=="symbol"?G:String(G)},G0=function(H,G){if(x(H)!="object"||!H)return H;var X=H[Symbol.toPrimitive];if(X!==void 0){var I=X.call(H,G||"default");if(x(I)!="object")return I;throw new TypeError("@@toPrimitive must return a primitive value.")}return(G==="string"?String:Number)(H)};(function(H){var G=Object.defineProperty,X=function E(U,B){for(var C in B)G(U,C,{get:B[C],enumerable:!0,configurable:!0,set:function Y(J){return B[C]=function(){return J}}})},I=function E(U){return U.one!==void 0},z=function E(U){switch(U%100){case 1:return"one";case 2:return"two";case 3:case 4:return"few";default:return"other"}},$={lessThanXSeconds:{present:{one:"manj kot {{count}} sekunda",two:"manj kot {{count}} sekundi",few:"manj kot {{count}} sekunde",other:"manj kot {{count}} sekund"},past:{one:"manj kot {{count}} sekundo",two:"manj kot {{count}} sekundama",few:"manj kot {{count}} sekundami",other:"manj kot {{count}} sekundami"},future:{one:"manj kot {{count}} sekundo",two:"manj kot {{count}} sekundi",few:"manj kot {{count}} sekunde",other:"manj kot {{count}} sekund"}},xSeconds:{present:{one:"{{count}} sekunda",two:"{{count}} sekundi",few:"{{count}} sekunde",other:"{{count}} sekund"},past:{one:"{{count}} sekundo",two:"{{count}} sekundama",few:"{{count}} sekundami",other:"{{count}} sekundami"},future:{one:"{{count}} sekundo",two:"{{count}} sekundi",few:"{{count}} sekunde",other:"{{count}} sekund"}},halfAMinute:"pol minute",lessThanXMinutes:{present:{one:"manj kot {{count}} minuta",two:"manj kot {{count}} minuti",few:"manj kot {{count}} minute",other:"manj kot {{count}} minut"},past:{one:"manj kot {{count}} minuto",two:"manj kot {{count}} minutama",few:"manj kot {{count}} minutami",other:"manj kot {{count}} minutami"},future:{one:"manj kot {{count}} minuto",two:"manj kot {{count}} minuti",few:"manj kot {{count}} minute",other:"manj kot {{count}} minut"}},xMinutes:{present:{one:"{{count}} minuta",two:"{{count}} minuti",few:"{{count}} minute",other:"{{count}} minut"},past:{one:"{{count}} minuto",two:"{{count}} minutama",few:"{{count}} minutami",other:"{{count}} minutami"},future:{one:"{{count}} minuto",two:"{{count}} minuti",few:"{{count}} minute",other:"{{count}} minut"}},aboutXHours:{present:{one:"pribli\u017Eno {{count}} ura",two:"pribli\u017Eno {{count}} uri",few:"pribli\u017Eno {{count}} ure",other:"pribli\u017Eno {{count}} ur"},past:{one:"pribli\u017Eno {{count}} uro",two:"pribli\u017Eno {{count}} urama",few:"pribli\u017Eno {{count}} urami",other:"pribli\u017Eno {{count}} urami"},future:{one:"pribli\u017Eno {{count}} uro",two:"pribli\u017Eno {{count}} uri",few:"pribli\u017Eno {{count}} ure",other:"pribli\u017Eno {{count}} ur"}},xHours:{present:{one:"{{count}} ura",two:"{{count}} uri",few:"{{count}} ure",other:"{{count}} ur"},past:{one:"{{count}} uro",two:"{{count}} urama",few:"{{count}} urami",other:"{{count}} urami"},future:{one:"{{count}} uro",two:"{{count}} uri",few:"{{count}} ure",other:"{{count}} ur"}},xDays:{present:{one:"{{count}} dan",two:"{{count}} dni",few:"{{count}} dni",other:"{{count}} dni"},past:{one:"{{count}} dnem",two:"{{count}} dnevoma",few:"{{count}} dnevi",other:"{{count}} dnevi"},future:{one:"{{count}} dan",two:"{{count}} dni",few:"{{count}} dni",other:"{{count}} dni"}},aboutXWeeks:{one:"pribli\u017Eno {{count}} teden",two:"pribli\u017Eno {{count}} tedna",few:"pribli\u017Eno {{count}} tedne",other:"pribli\u017Eno {{count}} tednov"},xWeeks:{one:"{{count}} teden",two:"{{count}} tedna",few:"{{count}} tedne",other:"{{count}} tednov"},aboutXMonths:{present:{one:"pribli\u017Eno {{count}} mesec",two:"pribli\u017Eno {{count}} meseca",few:"pribli\u017Eno {{count}} mesece",other:"pribli\u017Eno {{count}} mesecev"},past:{one:"pribli\u017Eno {{count}} mesecem",two:"pribli\u017Eno {{count}} mesecema",few:"pribli\u017Eno {{count}} meseci",other:"pribli\u017Eno {{count}} meseci"},future:{one:"pribli\u017Eno {{count}} mesec",two:"pribli\u017Eno {{count}} meseca",few:"pribli\u017Eno {{count}} mesece",other:"pribli\u017Eno {{count}} mesecev"}},xMonths:{present:{one:"{{count}} mesec",two:"{{count}} meseca",few:"{{count}} meseci",other:"{{count}} mesecev"},past:{one:"{{count}} mesecem",two:"{{count}} mesecema",few:"{{count}} meseci",other:"{{count}} meseci"},future:{one:"{{count}} mesec",two:"{{count}} meseca",few:"{{count}} mesece",other:"{{count}} mesecev"}},aboutXYears:{present:{one:"pribli\u017Eno {{count}} leto",two:"pribli\u017Eno {{count}} leti",few:"pribli\u017Eno {{count}} leta",other:"pribli\u017Eno {{count}} let"},past:{one:"pribli\u017Eno {{count}} letom",two:"pribli\u017Eno {{count}} letoma",few:"pribli\u017Eno {{count}} leti",other:"pribli\u017Eno {{count}} leti"},future:{one:"pribli\u017Eno {{count}} leto",two:"pribli\u017Eno {{count}} leti",few:"pribli\u017Eno {{count}} leta",other:"pribli\u017Eno {{count}} let"}},xYears:{present:{one:"{{count}} leto",two:"{{count}} leti",few:"{{count}} leta",other:"{{count}} let"},past:{one:"{{count}} letom",two:"{{count}} letoma",few:"{{count}} leti",other:"{{count}} leti"},future:{one:"{{count}} leto",two:"{{count}} leti",few:"{{count}} leta",other:"{{count}} let"}},overXYears:{present:{one:"ve\u010D kot {{count}} leto",two:"ve\u010D kot {{count}} leti",few:"ve\u010D kot {{count}} leta",other:"ve\u010D kot {{count}} let"},past:{one:"ve\u010D kot {{count}} letom",two:"ve\u010D kot {{count}} letoma",few:"ve\u010D kot {{count}} leti",other:"ve\u010D kot {{count}} leti"},future:{one:"ve\u010D kot {{count}} leto",two:"ve\u010D kot {{count}} leti",few:"ve\u010D kot {{count}} leta",other:"ve\u010D kot {{count}} let"}},almostXYears:{present:{one:"skoraj {{count}} leto",two:"skoraj {{count}} leti",few:"skoraj {{count}} leta",other:"skoraj {{count}} let"},past:{one:"skoraj {{count}} letom",two:"skoraj {{count}} letoma",few:"skoraj {{count}} leti",other:"skoraj {{count}} leti"},future:{one:"skoraj {{count}} leto",two:"skoraj {{count}} leti",few:"skoraj {{count}} leta",other:"skoraj {{count}} let"}}},M=function E(U,B,C){var Y="",J="present";if(C!==null&&C!==void 0&&C.addSuffix)if(C.comparison&&C.comparison>0)J="future",Y="\u010Dez ";else J="past",Y="pred ";var Z=$[U];if(typeof Z==="string")Y+=Z;else{var O=z(B);if(I(Z))Y+=Z[O].replace("{{count}}",String(B));else Y+=Z[J][O].replace("{{count}}",String(B))}return Y};function N(E){return function(){var U=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},B=U.width?String(U.width):E.defaultWidth,C=E.formats[B]||E.formats[E.defaultWidth];return C}}var R={full:"EEEE, dd. MMMM y",long:"dd. MMMM y",medium:"d. MMM y",short:"d. MM. yy"},S={full:"HH:mm:ss zzzz",long:"HH:mm:ss z",medium:"HH:mm:ss",short:"HH:mm"},L={full:"{{date}} {{time}}",long:"{{date}} {{time}}",medium:"{{date}} {{time}}",short:"{{date}} {{time}}"},V={date:N({formats:R,defaultWidth:"full"}),time:N({formats:S,defaultWidth:"full"}),dateTime:N({formats:L,defaultWidth:"full"})},j={lastWeek:function E(U){var B=U.getDay();switch(B){case 0:return"'prej\u0161njo nedeljo ob' p";case 3:return"'prej\u0161njo sredo ob' p";case 6:return"'prej\u0161njo soboto ob' p";default:return"'prej\u0161nji' EEEE 'ob' p"}},yesterday:"'v\u010Deraj ob' p",today:"'danes ob' p",tomorrow:"'jutri ob' p",nextWeek:function E(U){var B=U.getDay();switch(B){case 0:return"'naslednjo nedeljo ob' p";case 3:return"'naslednjo sredo ob' p";case 6:return"'naslednjo soboto ob' p";default:return"'naslednji' EEEE 'ob' p"}},other:"P"},f=function E(U,B,C,Y){var J=j[U];if(typeof J==="function")return J(B);return J};function T(E){return function(U,B){var C=B!==null&&B!==void 0&&B.context?String(B.context):"standalone",Y;if(C==="formatting"&&E.formattingValues){var J=E.defaultFormattingWidth||E.defaultWidth,Z=B!==null&&B!==void 0&&B.width?String(B.width):J;Y=E.formattingValues[Z]||E.formattingValues[J]}else{var O=E.defaultWidth,A=B!==null&&B!==void 0&&B.width?String(B.width):E.defaultWidth;Y=E.values[A]||E.values[O]}var Q=E.argumentCallback?E.argumentCallback(U):U;return Y[Q]}}var _={narrow:["pr. n. \u0161t.","po n. \u0161t."],abbreviated:["pr. n. \u0161t.","po n. \u0161t."],wide:["pred na\u0161im \u0161tetjem","po na\u0161em \u0161tetju"]},v={narrow:["1","2","3","4"],abbreviated:["1. \u010Det.","2. \u010Det.","3. \u010Det.","4. \u010Det."],wide:["1. \u010Detrtletje","2. \u010Detrtletje","3. \u010Detrtletje","4. \u010Detrtletje"]},w={narrow:["j","f","m","a","m","j","j","a","s","o","n","d"],abbreviated:["jan.","feb.","mar.","apr.","maj","jun.","jul.","avg.","sep.","okt.","nov.","dec."],wide:["januar","februar","marec","april","maj","junij","julij","avgust","september","oktober","november","december"]},F={narrow:["n","p","t","s","\u010D","p","s"],short:["ned.","pon.","tor.","sre.","\u010Det.","pet.","sob."],abbreviated:["ned.","pon.","tor.","sre.","\u010Det.","pet.","sob."],wide:["nedelja","ponedeljek","torek","sreda","\u010Detrtek","petek","sobota"]},P={narrow:{am:"d",pm:"p",midnight:"24.00",noon:"12.00",morning:"j",afternoon:"p",evening:"v",night:"n"},abbreviated:{am:"dop.",pm:"pop.",midnight:"poln.",noon:"pold.",morning:"jut.",afternoon:"pop.",evening:"ve\u010D.",night:"no\u010D"},wide:{am:"dop.",pm:"pop.",midnight:"polno\u010D",noon:"poldne",morning:"jutro",afternoon:"popoldne",evening:"ve\u010Der",night:"no\u010D"}},b={narrow:{am:"d",pm:"p",midnight:"24.00",noon:"12.00",morning:"zj",afternoon:"p",evening:"zv",night:"po"},abbreviated:{am:"dop.",pm:"pop.",midnight:"opoln.",noon:"opold.",morning:"zjut.",afternoon:"pop.",evening:"zve\u010D.",night:"pono\u010Di"},wide:{am:"dop.",pm:"pop.",midnight:"opolno\u010Di",noon:"opoldne",morning:"zjutraj",afternoon:"popoldan",evening:"zve\u010Der",night:"pono\u010Di"}},k=function E(U,B){var C=Number(U);return C+"."},h={ordinalNumber:k,era:T({values:_,defaultWidth:"wide"}),quarter:T({values:v,defaultWidth:"wide",argumentCallback:function E(U){return U-1}}),month:T({values:w,defaultWidth:"wide"}),day:T({values:F,defaultWidth:"wide"}),dayPeriod:T({values:P,defaultWidth:"wide",formattingValues:b,defaultFormattingWidth:"wide"})};function q(E){return function(U){var B=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},C=B.width,Y=C&&E.matchPatterns[C]||E.matchPatterns[E.defaultMatchWidth],J=U.match(Y);if(!J)return null;var Z=J[0],O=C&&E.parsePatterns[C]||E.parsePatterns[E.defaultParseWidth],A=Array.isArray(O)?c(O,function(W){return W.test(Z)}):m(O,function(W){return W.test(Z)}),Q;Q=E.valueCallback?E.valueCallback(A):A,Q=B.valueCallback?B.valueCallback(Q):Q;var U0=U.slice(Z.length);return{value:Q,rest:U0}}}var m=function E(U,B){for(var C in U)if(Object.prototype.hasOwnProperty.call(U,C)&&B(U[C]))return C;return},c=function E(U,B){for(var C=0;C<U.length;C++)if(B(U[C]))return C;return};function y(E){return function(U){var B=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},C=U.match(E.matchPattern);if(!C)return null;var Y=C[0],J=U.match(E.parsePattern);if(!J)return null;var Z=E.valueCallback?E.valueCallback(J[0]):J[0];Z=B.valueCallback?B.valueCallback(Z):Z;var O=U.slice(Y.length);return{value:Z,rest:O}}}var p=/^(\d+)\./i,u=/\d+/i,d={abbreviated:/^(pr\. n\. št\.|po n\. št\.)/i,wide:/^(pred Kristusom|pred na[sš]im [sš]tetjem|po Kristusu|po na[sš]em [sš]tetju|na[sš]ega [sš]tetja)/i},g={any:[/^pr/i,/^(po|na[sš]em)/i]},l={narrow:/^[1234]/i,abbreviated:/^[1234]\.\s?[čc]et\.?/i,wide:/^[1234]\. [čc]etrtletje/i},i={any:[/1/i,/2/i,/3/i,/4/i]},n={narrow:/^[jfmasond]/i,abbreviated:/^(jan\.|feb\.|mar\.|apr\.|maj|jun\.|jul\.|avg\.|sep\.|okt\.|nov\.|dec\.)/i,wide:/^(januar|februar|marec|april|maj|junij|julij|avgust|september|oktober|november|december)/i},s={narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],abbreviated:[/^ja/i,/^fe/i,/^mar/i,/^ap/i,/^maj/i,/^jun/i,/^jul/i,/^av/i,/^s/i,/^o/i,/^n/i,/^d/i],wide:[/^ja/i,/^fe/i,/^mar/i,/^ap/i,/^maj/i,/^jun/i,/^jul/i,/^av/i,/^s/i,/^o/i,/^n/i,/^d/i]},o={narrow:/^[nptsčc]/i,short:/^(ned\.|pon\.|tor\.|sre\.|[cč]et\.|pet\.|sob\.)/i,abbreviated:/^(ned\.|pon\.|tor\.|sre\.|[cč]et\.|pet\.|sob\.)/i,wide:/^(nedelja|ponedeljek|torek|sreda|[cč]etrtek|petek|sobota)/i},r={narrow:[/^n/i,/^p/i,/^t/i,/^s/i,/^[cč]/i,/^p/i,/^s/i],any:[/^n/i,/^po/i,/^t/i,/^sr/i,/^[cč]/i,/^pe/i,/^so/i]},e={narrow:/^(d|po?|z?v|n|z?j|24\.00|12\.00)/i,any:/^(dop\.|pop\.|o?poln(\.|o[cč]i?)|o?pold(\.|ne)|z?ve[cč](\.|er)|(po)?no[cč]i?|popold(ne|an)|jut(\.|ro)|zjut(\.|raj))/i},a={narrow:{am:/^d/i,pm:/^p/i,midnight:/^24/i,noon:/^12/i,morning:/^(z?j)/i,afternoon:/^p/i,evening:/^(z?v)/i,night:/^(n|po)/i},any:{am:/^dop\./i,pm:/^pop\./i,midnight:/^o?poln/i,noon:/^o?pold/i,morning:/j/i,afternoon:/^pop\./i,evening:/^z?ve/i,night:/(po)?no/i}},t={ordinalNumber:y({matchPattern:p,parsePattern:u,valueCallback:function E(U){return parseInt(U,10)}}),era:q({matchPatterns:d,defaultMatchWidth:"wide",parsePatterns:g,defaultParseWidth:"any"}),quarter:q({matchPatterns:l,defaultMatchWidth:"wide",parsePatterns:i,defaultParseWidth:"any",valueCallback:function E(U){return U+1}}),month:q({matchPatterns:n,defaultMatchWidth:"wide",parsePatterns:s,defaultParseWidth:"wide"}),day:q({matchPatterns:o,defaultMatchWidth:"wide",parsePatterns:r,defaultParseWidth:"any"}),dayPeriod:q({matchPatterns:e,defaultMatchWidth:"any",parsePatterns:a,defaultParseWidth:"any"})},E0={code:"sl",formatDistance:M,formatLong:V,formatRelative:f,localize:h,match:t,options:{weekStartsOn:1,firstWeekContainsDate:1}};window.dateFns=K(K({},window.dateFns),{},{locale:K(K({},(H=window.dateFns)===null||H===void 0?void 0:H.locale),{},{sl:E0})})})();

//# debugId=9FCBDCCB284AF64764756e2164756e21
