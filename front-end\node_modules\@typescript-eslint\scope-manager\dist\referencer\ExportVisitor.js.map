{"version": 3, "file": "ExportVisitor.js", "sourceRoot": "", "sources": ["../../src/referencer/ExportVisitor.ts"], "names": [], "mappings": ";;;AACA,oDAA0D;AAG1D,uCAAoC;AAOpC,MAAM,aAAc,SAAQ,iBAAO;IACxB,WAAW,CAAa;IACxB,WAAW,CAAa;IAEjC,YAAY,IAAgB,EAAE,UAAsB;QAClD,KAAK,CAAC,UAAU,CAAC,CAAC;QAClB,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;QACxB,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC;IAChC,CAAC;IAED,MAAM,CAAC,KAAK,CAAC,UAAsB,EAAE,IAAgB;QACnD,MAAM,gBAAgB,GAAG,IAAI,aAAa,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;QAC7D,gBAAgB,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IAC/B,CAAC;IAES,UAAU,CAAC,IAAyB;QAC5C,IAAI,IAAI,CAAC,WAAW,CAAC,UAAU,KAAK,MAAM,EAAE,CAAC;YAC3C,qBAAqB;YACrB,wCAAwC;YACxC,IAAI,CAAC,WAAW,CAAC,YAAY,EAAE,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;QACtD,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,WAAW,CAAC,YAAY,EAAE,CAAC,sBAAsB,CAAC,IAAI,CAAC,CAAC;QAC/D,CAAC;IACH,CAAC;IAES,wBAAwB,CAChC,IAAuC;QAEvC,IAAI,IAAI,CAAC,WAAW,CAAC,IAAI,KAAK,sBAAc,CAAC,UAAU,EAAE,CAAC;YACxD,oBAAoB;YACpB,qCAAqC;YACrC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAC/B,CAAC;aAAM,CAAC;YACN,sBAAsB;YACtB,sBAAsB;YACtB,MAAM;YACN,+GAA+G;QACjH,CAAC;IACH,CAAC;IAES,sBAAsB,CAC9B,IAAqC;QAErC,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;YAChB,yBAAyB;YACzB,0EAA0E;YAC1E,OAAO;QACT,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;YACtB,gBAAgB;YAChB,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;QAC3B,CAAC;aAAM,CAAC;YACN,sBAAsB;YACtB,6EAA6E;QAC/E,CAAC;IACH,CAAC;IAES,eAAe,CAAC,IAA8B;QACtD,IAAI,IAAI,CAAC,UAAU,KAAK,MAAM,EAAE,CAAC;YAC/B,qBAAqB;YACrB,wCAAwC;YACxC,EAAE;YACF,mGAAmG;YACnG,kEAAkE;YAClE,IAAI,CAAC,WAAW,CAAC,YAAY,EAAE,CAAC,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC5D,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACzB,CAAC;IACH,CAAC;CACF;AAEQ,sCAAa"}