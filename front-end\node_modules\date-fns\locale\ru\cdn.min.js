var P=function(J,H){var I=Object.keys(J);if(Object.getOwnPropertySymbols){var Y=Object.getOwnPropertySymbols(J);H&&(Y=Y.filter(function(Z){return Object.getOwnPropertyDescriptor(J,Z).enumerable})),I.push.apply(I,Y)}return I},D=function(J){for(var H=1;H<arguments.length;H++){var I=arguments[H]!=null?arguments[H]:{};H%2?P(Object(I),!0).forEach(function(Y){Q4(J,Y,I[Y])}):Object.getOwnPropertyDescriptors?Object.defineProperties(J,Object.getOwnPropertyDescriptors(I)):P(Object(I)).forEach(function(Y){Object.defineProperty(J,Y,Object.getOwnPropertyDescriptor(I,Y))})}return J},Q4=function(J,H,I){if(H=K4(H),H in J)Object.defineProperty(J,H,{value:I,enumerable:!0,configurable:!0,writable:!0});else J[H]=I;return J},K4=function(J){var H=N4(J,"string");return R(H)=="symbol"?H:String(H)},N4=function(J,H){if(R(J)!="object"||!J)return J;var I=J[Symbol.toPrimitive];if(I!==void 0){var Y=I.call(J,H||"default");if(R(Y)!="object")return Y;throw new TypeError("@@toPrimitive must return a primitive value.")}return(H==="string"?String:Number)(J)},R=function(J){return R=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(H){return typeof H}:function(H){return H&&typeof Symbol=="function"&&H.constructor===Symbol&&H!==Symbol.prototype?"symbol":typeof H},R(J)};(function(J){var H=Object.defineProperty,I=function E(C,B){for(var U in B)H(C,U,{get:B[U],enumerable:!0,configurable:!0,set:function G(X){return B[U]=function(){return X}}})},Y=function E(C,B){if(C.one!==void 0&&B===1)return C.one;var U=B%10,G=B%100;if(U===1&&G!==11)return C.singularNominative.replace("{{count}}",String(B));else if(U>=2&&U<=4&&(G<10||G>20))return C.singularGenitive.replace("{{count}}",String(B));else return C.pluralGenitive.replace("{{count}}",String(B))},Z=function E(C){return function(B,U){if(U!==null&&U!==void 0&&U.addSuffix)if(U.comparison&&U.comparison>0)if(C.future)return Y(C.future,B);else return"\u0447\u0435\u0440\u0435\u0437 "+Y(C.regular,B);else if(C.past)return Y(C.past,B);else return Y(C.regular,B)+" \u043D\u0430\u0437\u0430\u0434";else return Y(C.regular,B)}},$={lessThanXSeconds:Z({regular:{one:"\u043C\u0435\u043D\u044C\u0448\u0435 \u0441\u0435\u043A\u0443\u043D\u0434\u044B",singularNominative:"\u043C\u0435\u043D\u044C\u0448\u0435 {{count}} \u0441\u0435\u043A\u0443\u043D\u0434\u044B",singularGenitive:"\u043C\u0435\u043D\u044C\u0448\u0435 {{count}} \u0441\u0435\u043A\u0443\u043D\u0434",pluralGenitive:"\u043C\u0435\u043D\u044C\u0448\u0435 {{count}} \u0441\u0435\u043A\u0443\u043D\u0434"},future:{one:"\u043C\u0435\u043D\u044C\u0448\u0435, \u0447\u0435\u043C \u0447\u0435\u0440\u0435\u0437 \u0441\u0435\u043A\u0443\u043D\u0434\u0443",singularNominative:"\u043C\u0435\u043D\u044C\u0448\u0435, \u0447\u0435\u043C \u0447\u0435\u0440\u0435\u0437 {{count}} \u0441\u0435\u043A\u0443\u043D\u0434\u0443",singularGenitive:"\u043C\u0435\u043D\u044C\u0448\u0435, \u0447\u0435\u043C \u0447\u0435\u0440\u0435\u0437 {{count}} \u0441\u0435\u043A\u0443\u043D\u0434\u044B",pluralGenitive:"\u043C\u0435\u043D\u044C\u0448\u0435, \u0447\u0435\u043C \u0447\u0435\u0440\u0435\u0437 {{count}} \u0441\u0435\u043A\u0443\u043D\u0434"}}),xSeconds:Z({regular:{singularNominative:"{{count}} \u0441\u0435\u043A\u0443\u043D\u0434\u0430",singularGenitive:"{{count}} \u0441\u0435\u043A\u0443\u043D\u0434\u044B",pluralGenitive:"{{count}} \u0441\u0435\u043A\u0443\u043D\u0434"},past:{singularNominative:"{{count}} \u0441\u0435\u043A\u0443\u043D\u0434\u0443 \u043D\u0430\u0437\u0430\u0434",singularGenitive:"{{count}} \u0441\u0435\u043A\u0443\u043D\u0434\u044B \u043D\u0430\u0437\u0430\u0434",pluralGenitive:"{{count}} \u0441\u0435\u043A\u0443\u043D\u0434 \u043D\u0430\u0437\u0430\u0434"},future:{singularNominative:"\u0447\u0435\u0440\u0435\u0437 {{count}} \u0441\u0435\u043A\u0443\u043D\u0434\u0443",singularGenitive:"\u0447\u0435\u0440\u0435\u0437 {{count}} \u0441\u0435\u043A\u0443\u043D\u0434\u044B",pluralGenitive:"\u0447\u0435\u0440\u0435\u0437 {{count}} \u0441\u0435\u043A\u0443\u043D\u0434"}}),halfAMinute:function E(C,B){if(B!==null&&B!==void 0&&B.addSuffix)if(B.comparison&&B.comparison>0)return"\u0447\u0435\u0440\u0435\u0437 \u043F\u043E\u043B\u043C\u0438\u043D\u0443\u0442\u044B";else return"\u043F\u043E\u043B\u043C\u0438\u043D\u0443\u0442\u044B \u043D\u0430\u0437\u0430\u0434";return"\u043F\u043E\u043B\u043C\u0438\u043D\u0443\u0442\u044B"},lessThanXMinutes:Z({regular:{one:"\u043C\u0435\u043D\u044C\u0448\u0435 \u043C\u0438\u043D\u0443\u0442\u044B",singularNominative:"\u043C\u0435\u043D\u044C\u0448\u0435 {{count}} \u043C\u0438\u043D\u0443\u0442\u044B",singularGenitive:"\u043C\u0435\u043D\u044C\u0448\u0435 {{count}} \u043C\u0438\u043D\u0443\u0442",pluralGenitive:"\u043C\u0435\u043D\u044C\u0448\u0435 {{count}} \u043C\u0438\u043D\u0443\u0442"},future:{one:"\u043C\u0435\u043D\u044C\u0448\u0435, \u0447\u0435\u043C \u0447\u0435\u0440\u0435\u0437 \u043C\u0438\u043D\u0443\u0442\u0443",singularNominative:"\u043C\u0435\u043D\u044C\u0448\u0435, \u0447\u0435\u043C \u0447\u0435\u0440\u0435\u0437 {{count}} \u043C\u0438\u043D\u0443\u0442\u0443",singularGenitive:"\u043C\u0435\u043D\u044C\u0448\u0435, \u0447\u0435\u043C \u0447\u0435\u0440\u0435\u0437 {{count}} \u043C\u0438\u043D\u0443\u0442\u044B",pluralGenitive:"\u043C\u0435\u043D\u044C\u0448\u0435, \u0447\u0435\u043C \u0447\u0435\u0440\u0435\u0437 {{count}} \u043C\u0438\u043D\u0443\u0442"}}),xMinutes:Z({regular:{singularNominative:"{{count}} \u043C\u0438\u043D\u0443\u0442\u0430",singularGenitive:"{{count}} \u043C\u0438\u043D\u0443\u0442\u044B",pluralGenitive:"{{count}} \u043C\u0438\u043D\u0443\u0442"},past:{singularNominative:"{{count}} \u043C\u0438\u043D\u0443\u0442\u0443 \u043D\u0430\u0437\u0430\u0434",singularGenitive:"{{count}} \u043C\u0438\u043D\u0443\u0442\u044B \u043D\u0430\u0437\u0430\u0434",pluralGenitive:"{{count}} \u043C\u0438\u043D\u0443\u0442 \u043D\u0430\u0437\u0430\u0434"},future:{singularNominative:"\u0447\u0435\u0440\u0435\u0437 {{count}} \u043C\u0438\u043D\u0443\u0442\u0443",singularGenitive:"\u0447\u0435\u0440\u0435\u0437 {{count}} \u043C\u0438\u043D\u0443\u0442\u044B",pluralGenitive:"\u0447\u0435\u0440\u0435\u0437 {{count}} \u043C\u0438\u043D\u0443\u0442"}}),aboutXHours:Z({regular:{singularNominative:"\u043E\u043A\u043E\u043B\u043E {{count}} \u0447\u0430\u0441\u0430",singularGenitive:"\u043E\u043A\u043E\u043B\u043E {{count}} \u0447\u0430\u0441\u043E\u0432",pluralGenitive:"\u043E\u043A\u043E\u043B\u043E {{count}} \u0447\u0430\u0441\u043E\u0432"},future:{singularNominative:"\u043F\u0440\u0438\u0431\u043B\u0438\u0437\u0438\u0442\u0435\u043B\u044C\u043D\u043E \u0447\u0435\u0440\u0435\u0437 {{count}} \u0447\u0430\u0441",singularGenitive:"\u043F\u0440\u0438\u0431\u043B\u0438\u0437\u0438\u0442\u0435\u043B\u044C\u043D\u043E \u0447\u0435\u0440\u0435\u0437 {{count}} \u0447\u0430\u0441\u0430",pluralGenitive:"\u043F\u0440\u0438\u0431\u043B\u0438\u0437\u0438\u0442\u0435\u043B\u044C\u043D\u043E \u0447\u0435\u0440\u0435\u0437 {{count}} \u0447\u0430\u0441\u043E\u0432"}}),xHours:Z({regular:{singularNominative:"{{count}} \u0447\u0430\u0441",singularGenitive:"{{count}} \u0447\u0430\u0441\u0430",pluralGenitive:"{{count}} \u0447\u0430\u0441\u043E\u0432"}}),xDays:Z({regular:{singularNominative:"{{count}} \u0434\u0435\u043D\u044C",singularGenitive:"{{count}} \u0434\u043D\u044F",pluralGenitive:"{{count}} \u0434\u043D\u0435\u0439"}}),aboutXWeeks:Z({regular:{singularNominative:"\u043E\u043A\u043E\u043B\u043E {{count}} \u043D\u0435\u0434\u0435\u043B\u0438",singularGenitive:"\u043E\u043A\u043E\u043B\u043E {{count}} \u043D\u0435\u0434\u0435\u043B\u044C",pluralGenitive:"\u043E\u043A\u043E\u043B\u043E {{count}} \u043D\u0435\u0434\u0435\u043B\u044C"},future:{singularNominative:"\u043F\u0440\u0438\u0431\u043B\u0438\u0437\u0438\u0442\u0435\u043B\u044C\u043D\u043E \u0447\u0435\u0440\u0435\u0437 {{count}} \u043D\u0435\u0434\u0435\u043B\u044E",singularGenitive:"\u043F\u0440\u0438\u0431\u043B\u0438\u0437\u0438\u0442\u0435\u043B\u044C\u043D\u043E \u0447\u0435\u0440\u0435\u0437 {{count}} \u043D\u0435\u0434\u0435\u043B\u0438",pluralGenitive:"\u043F\u0440\u0438\u0431\u043B\u0438\u0437\u0438\u0442\u0435\u043B\u044C\u043D\u043E \u0447\u0435\u0440\u0435\u0437 {{count}} \u043D\u0435\u0434\u0435\u043B\u044C"}}),xWeeks:Z({regular:{singularNominative:"{{count}} \u043D\u0435\u0434\u0435\u043B\u044F",singularGenitive:"{{count}} \u043D\u0435\u0434\u0435\u043B\u0438",pluralGenitive:"{{count}} \u043D\u0435\u0434\u0435\u043B\u044C"}}),aboutXMonths:Z({regular:{singularNominative:"\u043E\u043A\u043E\u043B\u043E {{count}} \u043C\u0435\u0441\u044F\u0446\u0430",singularGenitive:"\u043E\u043A\u043E\u043B\u043E {{count}} \u043C\u0435\u0441\u044F\u0446\u0435\u0432",pluralGenitive:"\u043E\u043A\u043E\u043B\u043E {{count}} \u043C\u0435\u0441\u044F\u0446\u0435\u0432"},future:{singularNominative:"\u043F\u0440\u0438\u0431\u043B\u0438\u0437\u0438\u0442\u0435\u043B\u044C\u043D\u043E \u0447\u0435\u0440\u0435\u0437 {{count}} \u043C\u0435\u0441\u044F\u0446",singularGenitive:"\u043F\u0440\u0438\u0431\u043B\u0438\u0437\u0438\u0442\u0435\u043B\u044C\u043D\u043E \u0447\u0435\u0440\u0435\u0437 {{count}} \u043C\u0435\u0441\u044F\u0446\u0430",pluralGenitive:"\u043F\u0440\u0438\u0431\u043B\u0438\u0437\u0438\u0442\u0435\u043B\u044C\u043D\u043E \u0447\u0435\u0440\u0435\u0437 {{count}} \u043C\u0435\u0441\u044F\u0446\u0435\u0432"}}),xMonths:Z({regular:{singularNominative:"{{count}} \u043C\u0435\u0441\u044F\u0446",singularGenitive:"{{count}} \u043C\u0435\u0441\u044F\u0446\u0430",pluralGenitive:"{{count}} \u043C\u0435\u0441\u044F\u0446\u0435\u0432"}}),aboutXYears:Z({regular:{singularNominative:"\u043E\u043A\u043E\u043B\u043E {{count}} \u0433\u043E\u0434\u0430",singularGenitive:"\u043E\u043A\u043E\u043B\u043E {{count}} \u043B\u0435\u0442",pluralGenitive:"\u043E\u043A\u043E\u043B\u043E {{count}} \u043B\u0435\u0442"},future:{singularNominative:"\u043F\u0440\u0438\u0431\u043B\u0438\u0437\u0438\u0442\u0435\u043B\u044C\u043D\u043E \u0447\u0435\u0440\u0435\u0437 {{count}} \u0433\u043E\u0434",singularGenitive:"\u043F\u0440\u0438\u0431\u043B\u0438\u0437\u0438\u0442\u0435\u043B\u044C\u043D\u043E \u0447\u0435\u0440\u0435\u0437 {{count}} \u0433\u043E\u0434\u0430",pluralGenitive:"\u043F\u0440\u0438\u0431\u043B\u0438\u0437\u0438\u0442\u0435\u043B\u044C\u043D\u043E \u0447\u0435\u0440\u0435\u0437 {{count}} \u043B\u0435\u0442"}}),xYears:Z({regular:{singularNominative:"{{count}} \u0433\u043E\u0434",singularGenitive:"{{count}} \u0433\u043E\u0434\u0430",pluralGenitive:"{{count}} \u043B\u0435\u0442"}}),overXYears:Z({regular:{singularNominative:"\u0431\u043E\u043B\u044C\u0448\u0435 {{count}} \u0433\u043E\u0434\u0430",singularGenitive:"\u0431\u043E\u043B\u044C\u0448\u0435 {{count}} \u043B\u0435\u0442",pluralGenitive:"\u0431\u043E\u043B\u044C\u0448\u0435 {{count}} \u043B\u0435\u0442"},future:{singularNominative:"\u0431\u043E\u043B\u044C\u0448\u0435, \u0447\u0435\u043C \u0447\u0435\u0440\u0435\u0437 {{count}} \u0433\u043E\u0434",singularGenitive:"\u0431\u043E\u043B\u044C\u0448\u0435, \u0447\u0435\u043C \u0447\u0435\u0440\u0435\u0437 {{count}} \u0433\u043E\u0434\u0430",pluralGenitive:"\u0431\u043E\u043B\u044C\u0448\u0435, \u0447\u0435\u043C \u0447\u0435\u0440\u0435\u0437 {{count}} \u043B\u0435\u0442"}}),almostXYears:Z({regular:{singularNominative:"\u043F\u043E\u0447\u0442\u0438 {{count}} \u0433\u043E\u0434",singularGenitive:"\u043F\u043E\u0447\u0442\u0438 {{count}} \u0433\u043E\u0434\u0430",pluralGenitive:"\u043F\u043E\u0447\u0442\u0438 {{count}} \u043B\u0435\u0442"},future:{singularNominative:"\u043F\u043E\u0447\u0442\u0438 \u0447\u0435\u0440\u0435\u0437 {{count}} \u0433\u043E\u0434",singularGenitive:"\u043F\u043E\u0447\u0442\u0438 \u0447\u0435\u0440\u0435\u0437 {{count}} \u0433\u043E\u0434\u0430",pluralGenitive:"\u043F\u043E\u0447\u0442\u0438 \u0447\u0435\u0440\u0435\u0437 {{count}} \u043B\u0435\u0442"}})},O=function E(C,B,U){return $[C](B,U)};function j(E){return function(){var C=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},B=C.width?String(C.width):E.defaultWidth,U=E.formats[B]||E.formats[E.defaultWidth];return U}}var L={full:"EEEE, d MMMM y '\u0433.'",long:"d MMMM y '\u0433.'",medium:"d MMM y '\u0433.'",short:"dd.MM.y"},w={full:"H:mm:ss zzzz",long:"H:mm:ss z",medium:"H:mm:ss",short:"H:mm"},F={any:"{{date}}, {{time}}"},b={date:j({formats:L,defaultWidth:"full"}),time:j({formats:w,defaultWidth:"full"}),dateTime:j({formats:F,defaultWidth:"any"})};function f(E){var C=Object.prototype.toString.call(E);if(E instanceof Date||R(E)==="object"&&C==="[object Date]")return new E.constructor(+E);else if(typeof E==="number"||C==="[object Number]"||typeof E==="string"||C==="[object String]")return new Date(E);else return new Date(NaN)}function _(){return W}function M4(E){W=E}var W={};function v(E,C){var B,U,G,X,A,q,K=_(),Q=(B=(U=(G=(X=C===null||C===void 0?void 0:C.weekStartsOn)!==null&&X!==void 0?X:C===null||C===void 0||(A=C.locale)===null||A===void 0||(A=A.options)===null||A===void 0?void 0:A.weekStartsOn)!==null&&G!==void 0?G:K.weekStartsOn)!==null&&U!==void 0?U:(q=K.locale)===null||q===void 0||(q=q.options)===null||q===void 0?void 0:q.weekStartsOn)!==null&&B!==void 0?B:0,N=f(E),M=N.getDay(),q4=(M<Q?7:0)+M-Q;return N.setDate(N.getDate()-q4),N.setHours(0,0,0,0),N}function S(E,C,B){var U=v(E,B),G=v(C,B);return+U===+G}var u=function E(C){var B=T[C];switch(C){case 0:return"'\u0432 \u043F\u0440\u043E\u0448\u043B\u043E\u0435 "+B+" \u0432' p";case 1:case 2:case 4:return"'\u0432 \u043F\u0440\u043E\u0448\u043B\u044B\u0439 "+B+" \u0432' p";case 3:case 5:case 6:return"'\u0432 \u043F\u0440\u043E\u0448\u043B\u0443\u044E "+B+" \u0432' p"}},z=function E(C){var B=T[C];if(C===2)return"'\u0432\u043E "+B+" \u0432' p";else return"'\u0432 "+B+" \u0432' p"},h=function E(C){var B=T[C];switch(C){case 0:return"'\u0432 \u0441\u043B\u0435\u0434\u0443\u044E\u0449\u0435\u0435 "+B+" \u0432' p";case 1:case 2:case 4:return"'\u0432 \u0441\u043B\u0435\u0434\u0443\u044E\u0449\u0438\u0439 "+B+" \u0432' p";case 3:case 5:case 6:return"'\u0432 \u0441\u043B\u0435\u0434\u0443\u044E\u0449\u0443\u044E "+B+" \u0432' p"}},T=["\u0432\u043E\u0441\u043A\u0440\u0435\u0441\u0435\u043D\u044C\u0435","\u043F\u043E\u043D\u0435\u0434\u0435\u043B\u044C\u043D\u0438\u043A","\u0432\u0442\u043E\u0440\u043D\u0438\u043A","\u0441\u0440\u0435\u0434\u0443","\u0447\u0435\u0442\u0432\u0435\u0440\u0433","\u043F\u044F\u0442\u043D\u0438\u0446\u0443","\u0441\u0443\u0431\u0431\u043E\u0442\u0443"],k={lastWeek:function E(C,B,U){var G=C.getDay();if(S(C,B,U))return z(G);else return u(G)},yesterday:"'\u0432\u0447\u0435\u0440\u0430 \u0432' p",today:"'\u0441\u0435\u0433\u043E\u0434\u043D\u044F \u0432' p",tomorrow:"'\u0437\u0430\u0432\u0442\u0440\u0430 \u0432' p",nextWeek:function E(C,B,U){var G=C.getDay();if(S(C,B,U))return z(G);else return h(G)},other:"P"},y=function E(C,B,U,G){var X=k[C];if(typeof X==="function")return X(B,U,G);return X};function V(E){return function(C,B){var U=B!==null&&B!==void 0&&B.context?String(B.context):"standalone",G;if(U==="formatting"&&E.formattingValues){var X=E.defaultFormattingWidth||E.defaultWidth,A=B!==null&&B!==void 0&&B.width?String(B.width):X;G=E.formattingValues[A]||E.formattingValues[X]}else{var q=E.defaultWidth,K=B!==null&&B!==void 0&&B.width?String(B.width):E.defaultWidth;G=E.values[K]||E.values[q]}var Q=E.argumentCallback?E.argumentCallback(C):C;return G[Q]}}var m={narrow:["\u0434\u043E \u043D.\u044D.","\u043D.\u044D."],abbreviated:["\u0434\u043E \u043D. \u044D.","\u043D. \u044D."],wide:["\u0434\u043E \u043D\u0430\u0448\u0435\u0439 \u044D\u0440\u044B","\u043D\u0430\u0448\u0435\u0439 \u044D\u0440\u044B"]},g={narrow:["1","2","3","4"],abbreviated:["1-\u0439 \u043A\u0432.","2-\u0439 \u043A\u0432.","3-\u0439 \u043A\u0432.","4-\u0439 \u043A\u0432."],wide:["1-\u0439 \u043A\u0432\u0430\u0440\u0442\u0430\u043B","2-\u0439 \u043A\u0432\u0430\u0440\u0442\u0430\u043B","3-\u0439 \u043A\u0432\u0430\u0440\u0442\u0430\u043B","4-\u0439 \u043A\u0432\u0430\u0440\u0442\u0430\u043B"]},c={narrow:["\u042F","\u0424","\u041C","\u0410","\u041C","\u0418","\u0418","\u0410","\u0421","\u041E","\u041D","\u0414"],abbreviated:["\u044F\u043D\u0432.","\u0444\u0435\u0432.","\u043C\u0430\u0440\u0442","\u0430\u043F\u0440.","\u043C\u0430\u0439","\u0438\u044E\u043D\u044C","\u0438\u044E\u043B\u044C","\u0430\u0432\u0433.","\u0441\u0435\u043D\u0442.","\u043E\u043A\u0442.","\u043D\u043E\u044F\u0431.","\u0434\u0435\u043A."],wide:["\u044F\u043D\u0432\u0430\u0440\u044C","\u0444\u0435\u0432\u0440\u0430\u043B\u044C","\u043C\u0430\u0440\u0442","\u0430\u043F\u0440\u0435\u043B\u044C","\u043C\u0430\u0439","\u0438\u044E\u043D\u044C","\u0438\u044E\u043B\u044C","\u0430\u0432\u0433\u0443\u0441\u0442","\u0441\u0435\u043D\u0442\u044F\u0431\u0440\u044C","\u043E\u043A\u0442\u044F\u0431\u0440\u044C","\u043D\u043E\u044F\u0431\u0440\u044C","\u0434\u0435\u043A\u0430\u0431\u0440\u044C"]},l={narrow:["\u042F","\u0424","\u041C","\u0410","\u041C","\u0418","\u0418","\u0410","\u0421","\u041E","\u041D","\u0414"],abbreviated:["\u044F\u043D\u0432.","\u0444\u0435\u0432.","\u043C\u0430\u0440.","\u0430\u043F\u0440.","\u043C\u0430\u044F","\u0438\u044E\u043D.","\u0438\u044E\u043B.","\u0430\u0432\u0433.","\u0441\u0435\u043D\u0442.","\u043E\u043A\u0442.","\u043D\u043E\u044F\u0431.","\u0434\u0435\u043A."],wide:["\u044F\u043D\u0432\u0430\u0440\u044F","\u0444\u0435\u0432\u0440\u0430\u043B\u044F","\u043C\u0430\u0440\u0442\u0430","\u0430\u043F\u0440\u0435\u043B\u044F","\u043C\u0430\u044F","\u0438\u044E\u043D\u044F","\u0438\u044E\u043B\u044F","\u0430\u0432\u0433\u0443\u0441\u0442\u0430","\u0441\u0435\u043D\u0442\u044F\u0431\u0440\u044F","\u043E\u043A\u0442\u044F\u0431\u0440\u044F","\u043D\u043E\u044F\u0431\u0440\u044F","\u0434\u0435\u043A\u0430\u0431\u0440\u044F"]},p={narrow:["\u0412","\u041F","\u0412","\u0421","\u0427","\u041F","\u0421"],short:["\u0432\u0441","\u043F\u043D","\u0432\u0442","\u0441\u0440","\u0447\u0442","\u043F\u0442","\u0441\u0431"],abbreviated:["\u0432\u0441\u043A","\u043F\u043D\u0434","\u0432\u0442\u0440","\u0441\u0440\u0434","\u0447\u0442\u0432","\u043F\u0442\u043D","\u0441\u0443\u0431"],wide:["\u0432\u043E\u0441\u043A\u0440\u0435\u0441\u0435\u043D\u044C\u0435","\u043F\u043E\u043D\u0435\u0434\u0435\u043B\u044C\u043D\u0438\u043A","\u0432\u0442\u043E\u0440\u043D\u0438\u043A","\u0441\u0440\u0435\u0434\u0430","\u0447\u0435\u0442\u0432\u0435\u0440\u0433","\u043F\u044F\u0442\u043D\u0438\u0446\u0430","\u0441\u0443\u0431\u0431\u043E\u0442\u0430"]},d={narrow:{am:"\u0414\u041F",pm:"\u041F\u041F",midnight:"\u043F\u043E\u043B\u043D.",noon:"\u043F\u043E\u043B\u0434.",morning:"\u0443\u0442\u0440\u043E",afternoon:"\u0434\u0435\u043D\u044C",evening:"\u0432\u0435\u0447.",night:"\u043D\u043E\u0447\u044C"},abbreviated:{am:"\u0414\u041F",pm:"\u041F\u041F",midnight:"\u043F\u043E\u043B\u043D.",noon:"\u043F\u043E\u043B\u0434.",morning:"\u0443\u0442\u0440\u043E",afternoon:"\u0434\u0435\u043D\u044C",evening:"\u0432\u0435\u0447.",night:"\u043D\u043E\u0447\u044C"},wide:{am:"\u0414\u041F",pm:"\u041F\u041F",midnight:"\u043F\u043E\u043B\u043D\u043E\u0447\u044C",noon:"\u043F\u043E\u043B\u0434\u0435\u043D\u044C",morning:"\u0443\u0442\u0440\u043E",afternoon:"\u0434\u0435\u043D\u044C",evening:"\u0432\u0435\u0447\u0435\u0440",night:"\u043D\u043E\u0447\u044C"}},i={narrow:{am:"\u0414\u041F",pm:"\u041F\u041F",midnight:"\u043F\u043E\u043B\u043D.",noon:"\u043F\u043E\u043B\u0434.",morning:"\u0443\u0442\u0440\u0430",afternoon:"\u0434\u043D\u044F",evening:"\u0432\u0435\u0447.",night:"\u043D\u043E\u0447\u0438"},abbreviated:{am:"\u0414\u041F",pm:"\u041F\u041F",midnight:"\u043F\u043E\u043B\u043D.",noon:"\u043F\u043E\u043B\u0434.",morning:"\u0443\u0442\u0440\u0430",afternoon:"\u0434\u043D\u044F",evening:"\u0432\u0435\u0447.",night:"\u043D\u043E\u0447\u0438"},wide:{am:"\u0414\u041F",pm:"\u041F\u041F",midnight:"\u043F\u043E\u043B\u043D\u043E\u0447\u044C",noon:"\u043F\u043E\u043B\u0434\u0435\u043D\u044C",morning:"\u0443\u0442\u0440\u0430",afternoon:"\u0434\u043D\u044F",evening:"\u0432\u0435\u0447\u0435\u0440\u0430",night:"\u043D\u043E\u0447\u0438"}},r=function E(C,B){var U=Number(C),G=B===null||B===void 0?void 0:B.unit,X;if(G==="date")X="-\u0435";else if(G==="week"||G==="minute"||G==="second")X="-\u044F";else X="-\u0439";return U+X},n={ordinalNumber:r,era:V({values:m,defaultWidth:"wide"}),quarter:V({values:g,defaultWidth:"wide",argumentCallback:function E(C){return C-1}}),month:V({values:c,defaultWidth:"wide",formattingValues:l,defaultFormattingWidth:"wide"}),day:V({values:p,defaultWidth:"wide"}),dayPeriod:V({values:d,defaultWidth:"any",formattingValues:i,defaultFormattingWidth:"wide"})};function x(E){return function(C){var B=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},U=B.width,G=U&&E.matchPatterns[U]||E.matchPatterns[E.defaultMatchWidth],X=C.match(G);if(!X)return null;var A=X[0],q=U&&E.parsePatterns[U]||E.parsePatterns[E.defaultParseWidth],K=Array.isArray(q)?a(q,function(M){return M.test(A)}):s(q,function(M){return M.test(A)}),Q;Q=E.valueCallback?E.valueCallback(K):K,Q=B.valueCallback?B.valueCallback(Q):Q;var N=C.slice(A.length);return{value:Q,rest:N}}}var s=function E(C,B){for(var U in C)if(Object.prototype.hasOwnProperty.call(C,U)&&B(C[U]))return U;return},a=function E(C,B){for(var U=0;U<C.length;U++)if(B(C[U]))return U;return};function o(E){return function(C){var B=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},U=C.match(E.matchPattern);if(!U)return null;var G=U[0],X=C.match(E.parsePattern);if(!X)return null;var A=E.valueCallback?E.valueCallback(X[0]):X[0];A=B.valueCallback?B.valueCallback(A):A;var q=C.slice(G.length);return{value:A,rest:q}}}var t=/^(\d+)(-?(е|я|й|ое|ье|ая|ья|ый|ой|ий|ый))?/i,e=/\d+/i,B4={narrow:/^((до )?н\.?\s?э\.?)/i,abbreviated:/^((до )?н\.?\s?э\.?)/i,wide:/^(до нашей эры|нашей эры|наша эра)/i},C4={any:[/^д/i,/^н/i]},E4={narrow:/^[1234]/i,abbreviated:/^[1234](-?[ыои]?й?)? кв.?/i,wide:/^[1234](-?[ыои]?й?)? квартал/i},U4={any:[/1/i,/2/i,/3/i,/4/i]},G4={narrow:/^[яфмаисонд]/i,abbreviated:/^(янв|фев|март?|апр|ма[йя]|июн[ья]?|июл[ья]?|авг|сент?|окт|нояб?|дек)\.?/i,wide:/^(январ[ья]|феврал[ья]|марта?|апрел[ья]|ма[йя]|июн[ья]|июл[ья]|августа?|сентябр[ья]|октябр[ья]|октябр[ья]|ноябр[ья]|декабр[ья])/i},H4={narrow:[/^я/i,/^ф/i,/^м/i,/^а/i,/^м/i,/^и/i,/^и/i,/^а/i,/^с/i,/^о/i,/^н/i,/^я/i],any:[/^я/i,/^ф/i,/^мар/i,/^ап/i,/^ма[йя]/i,/^июн/i,/^июл/i,/^ав/i,/^с/i,/^о/i,/^н/i,/^д/i]},J4={narrow:/^[впсч]/i,short:/^(вс|во|пн|по|вт|ср|чт|че|пт|пя|сб|су)\.?/i,abbreviated:/^(вск|вос|пнд|пон|втр|вто|срд|сре|чтв|чет|птн|пят|суб).?/i,wide:/^(воскресень[ея]|понедельника?|вторника?|сред[аы]|четверга?|пятниц[аы]|суббот[аы])/i},X4={narrow:[/^в/i,/^п/i,/^в/i,/^с/i,/^ч/i,/^п/i,/^с/i],any:[/^в[ос]/i,/^п[он]/i,/^в/i,/^ср/i,/^ч/i,/^п[ят]/i,/^с[уб]/i]},Y4={narrow:/^([дп]п|полн\.?|полд\.?|утр[оа]|день|дня|веч\.?|ноч[ьи])/i,abbreviated:/^([дп]п|полн\.?|полд\.?|утр[оа]|день|дня|веч\.?|ноч[ьи])/i,wide:/^([дп]п|полночь|полдень|утр[оа]|день|дня|вечера?|ноч[ьи])/i},Z4={any:{am:/^дп/i,pm:/^пп/i,midnight:/^полн/i,noon:/^полд/i,morning:/^у/i,afternoon:/^д[ен]/i,evening:/^в/i,night:/^н/i}},A4={ordinalNumber:o({matchPattern:t,parsePattern:e,valueCallback:function E(C){return parseInt(C,10)}}),era:x({matchPatterns:B4,defaultMatchWidth:"wide",parsePatterns:C4,defaultParseWidth:"any"}),quarter:x({matchPatterns:E4,defaultMatchWidth:"wide",parsePatterns:U4,defaultParseWidth:"any",valueCallback:function E(C){return C+1}}),month:x({matchPatterns:G4,defaultMatchWidth:"wide",parsePatterns:H4,defaultParseWidth:"any"}),day:x({matchPatterns:J4,defaultMatchWidth:"wide",parsePatterns:X4,defaultParseWidth:"any"}),dayPeriod:x({matchPatterns:Y4,defaultMatchWidth:"wide",parsePatterns:Z4,defaultParseWidth:"any"})},I4={code:"ru",formatDistance:O,formatLong:b,formatRelative:y,localize:n,match:A4,options:{weekStartsOn:1,firstWeekContainsDate:1}};window.dateFns=D(D({},window.dateFns),{},{locale:D(D({},(J=window.dateFns)===null||J===void 0?void 0:J.locale),{},{ru:I4})})})();

//# debugId=0DE28BDA4A37D3E864756e2164756e21
