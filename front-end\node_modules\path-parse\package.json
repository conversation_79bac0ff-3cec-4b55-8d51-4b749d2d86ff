{"name": "path-parse", "version": "1.0.7", "description": "Node.js path.parse() ponyfill", "main": "index.js", "scripts": {"test": "node test.js"}, "repository": {"type": "git", "url": "https://github.com/jbgutierrez/path-parse.git"}, "keywords": ["path", "paths", "file", "dir", "parse", "built-in", "util", "utils", "core", "ponyfill", "polyfill", "shim"], "author": "<PERSON> <http://jbgutierrez.info>", "license": "MIT", "bugs": {"url": "https://github.com/jbgutierrez/path-parse/issues"}, "homepage": "https://github.com/jbgutierrez/path-parse#readme"}