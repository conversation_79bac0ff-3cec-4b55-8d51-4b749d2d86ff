var Q=function(G){return Q=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(B){return typeof B}:function(B){return B&&typeof Symbol=="function"&&B.constructor===Symbol&&B!==Symbol.prototype?"symbol":typeof B},Q(G)},D=function(G,B){var H=Object.keys(G);if(Object.getOwnPropertySymbols){var Y=Object.getOwnPropertySymbols(G);B&&(Y=Y.filter(function(K){return Object.getOwnPropertyDescriptor(G,K).enumerable})),H.push.apply(H,Y)}return H},W=function(G){for(var B=1;B<arguments.length;B++){var H=arguments[B]!=null?arguments[B]:{};B%2?D(Object(H),!0).forEach(function(Y){E1(G,Y,H[Y])}):Object.getOwnPropertyDescriptors?Object.defineProperties(G,Object.getOwnPropertyDescriptors(H)):D(Object(H)).forEach(function(Y){Object.defineProperty(G,Y,Object.getOwnPropertyDescriptor(H,Y))})}return G},E1=function(G,B,H){if(B=x1(B),B in G)Object.defineProperty(G,B,{value:H,enumerable:!0,configurable:!0,writable:!0});else G[B]=H;return G},x1=function(G){var B=C1(G,"string");return Q(B)=="symbol"?B:String(B)},C1=function(G,B){if(Q(G)!="object"||!G)return G;var H=G[Symbol.toPrimitive];if(H!==void 0){var Y=H.call(G,B||"default");if(Q(Y)!="object")return Y;throw new TypeError("@@toPrimitive must return a primitive value.")}return(B==="string"?String:Number)(G)};(function(G){var B=Object.defineProperty,H=function E(C,x){for(var X in x)B(C,X,{get:x[X],enumerable:!0,configurable:!0,set:function J(U){return x[X]=function(){return U}}})},Y={lessThanXSeconds:{one:"minna en 1 sek\xFAnda",other:"minna en {{count}} sek\xFAndur"},xSeconds:{one:"1 sek\xFAnda",other:"{{count}} sek\xFAndur"},halfAMinute:"h\xE1lf m\xEDn\xFAta",lessThanXMinutes:{one:"minna en 1 m\xEDn\xFAta",other:"minna en {{count}} m\xEDn\xFAtur"},xMinutes:{one:"1 m\xEDn\xFAta",other:"{{count}} m\xEDn\xFAtur"},aboutXHours:{one:"u.\xFE.b. 1 klukkustund",other:"u.\xFE.b. {{count}} klukkustundir"},xHours:{one:"1 klukkustund",other:"{{count}} klukkustundir"},xDays:{one:"1 dagur",other:"{{count}} dagar"},aboutXWeeks:{one:"um viku",other:"um {{count}} vikur"},xWeeks:{one:"1 viku",other:"{{count}} vikur"},aboutXMonths:{one:"u.\xFE.b. 1 m\xE1nu\xF0ur",other:"u.\xFE.b. {{count}} m\xE1nu\xF0ir"},xMonths:{one:"1 m\xE1nu\xF0ur",other:"{{count}} m\xE1nu\xF0ir"},aboutXYears:{one:"u.\xFE.b. 1 \xE1r",other:"u.\xFE.b. {{count}} \xE1r"},xYears:{one:"1 \xE1r",other:"{{count}} \xE1r"},overXYears:{one:"meira en 1 \xE1r",other:"meira en {{count}} \xE1r"},almostXYears:{one:"n\xE6stum 1 \xE1r",other:"n\xE6stum {{count}} \xE1r"}},K=function E(C,x,X){var J,U=Y[C];if(typeof U==="string")J=U;else if(x===1)J=U.one;else J=U.other.replace("{{count}}",x.toString());if(X!==null&&X!==void 0&&X.addSuffix)if(X.comparison&&X.comparison>0)return"\xED "+J;else return J+" s\xED\xF0an";return J};function N(E){return function(){var C=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},x=C.width?String(C.width):E.defaultWidth,X=E.formats[x]||E.formats[E.defaultWidth];return X}}var S={full:"EEEE, do MMMM y",long:"do MMMM y",medium:"do MMM y",short:"d.MM.y"},$={full:"'kl'. HH:mm:ss zzzz",long:"HH:mm:ss z",medium:"HH:mm:ss",short:"HH:mm"},M={full:"{{date}} 'kl.' {{time}}",long:"{{date}} 'kl.' {{time}}",medium:"{{date}} {{time}}",short:"{{date}} {{time}}"},R={date:N({formats:S,defaultWidth:"full"}),time:N({formats:$,defaultWidth:"full"}),dateTime:N({formats:M,defaultWidth:"full"})},L={lastWeek:"'s\xED\xF0asta' dddd 'kl.' p",yesterday:"'\xED g\xE6r kl.' p",today:"'\xED dag kl.' p",tomorrow:"'\xE1 morgun kl.' p",nextWeek:"dddd 'kl.' p",other:"P"},V=function E(C,x,X,J){return L[C]};function A(E){return function(C,x){var X=x!==null&&x!==void 0&&x.context?String(x.context):"standalone",J;if(X==="formatting"&&E.formattingValues){var U=E.defaultFormattingWidth||E.defaultWidth,Z=x!==null&&x!==void 0&&x.width?String(x.width):U;J=E.formattingValues[Z]||E.formattingValues[U]}else{var I=E.defaultWidth,q=x!==null&&x!==void 0&&x.width?String(x.width):E.defaultWidth;J=E.values[q]||E.values[I]}var T=E.argumentCallback?E.argumentCallback(C):C;return J[T]}}var f={narrow:["f.Kr.","e.Kr."],abbreviated:["f.Kr.","e.Kr."],wide:["fyrir Krist","eftir Krist"]},j={narrow:["1","2","3","4"],abbreviated:["1F","2F","3F","4F"],wide:["1. fj\xF3r\xF0ungur","2. fj\xF3r\xF0ungur","3. fj\xF3r\xF0ungur","4. fj\xF3r\xF0ungur"]},v={narrow:["J","F","M","A","M","J","J","\xC1","S","\xD3","N","D"],abbreviated:["jan.","feb.","mars","apr\xEDl","ma\xED","j\xFAn\xED","j\xFAl\xED","\xE1g\xFAst","sept.","okt.","n\xF3v.","des."],wide:["jan\xFAar","febr\xFAar","mars","apr\xEDl","ma\xED","j\xFAn\xED","j\xFAl\xED","\xE1g\xFAst","september","okt\xF3ber","n\xF3vember","desember"]},_={narrow:["S","M","\xDE","M","F","F","L"],short:["Su","M\xE1","\xDEr","Mi","Fi","F\xF6","La"],abbreviated:["sun.","m\xE1n.","\xFEri.","mi\xF0.","fim.","f\xF6s.","lau."],wide:["sunnudagur","m\xE1nudagur","\xFEri\xF0judagur","mi\xF0vikudagur","fimmtudagur","f\xF6studagur","laugardagur"]},F={narrow:{am:"f",pm:"e",midnight:"mi\xF0n\xE6tti",noon:"h\xE1degi",morning:"morgunn",afternoon:"s\xED\xF0degi",evening:"kv\xF6ld",night:"n\xF3tt"},abbreviated:{am:"f.h.",pm:"e.h.",midnight:"mi\xF0n\xE6tti",noon:"h\xE1degi",morning:"morgunn",afternoon:"s\xED\xF0degi",evening:"kv\xF6ld",night:"n\xF3tt"},wide:{am:"fyrir h\xE1degi",pm:"eftir h\xE1degi",midnight:"mi\xF0n\xE6tti",noon:"h\xE1degi",morning:"morgunn",afternoon:"s\xED\xF0degi",evening:"kv\xF6ld",night:"n\xF3tt"}},w={narrow:{am:"f",pm:"e",midnight:"\xE1 mi\xF0n\xE6tti",noon:"\xE1 h\xE1degi",morning:"a\xF0 morgni",afternoon:"s\xED\xF0degis",evening:"um kv\xF6ld",night:"um n\xF3tt"},abbreviated:{am:"f.h.",pm:"e.h.",midnight:"\xE1 mi\xF0n\xE6tti",noon:"\xE1 h\xE1degi",morning:"a\xF0 morgni",afternoon:"s\xED\xF0degis",evening:"um kv\xF6ld",night:"um n\xF3tt"},wide:{am:"fyrir h\xE1degi",pm:"eftir h\xE1degi",midnight:"\xE1 mi\xF0n\xE6tti",noon:"\xE1 h\xE1degi",morning:"a\xF0 morgni",afternoon:"s\xED\xF0degis",evening:"um kv\xF6ld",night:"um n\xF3tt"}},P=function E(C,x){var X=Number(C);return X+"."},m={ordinalNumber:P,era:A({values:f,defaultWidth:"wide"}),quarter:A({values:j,defaultWidth:"wide",argumentCallback:function E(C){return C-1}}),month:A({values:v,defaultWidth:"wide"}),day:A({values:_,defaultWidth:"wide"}),dayPeriod:A({values:F,defaultWidth:"wide",formattingValues:w,defaultFormattingWidth:"wide"})};function O(E){return function(C){var x=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},X=x.width,J=X&&E.matchPatterns[X]||E.matchPatterns[E.defaultMatchWidth],U=C.match(J);if(!U)return null;var Z=U[0],I=X&&E.parsePatterns[X]||E.parsePatterns[E.defaultParseWidth],q=Array.isArray(I)?b(I,function(z){return z.test(Z)}):k(I,function(z){return z.test(Z)}),T;T=E.valueCallback?E.valueCallback(q):q,T=x.valueCallback?x.valueCallback(T):T;var t=C.slice(Z.length);return{value:T,rest:t}}}var k=function E(C,x){for(var X in C)if(Object.prototype.hasOwnProperty.call(C,X)&&x(C[X]))return X;return},b=function E(C,x){for(var X=0;X<C.length;X++)if(x(C[X]))return X;return};function h(E){return function(C){var x=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},X=C.match(E.matchPattern);if(!X)return null;var J=X[0],U=C.match(E.parsePattern);if(!U)return null;var Z=E.valueCallback?E.valueCallback(U[0]):U[0];Z=x.valueCallback?x.valueCallback(Z):Z;var I=C.slice(J.length);return{value:Z,rest:I}}}var c=/^(\d+)(\.)?/i,y=/\d+(\.)?/i,d={narrow:/^(f\.Kr\.|e\.Kr\.)/i,abbreviated:/^(f\.Kr\.|e\.Kr\.)/i,wide:/^(fyrir Krist|eftir Krist)/i},p={any:[/^(f\.Kr\.)/i,/^(e\.Kr\.)/i]},u={narrow:/^[1234]\.?/i,abbreviated:/^q[1234]\.?/i,wide:/^[1234]\.? fjórðungur/i},g={any:[/1\.?/i,/2\.?/i,/3\.?/i,/4\.?/i]},l={narrow:/^[jfmásónd]/i,abbreviated:/^(jan\.|feb\.|mars\.|apríl\.|maí|júní|júlí|águst|sep\.|oct\.|nov\.|dec\.)/i,wide:/^(januar|febrúar|mars|apríl|maí|júní|júlí|águst|september|október|nóvember|desember)/i},i={narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^á/i,/^s/i,/^ó/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^mar/i,/^ap/i,/^maí/i,/^jún/i,/^júl/i,/^áu/i,/^s/i,/^ó/i,/^n/i,/^d/i]},n={narrow:/^[smtwf]/i,short:/^(su|má|þr|mi|fi|fö|la)/i,abbreviated:/^(sun|mán|þri|mið|fim|fös|lau)\.?/i,wide:/^(sunnudagur|mánudagur|þriðjudagur|miðvikudagur|fimmtudagur|föstudagur|laugardagur)/i},s={narrow:[/^s/i,/^m/i,/^þ/i,/^m/i,/^f/i,/^f/i,/^l/i],any:[/^su/i,/^má/i,/^þr/i,/^mi/i,/^fi/i,/^fö/i,/^la/i]},o={narrow:/^(f|e|síðdegis|(á|að|um) (morgni|kvöld|nótt|miðnætti))/i,any:/^(fyrir hádegi|eftir hádegi|[ef]\.?h\.?|síðdegis|morgunn|(á|að|um) (morgni|kvöld|nótt|miðnætti))/i},r={any:{am:/^f/i,pm:/^e/i,midnight:/^mi/i,noon:/^há/i,morning:/morgunn/i,afternoon:/síðdegi/i,evening:/kvöld/i,night:/nótt/i}},a={ordinalNumber:h({matchPattern:c,parsePattern:y,valueCallback:function E(C){return parseInt(C,10)}}),era:O({matchPatterns:d,defaultMatchWidth:"wide",parsePatterns:p,defaultParseWidth:"any"}),quarter:O({matchPatterns:u,defaultMatchWidth:"wide",parsePatterns:g,defaultParseWidth:"any",valueCallback:function E(C){return C+1}}),month:O({matchPatterns:l,defaultMatchWidth:"wide",parsePatterns:i,defaultParseWidth:"any"}),day:O({matchPatterns:n,defaultMatchWidth:"wide",parsePatterns:s,defaultParseWidth:"any"}),dayPeriod:O({matchPatterns:o,defaultMatchWidth:"any",parsePatterns:r,defaultParseWidth:"any"})},e={code:"is",formatDistance:K,formatLong:R,formatRelative:V,localize:m,match:a,options:{weekStartsOn:1,firstWeekContainsDate:4}};window.dateFns=W(W({},window.dateFns),{},{locale:W(W({},(G=window.dateFns)===null||G===void 0?void 0:G.locale),{},{is:e})})})();

//# debugId=D70DA1DDD2D3137564756e2164756e21
