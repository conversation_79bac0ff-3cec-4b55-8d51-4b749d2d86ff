"use strict";
exports.isYesterday = isYesterday;
var _index = require("./constructNow.js");
var _index2 = require("./isSameDay.js");
var _index3 = require("./subDays.js");

/**
 * @name isYesterday
 * @category Day Helpers
 * @summary Is the given date yesterday?
 * @pure false
 *
 * @description
 * Is the given date yesterday?
 *
 * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).
 *
 * @param date - The date to check
 *
 * @returns The date is yesterday
 *
 * @example
 * // If today is 6 October 2014, is 5 October 14:00:00 yesterday?
 * const result = isYesterday(new Date(2014, 9, 5, 14, 0))
 * //=> true
 */
function isYesterday(date) {
  return (0, _index2.isSameDay)(
    date,
    (0, _index3.subDays)((0, _index.constructNow)(date), 1),
  );
}
