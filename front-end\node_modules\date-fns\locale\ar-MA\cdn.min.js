var K=function(H){return K=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(U){return typeof U}:function(U){return U&&typeof Symbol=="function"&&U.constructor===Symbol&&U!==Symbol.prototype?"symbol":typeof U},K(H)},G=function(H,U){var X=Object.keys(H);if(Object.getOwnPropertySymbols){var Z=Object.getOwnPropertySymbols(H);U&&(Z=Z.filter(function(N){return Object.getOwnPropertyDescriptor(H,N).enumerable})),X.push.apply(X,Z)}return X},x=function(H){for(var U=1;U<arguments.length;U++){var X=arguments[U]!=null?arguments[U]:{};U%2?G(Object(X),!0).forEach(function(Z){B0(H,Z,X[Z])}):Object.getOwnPropertyDescriptors?Object.defineProperties(H,Object.getOwnPropertyDescriptors(X)):G(Object(X)).forEach(function(Z){Object.defineProperty(H,Z,Object.getOwnPropertyDescriptor(X,Z))})}return H},B0=function(H,U,X){if(U=A0(U),U in H)Object.defineProperty(H,U,{value:X,enumerable:!0,configurable:!0,writable:!0});else H[U]=X;return H},A0=function(H){var U=C0(H,"string");return K(U)=="symbol"?U:String(U)},C0=function(H,U){if(K(H)!="object"||!H)return H;var X=H[Symbol.toPrimitive];if(X!==void 0){var Z=X.call(H,U||"default");if(K(Z)!="object")return Z;throw new TypeError("@@toPrimitive must return a primitive value.")}return(U==="string"?String:Number)(H)};(function(H){var U=Object.defineProperty,X=function B(C,A){for(var T in A)U(C,T,{get:A[T],enumerable:!0,configurable:!0,set:function Y(J){return A[T]=function(){return J}}})},Z={lessThanXSeconds:{one:"\u0623\u0642\u0644 \u0645\u0646 \u062B\u0627\u0646\u064A\u0629 \u0648\u0627\u062D\u062F\u0629",two:"\u0623\u0642\u0644 \u0645\u0646 \u062B\u0627\u0646\u062A\u064A\u0646",threeToTen:"\u0623\u0642\u0644 \u0645\u0646 {{count}} \u062B\u0648\u0627\u0646\u064A",other:"\u0623\u0642\u0644 \u0645\u0646 {{count}} \u062B\u0627\u0646\u064A\u0629"},xSeconds:{one:"\u062B\u0627\u0646\u064A\u0629 \u0648\u0627\u062D\u062F\u0629",two:"\u062B\u0627\u0646\u062A\u064A\u0646",threeToTen:"{{count}} \u062B\u0648\u0627\u0646\u064A",other:"{{count}} \u062B\u0627\u0646\u064A\u0629"},halfAMinute:"\u0646\u0635\u0641 \u062F\u0642\u064A\u0642\u0629",lessThanXMinutes:{one:"\u0623\u0642\u0644 \u0645\u0646 \u062F\u0642\u064A\u0642\u0629",two:"\u0623\u0642\u0644 \u0645\u0646 \u062F\u0642\u064A\u0642\u062A\u064A\u0646",threeToTen:"\u0623\u0642\u0644 \u0645\u0646 {{count}} \u062F\u0642\u0627\u0626\u0642",other:"\u0623\u0642\u0644 \u0645\u0646 {{count}} \u062F\u0642\u064A\u0642\u0629"},xMinutes:{one:"\u062F\u0642\u064A\u0642\u0629 \u0648\u0627\u062D\u062F\u0629",two:"\u062F\u0642\u064A\u0642\u062A\u064A\u0646",threeToTen:"{{count}} \u062F\u0642\u0627\u0626\u0642",other:"{{count}} \u062F\u0642\u064A\u0642\u0629"},aboutXHours:{one:"\u0633\u0627\u0639\u0629 \u0648\u0627\u062D\u062F\u0629 \u062A\u0642\u0631\u064A\u0628\u0627\u064B",two:"\u0633\u0627\u0639\u062A\u064A\u0646 \u062A\u0642\u0631\u064A\u0628\u0627\u064B",threeToTen:"{{count}} \u0633\u0627\u0639\u0627\u062A \u062A\u0642\u0631\u064A\u0628\u0627\u064B",other:"{{count}} \u0633\u0627\u0639\u0629 \u062A\u0642\u0631\u064A\u0628\u0627\u064B"},xHours:{one:"\u0633\u0627\u0639\u0629 \u0648\u0627\u062D\u062F\u0629",two:"\u0633\u0627\u0639\u062A\u064A\u0646",threeToTen:"{{count}} \u0633\u0627\u0639\u0627\u062A",other:"{{count}} \u0633\u0627\u0639\u0629"},xDays:{one:"\u064A\u0648\u0645 \u0648\u0627\u062D\u062F",two:"\u064A\u0648\u0645\u064A\u0646",threeToTen:"{{count}} \u0623\u064A\u0627\u0645",other:"{{count}} \u064A\u0648\u0645"},aboutXWeeks:{one:"\u0623\u0633\u0628\u0648\u0639 \u0648\u0627\u062D\u062F \u062A\u0642\u0631\u064A\u0628\u0627\u064B",two:"\u0623\u0633\u0628\u0648\u0639\u064A\u0646 \u062A\u0642\u0631\u064A\u0628\u0627\u064B",threeToTen:"{{count}} \u0623\u0633\u0627\u0628\u064A\u0639 \u062A\u0642\u0631\u064A\u0628\u0627\u064B",other:"{{count}} \u0623\u0633\u0628\u0648\u0639 \u062A\u0642\u0631\u064A\u0628\u0627\u064B"},xWeeks:{one:"\u0623\u0633\u0628\u0648\u0639 \u0648\u0627\u062D\u062F",two:"\u0623\u0633\u0628\u0648\u0639\u064A\u0646",threeToTen:"{{count}} \u0623\u0633\u0627\u0628\u064A\u0639",other:"{{count}} \u0623\u0633\u0628\u0648\u0639"},aboutXMonths:{one:"\u0634\u0647\u0631 \u0648\u0627\u062D\u062F \u062A\u0642\u0631\u064A\u0628\u0627\u064B",two:"\u0634\u0647\u0631\u064A\u0646 \u062A\u0642\u0631\u064A\u0628\u0627\u064B",threeToTen:"{{count}} \u0623\u0634\u0647\u0631 \u062A\u0642\u0631\u064A\u0628\u0627\u064B",other:"{{count}} \u0634\u0647\u0631 \u062A\u0642\u0631\u064A\u0628\u0627\u064B"},xMonths:{one:"\u0634\u0647\u0631 \u0648\u0627\u062D\u062F",two:"\u0634\u0647\u0631\u064A\u0646",threeToTen:"{{count}} \u0623\u0634\u0647\u0631",other:"{{count}} \u0634\u0647\u0631"},aboutXYears:{one:"\u0639\u0627\u0645 \u0648\u0627\u062D\u062F \u062A\u0642\u0631\u064A\u0628\u0627\u064B",two:"\u0639\u0627\u0645\u064A\u0646 \u062A\u0642\u0631\u064A\u0628\u0627\u064B",threeToTen:"{{count}} \u0623\u0639\u0648\u0627\u0645 \u062A\u0642\u0631\u064A\u0628\u0627\u064B",other:"{{count}} \u0639\u0627\u0645 \u062A\u0642\u0631\u064A\u0628\u0627\u064B"},xYears:{one:"\u0639\u0627\u0645 \u0648\u0627\u062D\u062F",two:"\u0639\u0627\u0645\u064A\u0646",threeToTen:"{{count}} \u0623\u0639\u0648\u0627\u0645",other:"{{count}} \u0639\u0627\u0645"},overXYears:{one:"\u0623\u0643\u062B\u0631 \u0645\u0646 \u0639\u0627\u0645",two:"\u0623\u0643\u062B\u0631 \u0645\u0646 \u0639\u0627\u0645\u064A\u0646",threeToTen:"\u0623\u0643\u062B\u0631 \u0645\u0646 {{count}} \u0623\u0639\u0648\u0627\u0645",other:"\u0623\u0643\u062B\u0631 \u0645\u0646 {{count}} \u0639\u0627\u0645"},almostXYears:{one:"\u0639\u0627\u0645 \u0648\u0627\u062D\u062F \u062A\u0642\u0631\u064A\u0628\u0627\u064B",two:"\u0639\u0627\u0645\u064A\u0646 \u062A\u0642\u0631\u064A\u0628\u0627\u064B",threeToTen:"{{count}} \u0623\u0639\u0648\u0627\u0645 \u062A\u0642\u0631\u064A\u0628\u0627\u064B",other:"{{count}} \u0639\u0627\u0645 \u062A\u0642\u0631\u064A\u0628\u0627\u064B"}},N=function B(C,A,T){T=T||{};var Y=Z[C],J;if(typeof Y==="string")J=Y;else if(A===1)J=Y.one;else if(A===2)J=Y.two;else if(A<=10)J=Y.threeToTen.replace("{{count}}",String(A));else J=Y.other.replace("{{count}}",String(A));if(T.addSuffix)if(T.comparison&&T.comparison>0)return"\u0641\u064A \u062E\u0644\u0627\u0644 "+J;else return"\u0645\u0646\u0630 "+J;return J};function z(B){return function(){var C=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},A=C.width?String(C.width):B.defaultWidth,T=B.formats[A]||B.formats[B.defaultWidth];return T}}var S={full:"EEEE, MMMM do, y",long:"MMMM do, y",medium:"MMM d, y",short:"MM/dd/yyyy"},W={full:"h:mm:ss a zzzz",long:"h:mm:ss a z",medium:"h:mm:ss a",short:"h:mm a"},$={full:"{{date}} '\u0639\u0646\u062F' {{time}}",long:"{{date}} '\u0639\u0646\u062F' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},R={date:z({formats:S,defaultWidth:"full"}),time:z({formats:W,defaultWidth:"full"}),dateTime:z({formats:$,defaultWidth:"full"})},V={lastWeek:"'\u0623\u062E\u0631' eeee '\u0639\u0646\u062F' p",yesterday:"'\u0623\u0645\u0633 \u0639\u0646\u062F' p",today:"'\u0627\u0644\u064A\u0648\u0645 \u0639\u0646\u062F' p",tomorrow:"'\u063A\u062F\u0627\u064B \u0639\u0646\u062F' p",nextWeek:"eeee '\u0639\u0646\u062F' p",other:"P"},L=function B(C,A,T,Y){return V[C]};function Q(B){return function(C,A){var T=A!==null&&A!==void 0&&A.context?String(A.context):"standalone",Y;if(T==="formatting"&&B.formattingValues){var J=B.defaultFormattingWidth||B.defaultWidth,E=A!==null&&A!==void 0&&A.width?String(A.width):J;Y=B.formattingValues[E]||B.formattingValues[J]}else{var I=B.defaultWidth,M=A!==null&&A!==void 0&&A.width?String(A.width):B.defaultWidth;Y=B.values[M]||B.values[I]}var O=B.argumentCallback?B.argumentCallback(C):C;return Y[O]}}var f={narrow:["\u0642","\u0628"],abbreviated:["\u0642.\u0645.","\u0628.\u0645."],wide:["\u0642\u0628\u0644 \u0627\u0644\u0645\u064A\u0644\u0627\u062F","\u0628\u0639\u062F \u0627\u0644\u0645\u064A\u0644\u0627\u062F"]},j={narrow:["1","2","3","4"],abbreviated:["\u06311","\u06312","\u06313","\u06314"],wide:["\u0627\u0644\u0631\u0628\u0639 \u0627\u0644\u0623\u0648\u0644","\u0627\u0644\u0631\u0628\u0639 \u0627\u0644\u062B\u0627\u0646\u064A","\u0627\u0644\u0631\u0628\u0639 \u0627\u0644\u062B\u0627\u0644\u062B","\u0627\u0644\u0631\u0628\u0639 \u0627\u0644\u0631\u0627\u0628\u0639"]},v={narrow:["\u064A","\u0641","\u0645","\u0623","\u0645","\u064A","\u064A","\u063A","\u0634","\u0623","\u0646","\u062F"],abbreviated:["\u064A\u0646\u0627","\u0641\u0628\u0631","\u0645\u0627\u0631\u0633","\u0623\u0628\u0631\u064A\u0644","\u0645\u0627\u064A","\u064A\u0648\u0646\u0640","\u064A\u0648\u0644\u0640","\u063A\u0634\u062A","\u0634\u062A\u0646\u0640","\u0623\u0643\u062A\u0640","\u0646\u0648\u0646\u0640","\u062F\u062C\u0646\u0640"],wide:["\u064A\u0646\u0627\u064A\u0631","\u0641\u0628\u0631\u0627\u064A\u0631","\u0645\u0627\u0631\u0633","\u0623\u0628\u0631\u064A\u0644","\u0645\u0627\u064A","\u064A\u0648\u0646\u064A\u0648","\u064A\u0648\u0644\u064A\u0648\u0632","\u063A\u0634\u062A","\u0634\u062A\u0646\u0628\u0631","\u0623\u0643\u062A\u0648\u0628\u0631","\u0646\u0648\u0646\u0628\u0631","\u062F\u062C\u0646\u0628\u0631"]},P={narrow:["\u062D","\u0646","\u062B","\u0631","\u062E","\u062C","\u0633"],short:["\u0623\u062D\u062F","\u0627\u062B\u0646\u064A\u0646","\u062B\u0644\u0627\u062B\u0627\u0621","\u0623\u0631\u0628\u0639\u0627\u0621","\u062E\u0645\u064A\u0633","\u062C\u0645\u0639\u0629","\u0633\u0628\u062A"],abbreviated:["\u0623\u062D\u062F","\u0627\u062B\u0646\u0640","\u062B\u0644\u0627","\u0623\u0631\u0628\u0640","\u062E\u0645\u064A\u0640","\u062C\u0645\u0639\u0629","\u0633\u0628\u062A"],wide:["\u0627\u0644\u0623\u062D\u062F","\u0627\u0644\u0625\u062B\u0646\u064A\u0646","\u0627\u0644\u062B\u0644\u0627\u062B\u0627\u0621","\u0627\u0644\u0623\u0631\u0628\u0639\u0627\u0621","\u0627\u0644\u062E\u0645\u064A\u0633","\u0627\u0644\u062C\u0645\u0639\u0629","\u0627\u0644\u0633\u0628\u062A"]},_={narrow:{am:"\u0635",pm:"\u0645",midnight:"\u0646",noon:"\u0638",morning:"\u0635\u0628\u0627\u062D\u0627\u064B",afternoon:"\u0628\u0639\u062F \u0627\u0644\u0638\u0647\u0631",evening:"\u0645\u0633\u0627\u0621\u0627\u064B",night:"\u0644\u064A\u0644\u0627\u064B"},abbreviated:{am:"\u0635",pm:"\u0645",midnight:"\u0646\u0635\u0641 \u0627\u0644\u0644\u064A\u0644",noon:"\u0638\u0647\u0631",morning:"\u0635\u0628\u0627\u062D\u0627\u064B",afternoon:"\u0628\u0639\u062F \u0627\u0644\u0638\u0647\u0631",evening:"\u0645\u0633\u0627\u0621\u0627\u064B",night:"\u0644\u064A\u0644\u0627\u064B"},wide:{am:"\u0635",pm:"\u0645",midnight:"\u0646\u0635\u0641 \u0627\u0644\u0644\u064A\u0644",noon:"\u0638\u0647\u0631",morning:"\u0635\u0628\u0627\u062D\u0627\u064B",afternoon:"\u0628\u0639\u062F \u0627\u0644\u0638\u0647\u0631",evening:"\u0645\u0633\u0627\u0621\u0627\u064B",night:"\u0644\u064A\u0644\u0627\u064B"}},w={narrow:{am:"\u0635",pm:"\u0645",midnight:"\u0646",noon:"\u0638",morning:"\u0641\u064A \u0627\u0644\u0635\u0628\u0627\u062D",afternoon:"\u0628\u0639\u062F \u0627\u0644\u0638\u0640\u0647\u0631",evening:"\u0641\u064A \u0627\u0644\u0645\u0633\u0627\u0621",night:"\u0641\u064A \u0627\u0644\u0644\u064A\u0644"},abbreviated:{am:"\u0635",pm:"\u0645",midnight:"\u0646\u0635\u0641 \u0627\u0644\u0644\u064A\u0644",noon:"\u0638\u0647\u0631",morning:"\u0641\u064A \u0627\u0644\u0635\u0628\u0627\u062D",afternoon:"\u0628\u0639\u062F \u0627\u0644\u0638\u0647\u0631",evening:"\u0641\u064A \u0627\u0644\u0645\u0633\u0627\u0621",night:"\u0641\u064A \u0627\u0644\u0644\u064A\u0644"},wide:{am:"\u0635",pm:"\u0645",midnight:"\u0646\u0635\u0641 \u0627\u0644\u0644\u064A\u0644",noon:"\u0638\u0647\u0631",morning:"\u0635\u0628\u0627\u062D\u0627\u064B",afternoon:"\u0628\u0639\u062F \u0627\u0644\u0638\u0640\u0647\u0631",evening:"\u0641\u064A \u0627\u0644\u0645\u0633\u0627\u0621",night:"\u0641\u064A \u0627\u0644\u0644\u064A\u0644"}},F=function B(C){return String(C)},k={ordinalNumber:F,era:Q({values:f,defaultWidth:"wide"}),quarter:Q({values:j,defaultWidth:"wide",argumentCallback:function B(C){return Number(C)-1}}),month:Q({values:v,defaultWidth:"wide"}),day:Q({values:P,defaultWidth:"wide"}),dayPeriod:Q({values:_,defaultWidth:"wide",formattingValues:w,defaultFormattingWidth:"wide"})};function h(B){return function(C){var A=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},T=C.match(B.matchPattern);if(!T)return null;var Y=T[0],J=C.match(B.parsePattern);if(!J)return null;var E=B.valueCallback?B.valueCallback(J[0]):J[0];E=A.valueCallback?A.valueCallback(E):E;var I=C.slice(Y.length);return{value:E,rest:I}}}function q(B){return function(C){var A=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},T=A.width,Y=T&&B.matchPatterns[T]||B.matchPatterns[B.defaultMatchWidth],J=C.match(Y);if(!J)return null;var E=J[0],I=T&&B.parsePatterns[T]||B.parsePatterns[B.defaultParseWidth],M=Array.isArray(I)?m(I,function(D){return D.test(E)}):b(I,function(D){return D.test(E)}),O;O=B.valueCallback?B.valueCallback(M):M,O=A.valueCallback?A.valueCallback(O):O;var t=C.slice(E.length);return{value:O,rest:t}}}var b=function B(C,A){for(var T in C)if(Object.prototype.hasOwnProperty.call(C,T)&&A(C[T]))return T;return},m=function B(C,A){for(var T=0;T<C.length;T++)if(A(C[T]))return T;return},y=/^(\d+)(th|st|nd|rd)?/i,c=/\d+/i,u={narrow:/^(ق|ب)/i,abbreviated:/^(ق\.?\s?م\.?|ق\.?\s?م\.?\s?|a\.?\s?d\.?|c\.?\s?)/i,wide:/^(قبل الميلاد|قبل الميلاد|بعد الميلاد|بعد الميلاد)/i},p={any:[/^قبل/i,/^بعد/i]},d={narrow:/^[1234]/i,abbreviated:/^ر[1234]/i,wide:/^الربع [1234]/i},g={any:[/1/i,/2/i,/3/i,/4/i]},l={narrow:/^[يفمأمسند]/i,abbreviated:/^(ين|ف|مار|أب|ماي|يون|يول|غش|شت|أك|ن|د)/i,wide:/^(ين|ف|مار|أب|ماي|يون|يول|غش|شت|أك|ن|د)/i},i={narrow:[/^ي/i,/^ف/i,/^م/i,/^أ/i,/^م/i,/^ي/i,/^ي/i,/^غ/i,/^ش/i,/^أ/i,/^ن/i,/^د/i],any:[/^ين/i,/^فب/i,/^مار/i,/^أب/i,/^ماي/i,/^يون/i,/^يول/i,/^غشت/i,/^ش/i,/^أك/i,/^ن/i,/^د/i]},n={narrow:/^[حنثرخجس]/i,short:/^(أحد|إثنين|ثلاثاء|أربعاء|خميس|جمعة|سبت)/i,abbreviated:/^(أحد|إثن|ثلا|أرب|خمي|جمعة|سبت)/i,wide:/^(الأحد|الإثنين|الثلاثاء|الأربعاء|الخميس|الجمعة|السبت)/i},s={narrow:[/^ح/i,/^ن/i,/^ث/i,/^ر/i,/^خ/i,/^ج/i,/^س/i],wide:[/^الأحد/i,/^الإثنين/i,/^الثلاثاء/i,/^الأربعاء/i,/^الخميس/i,/^الجمعة/i,/^السبت/i],any:[/^أح/i,/^إث/i,/^ث/i,/^أر/i,/^خ/i,/^ج/i,/^س/i]},o={narrow:/^(a|p|mi|n|(in the|at) (morning|afternoon|evening|night))/i,any:/^([ap]\.?\s?m\.?|midnight|noon|(in the|at) (morning|afternoon|evening|night))/i},r={any:{am:/^a/i,pm:/^p/i,midnight:/^mi/i,noon:/^no/i,morning:/morning/i,afternoon:/afternoon/i,evening:/evening/i,night:/night/i}},e={ordinalNumber:h({matchPattern:y,parsePattern:c,valueCallback:function B(C){return parseInt(C,10)}}),era:q({matchPatterns:u,defaultMatchWidth:"wide",parsePatterns:p,defaultParseWidth:"any"}),quarter:q({matchPatterns:d,defaultMatchWidth:"wide",parsePatterns:g,defaultParseWidth:"any",valueCallback:function B(C){return Number(C)+1}}),month:q({matchPatterns:l,defaultMatchWidth:"wide",parsePatterns:i,defaultParseWidth:"any"}),day:q({matchPatterns:n,defaultMatchWidth:"wide",parsePatterns:s,defaultParseWidth:"any"}),dayPeriod:q({matchPatterns:o,defaultMatchWidth:"any",parsePatterns:r,defaultParseWidth:"any"})},a={code:"ar-MA",formatDistance:N,formatLong:R,formatRelative:L,localize:k,match:e,options:{weekStartsOn:1,firstWeekContainsDate:1}};window.dateFns=x(x({},window.dateFns),{},{locale:x(x({},(H=window.dateFns)===null||H===void 0?void 0:H.locale),{},{arMA:a})})})();

//# debugId=D303022F1BB763B164756e2164756e21
