# 📋 Système d'Annonces Légales - Documentation

## 🎯 Vue d'ensemble

Le système d'annonces légales de Charikti permet aux entreprises de créer, gérer et publier leurs annonces légales avec génération automatique de PDF et numérotation séquentielle.

## ✨ Fonctionnalités principales

### 📄 Page Annonces Légales (`AnnoncesManagement.tsx`)
- **Charte d'activités** : Graphique des annonces légales vs services juridiques
- **Transactions récentes** : Historique des paiements liés aux annonces
- **Table des ordres** : Liste complète des annonces avec statuts et actions
- **Bouton "Créer une annonce"** : Accès direct au formulaire

### 📝 Formulaire d'Annonce Légale (`AnnonceLegaleForm.tsx`)
- **Étape 1** : Sélection du type d'annonce (création, modification, transfert, etc.)
- **Étape 2** : Informations de l'entreprise et du dirigeant
- **Étape 3** : Contenu de l'annonce et documents justificatifs
- **Étape 4** : Sélection du journal et génération PDF automatique

### 🔢 Système de Numérotation Automatique (`numeroSerieService.ts`)
- **Format** : `AL-YYYY-NNNN` (ex: AL-2024-0001)
- **Incrémentation automatique** par année
- **Stockage local** persistant
- **Réinitialisation** automatique chaque nouvelle année

### 📄 Génération PDF (`AnnonceLegalePdf.tsx`)
- **Template professionnel** avec logo et mise en forme
- **Numéro de série unique** intégré
- **Toutes les informations** de l'annonce
- **Téléchargement automatique** après soumission

## 🛠️ Architecture technique

### Composants créés/modifiés

```
front-end/src/
├── components/
│   ├── forms/
│   │   └── AnnonceLegaleForm.tsx          # Formulaire principal
│   └── annonces-legales/
│       ├── AnnonceLegalePdf.tsx           # Template PDF
│       └── NumeroSerieDisplay.tsx         # Affichage numéro série
├── services/
│   └── numeroSerieService.ts              # Service de numérotation
└── pages/Entreprise/
    └── AnnoncesManagement.tsx             # Page principale (modifiée)
```

### Dépendances utilisées
- `@react-pdf/renderer` : Génération PDF
- `react-icons` : Icônes interface
- `localStorage` : Persistance numéros de série

## 📊 Types d'annonces supportés

1. **Création de société** 🏢
2. **Modification des statuts** 📝
3. **Transfert de siège social** 🏠
4. **Changement de dirigeant** 👤
5. **Augmentation de capital** 📈
6. **Dissolution de société** ⚖️

## 🗞️ Journaux de publication

- **Al Alam** (850 DH)
- **L'Économiste** (950 DH)
- **La Vie Éco** (900 DH)
- **Aujourd'hui le Maroc** (800 DH)
- **Le Matin** (875 DH)

## 🔄 Flux de travail

1. **Accès** : Entreprise → Sidebar → "Annonces Légales"
2. **Création** : Clic sur "Créer une annonce"
3. **Sélection** : Choix du type d'annonce légale
4. **Saisie** : Informations entreprise et dirigeant
5. **Contenu** : Rédaction de l'annonce + documents
6. **Publication** : Choix journal + génération PDF automatique
7. **Confirmation** : Message de succès + téléchargement PDF

## 📈 Statistiques et suivi

### Numéros de série
- **Format standardisé** : Préfixe-Année-Numéro
- **Compteurs séparés** par type de document
- **Réinitialisation annuelle** automatique
- **Historique persistant** en localStorage

### Données trackées
- Nombre total d'annonces générées
- Répartition par type d'annonce
- Évolution mensuelle/annuelle
- Coûts de publication

## 🎨 Interface utilisateur

### Design cohérent
- **Couleurs** : Orange (#FF6B35) pour les annonces légales
- **Style** : Cohérent avec le reste de l'application
- **Responsive** : Adapté mobile et desktop
- **Accessibilité** : Contrastes et navigation clavier

### Composants réutilisables
- `NumeroSerieDisplay` : Affichage numéro série
- `AnnonceLegalePdf` : Template PDF personnalisable
- Formulaire multi-étapes avec validation

## 🔧 Configuration et personnalisation

### Numérotation
```typescript
// Modifier le préfixe dans numeroSerieService.ts
const getDefaultPrefix = (type: string): string => {
  switch (type) {
    case 'annonce-legale':
      return 'AL'; // Modifiable
    // ...
  }
};
```

### Tarifs journaux
```typescript
// Dans AnnonceLegaleForm.tsx
const journaux = [
  { id: 'al-alam', name: 'Al Alam', tarif: 850 }, // Modifiable
  // ...
];
```

## 🚀 Déploiement

### Prérequis
- `@react-pdf/renderer` installé
- Stockage localStorage disponible
- Icônes React Icons

### Installation
```bash
npm install @react-pdf/renderer
```

### Utilisation
```tsx
import AnnonceLegaleForm from './components/forms/AnnonceLegaleForm';

// Dans votre composant
<AnnonceLegaleForm onClose={handleClose} />
```

## 📝 Notes importantes

- **Numéros de série** : Stockés localement, considérer une API pour la production
- **PDF** : Génération côté client, peut être lente sur gros documents
- **Validation** : Ajouter validation serveur pour données critiques
- **Backup** : Implémenter sauvegarde des numéros de série

## 🔮 Évolutions futures

- [ ] API backend pour numérotation centralisée
- [ ] Templates PDF personnalisables par type
- [ ] Intégration paiement en ligne
- [ ] Notifications email automatiques
- [ ] Historique complet des modifications
- [ ] Export Excel des statistiques
