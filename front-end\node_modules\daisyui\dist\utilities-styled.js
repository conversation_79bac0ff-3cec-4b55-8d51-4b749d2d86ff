module.exports = {".artboard-demo":{"borderRadius":"var(--rounded-box, 1rem)","-TwBgOpacity":"1","backgroundColor":"var(--fallback-b3,oklch(var(--b3)/var(--tw-bg-opacity)))","-TwTextOpacity":"1","color":"var(--fallback-bc,oklch(var(--bc)/var(--tw-text-opacity)))","boxShadow":"0 1px 3px 0 rgba(0, 0, 0, 0.1),\n      0 1px 2px 0 rgba(0, 0, 0, 0.06)"},".avatar.online:before":{"content":"\"\"","position":"absolute","zIndex":"10","display":"block","borderRadius":"9999px","-TwBgOpacity":"1","backgroundColor":"var(--fallback-su,oklch(var(--su)/var(--tw-bg-opacity)))","outlineStyle":"solid","outlineWidth":"2px","outlineColor":"var(--fallback-b1,oklch(var(--b1)/1))","width":"15%","height":"15%","top":"7%","right":"7%"},".avatar.offline:before":{"content":"\"\"","position":"absolute","zIndex":"10","display":"block","borderRadius":"9999px","-TwBgOpacity":"1","backgroundColor":"var(--fallback-b3,oklch(var(--b3)/var(--tw-bg-opacity)))","outlineStyle":"solid","outlineWidth":"2px","outlineColor":"var(--fallback-b1,oklch(var(--b1)/1))","width":"15%","height":"15%","top":"7%","right":"7%"},".card-compact .card-body":{"padding":"1rem","fontSize":"0.875rem","lineHeight":"1.25rem"},".card-compact .card-title":{"marginBottom":"0.25rem"},".card-normal .card-body":{"padding":"var(--padding-card, 2rem)","fontSize":"1rem","lineHeight":"1.5rem"},".card-normal .card-title":{"marginBottom":"0.75rem"},".divider-horizontal":{"marginLeft":"1rem","marginRight":"1rem","marginTop":"0px","marginBottom":"0px","height":"auto","width":"1rem"},".divider-vertical":{"marginLeft":"0px","marginRight":"0px","marginTop":"1rem","marginBottom":"1rem","height":"1rem","width":"auto"},".drawer-open > .drawer-toggle ~ .drawer-side > .drawer-overlay":{"cursor":"default","backgroundColor":"transparent"},".join.join-vertical > :where(*:not(:first-child))":{"marginLeft":"0px","marginRight":"0px","marginTop":"-1px"},".join.join-vertical > :where(*:not(:first-child)):is(.btn)":{"marginTop":"calc(var(--border-btn) * -1)"},".join.join-horizontal > :where(*:not(:first-child))":{"marginTop":"0px","marginBottom":"0px","marginInlineStart":"-1px"},".join.join-horizontal > :where(*:not(:first-child)):is(.btn)":{"marginInlineStart":"calc(var(--border-btn) * -1)"},".menu-horizontal > li:not(.menu-title) > details > ul":{"marginInlineStart":"0px","marginTop":"1rem","paddingTop":"0.5rem","paddingBottom":"0.5rem","paddingInlineEnd":"0.5rem"},".menu-horizontal > li > details > ul:before":{"content":"none"},":where(.menu-horizontal > li:not(.menu-title) > details > ul)":{"borderRadius":"var(--rounded-box, 1rem)","-TwBgOpacity":"1","backgroundColor":"var(--fallback-b1,oklch(var(--b1)/var(--tw-bg-opacity)))","-TwShadow":"0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1)","-TwShadowColored":"0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color)","boxShadow":"var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow)"},".menu-vertical > li:not(.menu-title) > details > ul":{"marginInlineStart":"1rem","marginTop":"0px","paddingTop":"0px","paddingBottom":"0px","paddingInlineEnd":"0px"},".menu-xs :where(li:not(.menu-title) > *:not(ul, details, .menu-title)), .menu-xs :where(li:not(.menu-title) > details > summary:not(.menu-title))":{"borderRadius":"0.25rem","paddingLeft":"0.5rem","paddingRight":"0.5rem","paddingTop":"0.25rem","paddingBottom":"0.25rem","fontSize":"0.75rem","lineHeight":"1rem"},".menu-xs .menu-title":{"paddingLeft":"0.5rem","paddingRight":"0.5rem","paddingTop":"0.25rem","paddingBottom":"0.25rem"},".menu-sm :where(li:not(.menu-title) > *:not(ul, details, .menu-title)), .menu-sm :where(li:not(.menu-title) > details > summary:not(.menu-title))":{"borderRadius":"var(--rounded-btn, 0.5rem)","paddingLeft":"0.75rem","paddingRight":"0.75rem","paddingTop":"0.25rem","paddingBottom":"0.25rem","fontSize":"0.875rem","lineHeight":"1.25rem"},".menu-sm .menu-title":{"paddingLeft":"0.75rem","paddingRight":"0.75rem","paddingTop":"0.5rem","paddingBottom":"0.5rem"},".menu-md :where(li:not(.menu-title) > *:not(ul, details, .menu-title)), .menu-md :where(li:not(.menu-title) > details > summary:not(.menu-title))":{"borderRadius":"var(--rounded-btn, 0.5rem)","paddingLeft":"1rem","paddingRight":"1rem","paddingTop":"0.5rem","paddingBottom":"0.5rem","fontSize":"0.875rem","lineHeight":"1.25rem"},".menu-md .menu-title":{"paddingLeft":"1rem","paddingRight":"1rem","paddingTop":"0.5rem","paddingBottom":"0.5rem"},".menu-lg :where(li:not(.menu-title) > *:not(ul, details, .menu-title)), .menu-lg :where(li:not(.menu-title) > details > summary:not(.menu-title))":{"borderRadius":"var(--rounded-btn, 0.5rem)","paddingLeft":"1.5rem","paddingRight":"1.5rem","paddingTop":"0.75rem","paddingBottom":"0.75rem","fontSize":"1.125rem","lineHeight":"1.75rem"},".menu-lg .menu-title":{"paddingLeft":"1.5rem","paddingRight":"1.5rem","paddingTop":"0.75rem","paddingBottom":"0.75rem"},".modal-top :where(.modal-box)":{"width":"100%","maxWidth":"none","-TwTranslateY":"-2.5rem","-TwScaleX":"1","-TwScaleY":"1","transform":"translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))","borderBottomRightRadius":"var(--rounded-box, 1rem)","borderBottomLeftRadius":"var(--rounded-box, 1rem)","borderTopLeftRadius":"0px","borderTopRightRadius":"0px"},".modal-middle :where(.modal-box)":{"width":"91.666667%","maxWidth":"32rem","-TwTranslateY":"0px","-TwScaleX":".9","-TwScaleY":".9","transform":"translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))","borderTopLeftRadius":"var(--rounded-box, 1rem)","borderTopRightRadius":"var(--rounded-box, 1rem)","borderBottomRightRadius":"var(--rounded-box, 1rem)","borderBottomLeftRadius":"var(--rounded-box, 1rem)"},".modal-bottom :where(.modal-box)":{"width":"100%","maxWidth":"none","-TwTranslateY":"2.5rem","-TwScaleX":"1","-TwScaleY":"1","transform":"translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))","borderTopLeftRadius":"var(--rounded-box, 1rem)","borderTopRightRadius":"var(--rounded-box, 1rem)","borderBottomRightRadius":"0px","borderBottomLeftRadius":"0px"},".stats-horizontal > :not([hidden]) ~ :not([hidden])":{"-TwDivideXReverse":"0","borderRightWidth":"calc(1px * var(--tw-divide-x-reverse))","borderLeftWidth":"calc(1px * calc(1 - var(--tw-divide-x-reverse)))","-TwDivideYReverse":"0","borderTopWidth":"calc(0px * calc(1 - var(--tw-divide-y-reverse)))","borderBottomWidth":"calc(0px * var(--tw-divide-y-reverse))"},".stats-horizontal":{"overflowX":"auto"},".stats-horizontal:where([dir=\"rtl\"], [dir=\"rtl\"] *)":{"-TwDivideXReverse":"1"},".stats-vertical > :not([hidden]) ~ :not([hidden])":{"-TwDivideXReverse":"0","borderRightWidth":"calc(0px * var(--tw-divide-x-reverse))","borderLeftWidth":"calc(0px * calc(1 - var(--tw-divide-x-reverse)))","-TwDivideYReverse":"0","borderTopWidth":"calc(1px * calc(1 - var(--tw-divide-y-reverse)))","borderBottomWidth":"calc(1px * var(--tw-divide-y-reverse))"},".stats-vertical":{"overflowY":"auto"},".steps-horizontal .step":{"gridTemplateRows":"40px 1fr","gridTemplateColumns":"auto","minWidth":"4rem"},".steps-horizontal .step:before":{"height":"0.5rem","width":"100%","-TwTranslateX":"0px","-TwTranslateY":"0px","transform":"translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))","content":"\"\"","marginInlineStart":"-100%"},".steps-horizontal .step:where([dir=\"rtl\"], [dir=\"rtl\"] *):before":{"-TwTranslateX":"0px","transform":"translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))"},".steps-vertical .step":{"gap":"0.5rem","gridTemplateColumns":"40px 1fr","gridTemplateRows":"auto","minHeight":"4rem","justifyItems":"start"},".steps-vertical .step:before":{"height":"100%","width":"0.5rem","-TwTranslateX":"-50%","-TwTranslateY":"-50%","transform":"translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))","marginInlineStart":"50%"},".steps-vertical .step:where([dir=\"rtl\"], [dir=\"rtl\"] *):before":{"-TwTranslateX":"50%","transform":"translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))"},".table-xs :not(thead):not(tfoot) tr":{"fontSize":"0.75rem","lineHeight":"1rem"},".table-xs :where(th, td)":{"paddingLeft":"0.5rem","paddingRight":"0.5rem","paddingTop":"0.25rem","paddingBottom":"0.25rem"},".table-sm :not(thead):not(tfoot) tr":{"fontSize":"0.875rem","lineHeight":"1.25rem"},".table-sm :where(th, td)":{"paddingLeft":"0.75rem","paddingRight":"0.75rem","paddingTop":"0.5rem","paddingBottom":"0.5rem"},".table-md :not(thead):not(tfoot) tr":{"fontSize":"0.875rem","lineHeight":"1.25rem"},".table-md :where(th, td)":{"paddingLeft":"1rem","paddingRight":"1rem","paddingTop":"0.75rem","paddingBottom":"0.75rem"},".table-lg :not(thead):not(tfoot) tr":{"fontSize":"1rem","lineHeight":"1.5rem"},".table-lg :where(th, td)":{"paddingLeft":"1.5rem","paddingRight":"1.5rem","paddingTop":"1rem","paddingBottom":"1rem"},".timeline-vertical > li > hr":{"width":"0.25rem"},":where(.timeline-vertical:has(.timeline-middle) > li > hr):first-child":{"borderBottomRightRadius":"var(--rounded-badge, 1.9rem)","borderBottomLeftRadius":"var(--rounded-badge, 1.9rem)","borderTopLeftRadius":"0px","borderTopRightRadius":"0px"},":where(.timeline-vertical:has(.timeline-middle) > li > hr):last-child":{"borderTopLeftRadius":"var(--rounded-badge, 1.9rem)","borderTopRightRadius":"var(--rounded-badge, 1.9rem)","borderBottomRightRadius":"0px","borderBottomLeftRadius":"0px"},":where(.timeline-vertical:not(:has(.timeline-middle)) :first-child > hr:last-child)":{"borderTopLeftRadius":"var(--rounded-badge, 1.9rem)","borderTopRightRadius":"var(--rounded-badge, 1.9rem)","borderBottomRightRadius":"0px","borderBottomLeftRadius":"0px"},":where(.timeline-vertical:not(:has(.timeline-middle)) :last-child > hr:first-child)":{"borderBottomRightRadius":"var(--rounded-badge, 1.9rem)","borderBottomLeftRadius":"var(--rounded-badge, 1.9rem)","borderTopLeftRadius":"0px","borderTopRightRadius":"0px"},".timeline-horizontal > li > hr":{"height":"0.25rem"},":where(.timeline-horizontal:has(.timeline-middle) > li > hr):first-child":{"borderStartEndRadius":"var(--rounded-badge, 1.9rem)","borderEndEndRadius":"var(--rounded-badge, 1.9rem)","borderStartStartRadius":"0px","borderEndStartRadius":"0px"},":where(.timeline-horizontal:has(.timeline-middle) > li > hr):last-child":{"borderStartStartRadius":"var(--rounded-badge, 1.9rem)","borderEndStartRadius":"var(--rounded-badge, 1.9rem)","borderStartEndRadius":"0px","borderEndEndRadius":"0px"},":where(.timeline-horizontal:not(:has(.timeline-middle)) :first-child > hr:last-child)":{"borderStartStartRadius":"var(--rounded-badge, 1.9rem)","borderEndStartRadius":"var(--rounded-badge, 1.9rem)","borderStartEndRadius":"0px","borderEndEndRadius":"0px"},":where(.timeline-horizontal:not(:has(.timeline-middle)) :last-child > hr:first-child)":{"borderStartEndRadius":"var(--rounded-badge, 1.9rem)","borderEndEndRadius":"var(--rounded-badge, 1.9rem)","borderStartStartRadius":"0px","borderEndStartRadius":"0px"},".tooltip":{"position":"relative","display":"inline-block","textAlign":"center","-TooltipTail":"0.1875rem","-TooltipColor":"var(--fallback-n,oklch(var(--n)/1))","-TooltipTextColor":"var(--fallback-nc,oklch(var(--nc)/1))","-TooltipTailOffset":"calc(100% + 0.0625rem - var(--tooltip-tail))"},".tooltip:before,\n.tooltip:after":{"opacity":"0","transitionProperty":"color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter","transitionDelay":"100ms","transitionDuration":"200ms","transitionTimingFunction":"cubic-bezier(0.4, 0, 0.2, 1)"},".tooltip:after":{"position":["absolute","absolute"],"content":"\"\"","borderStyle":"solid","borderWidth":"var(--tooltip-tail, 0)","width":"0","height":"0","display":"block"},".tooltip:before":{"maxWidth":"20rem","whiteSpace":"normal","borderRadius":"0.25rem","paddingLeft":"0.5rem","paddingRight":"0.5rem","paddingTop":"0.25rem","paddingBottom":"0.25rem","fontSize":"0.875rem","lineHeight":"1.25rem","backgroundColor":"var(--tooltip-color)","color":"var(--tooltip-text-color)","width":"max-content"},".tooltip.tooltip-open:before":{"opacity":"1","transitionDelay":"75ms"},".tooltip.tooltip-open:after":{"opacity":"1","transitionDelay":"75ms"},".tooltip:hover:before":{"opacity":"1","transitionDelay":"75ms"},".tooltip:hover:after":{"opacity":"1","transitionDelay":"75ms"},".tooltip:has(:focus-visible):after,\n.tooltip:has(:focus-visible):before":{"opacity":"1","transitionDelay":"75ms"},".tooltip:not([data-tip]):hover:before,\n.tooltip:not([data-tip]):hover:after":{"visibility":"hidden","opacity":"0"},".tooltip:after, .tooltip-top:after":{"transform":"translateX(-50%)","borderColor":"var(--tooltip-color) transparent transparent transparent","top":"auto","left":"50%","right":"auto","bottom":"var(--tooltip-tail-offset)"},".tooltip-bottom:after":{"transform":"translateX(-50%)","borderColor":"transparent transparent var(--tooltip-color) transparent","top":"var(--tooltip-tail-offset)","left":"50%","right":"auto","bottom":"auto"},".tooltip-left:after":{"transform":"translateY(-50%)","borderColor":"transparent transparent transparent var(--tooltip-color)","top":"50%","left":"auto","right":"calc(var(--tooltip-tail-offset) + 0.0625rem)","bottom":"auto"},".tooltip-right:after":{"transform":"translateY(-50%)","borderColor":"transparent var(--tooltip-color) transparent transparent","top":"50%","left":"calc(var(--tooltip-tail-offset) + 0.0625rem)","right":"auto","bottom":"auto"},".tooltip-primary":{"-TooltipColor":"var(--fallback-p,oklch(var(--p)/1))","-TooltipTextColor":"var(--fallback-pc,oklch(var(--pc)/1))"},".tooltip-secondary":{"-TooltipColor":"var(--fallback-s,oklch(var(--s)/1))","-TooltipTextColor":"var(--fallback-sc,oklch(var(--sc)/1))"},".tooltip-accent":{"-TooltipColor":"var(--fallback-a,oklch(var(--a)/1))","-TooltipTextColor":"var(--fallback-ac,oklch(var(--ac)/1))"},".tooltip-info":{"-TooltipColor":"var(--fallback-in,oklch(var(--in)/1))","-TooltipTextColor":"var(--fallback-inc,oklch(var(--inc)/1))"},".tooltip-success":{"-TooltipColor":"var(--fallback-su,oklch(var(--su)/1))","-TooltipTextColor":"var(--fallback-suc,oklch(var(--suc)/1))"},".tooltip-warning":{"-TooltipColor":"var(--fallback-wa,oklch(var(--wa)/1))","-TooltipTextColor":"var(--fallback-wac,oklch(var(--wac)/1))"},".tooltip-error":{"-TooltipColor":"var(--fallback-er,oklch(var(--er)/1))","-TooltipTextColor":"var(--fallback-erc,oklch(var(--erc)/1))"}};