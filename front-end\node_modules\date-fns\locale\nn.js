"use strict";
exports.nn = void 0;
var _index = require("./nn/_lib/formatDistance.js");
var _index2 = require("./nn/_lib/formatLong.js");
var _index3 = require("./nn/_lib/formatRelative.js");
var _index4 = require("./nn/_lib/localize.js");
var _index5 = require("./nn/_lib/match.js");

/**
 * @category Locales
 * @summary Norwegian Nynorsk locale.
 * @language Norwegian Nynorsk
 * @iso-639-2 nno
 * <AUTHOR> [@draperunner](https://github.com/draperunner)
 */
const nn = (exports.nn = {
  code: "nn",
  formatDistance: _index.formatDistance,
  formatLong: _index2.formatLong,
  formatRelative: _index3.formatRelative,
  localize: _index4.localize,
  match: _index5.match,
  options: {
    weekStartsOn: 1 /* Monday */,
    firstWeekContainsDate: 4,
  },
});
