"use strict";
exports.bs = void 0;
var _index = require("./bs/_lib/formatDistance.cjs");
var _index2 = require("./bs/_lib/formatLong.cjs");
var _index3 = require("./bs/_lib/formatRelative.cjs");
var _index4 = require("./bs/_lib/localize.cjs");
var _index5 = require("./bs/_lib/match.cjs");

/**
 * @category Locales
 * @summary Bosnian locale.
 * @language Bosnian
 * @iso-639-2 bos
 * <AUTHOR> [@branislavlazic](https://github.com/branislavlazic)
 */
const bs = (exports.bs = {
  code: "bs",
  formatDistance: _index.formatDistance,
  formatLong: _index2.formatLong,
  formatRelative: _index3.formatRelative,
  localize: _index4.localize,
  match: _index5.match,
  options: {
    weekStartsOn: 1 /* Monday */,
    firstWeekContainsDate: 4,
  },
});
