{"version": 3, "file": "astUtilities.d.ts", "sourceRoot": "", "sources": ["../../../src/ast-utils/eslint-utils/astUtilities.ts"], "names": [], "mappings": "AAEA,OAAO,KAAK,KAAK,QAAQ,MAAM,iBAAiB,CAAC;AACjD,OAAO,KAAK,EAAE,QAAQ,EAAE,MAAM,iBAAiB,CAAC;AAEhD;;;;GAIG;AACH,QAAA,MAAM,uBAAuB,EAA0C,CACrE,IAAI,EACA,QAAQ,CAAC,uBAAuB,GAChC,QAAQ,CAAC,mBAAmB,GAC5B,QAAQ,CAAC,kBAAkB,EAC/B,UAAU,EAAE,QAAQ,CAAC,UAAU,KAC5B,QAAQ,CAAC,cAAc,CAAC;AAE7B;;;;GAIG;AACH,QAAA,MAAM,uBAAuB,EAA0C,CACrE,IAAI,EACA,QAAQ,CAAC,uBAAuB,GAChC,QAAQ,CAAC,mBAAmB,GAC5B,QAAQ,CAAC,kBAAkB,EAC/B,UAAU,CAAC,EAAE,QAAQ,CAAC,UAAU,KAC7B,MAAM,CAAC;AAEZ;;;;;;GAMG;AACH,QAAA,MAAM,eAAe,EAAkC,CACrD,IAAI,EACA,QAAQ,CAAC,gBAAgB,GACzB,QAAQ,CAAC,gBAAgB,GACzB,QAAQ,CAAC,QAAQ,GACjB,QAAQ,CAAC,kBAAkB,EAC/B,YAAY,CAAC,EAAE,QAAQ,CAAC,KAAK,CAAC,KAAK,KAChC,MAAM,GAAG,IAAI,CAAC;AAEnB;;;;;;;;;;GAUG;AACH,QAAA,MAAM,cAAc,EAAiC,CACnD,IAAI,EAAE,QAAQ,CAAC,IAAI,EACnB,YAAY,CAAC,EAAE,QAAQ,CAAC,KAAK,CAAC,KAAK,KAChC;IAAE,KAAK,EAAE,OAAO,CAAA;CAAE,GAAG,IAAI,CAAC;AAE/B;;;;;GAKG;AACH,QAAA,MAAM,mBAAmB,EAAsC,CAC7D,IAAI,EAAE,QAAQ,CAAC,IAAI,EACnB,YAAY,CAAC,EAAE,QAAQ,CAAC,KAAK,CAAC,KAAK,KAChC,MAAM,GAAG,IAAI,CAAC;AAEnB;;;;;;;;;;;;;;;;;;;;;;GAsBG;AACH,QAAA,MAAM,aAAa,EAAgC,CACjD,IAAI,EAAE,QAAQ,CAAC,IAAI,EACnB,UAAU,EAAE,QAAQ,CAAC,UAAU,EAC/B,OAAO,CAAC,EAAE;IACR,eAAe,CAAC,EAAE,OAAO,CAAC;IAC1B,8BAA8B,CAAC,EAAE,OAAO,CAAC;CAC1C,KACE,OAAO,CAAC;AAEb,QAAA,MAAM,eAAe,EAAkC;IACrD,CACE,KAAK,EAAE,MAAM,EACb,IAAI,EAAE,QAAQ,CAAC,IAAI,EACnB,UAAU,EAAE,QAAQ,CAAC,UAAU,GAC9B,OAAO,CAAC;IAEX;;;;;;;;OAQG;IACH,CAAC,IAAI,EAAE,QAAQ,CAAC,IAAI,EAAE,UAAU,EAAE,QAAQ,CAAC,UAAU,GAAG,OAAO,CAAC;CACjE,CAAC;AAEF,OAAO,EACL,uBAAuB,EACvB,uBAAuB,EACvB,eAAe,EACf,cAAc,EACd,mBAAmB,EACnB,aAAa,EACb,eAAe,GAChB,CAAC"}