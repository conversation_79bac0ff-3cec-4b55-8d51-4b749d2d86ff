{"version": 3, "sources": ["../../src/QueryClientProvider.tsx"], "sourcesContent": ["'use client'\nimport * as React from 'react'\n\nimport type { QueryClient } from '@tanstack/query-core'\n\nexport const QueryClientContext = React.createContext<QueryClient | undefined>(\n  undefined,\n)\n\nexport const useQueryClient = (queryClient?: QueryClient) => {\n  const client = React.useContext(QueryClientContext)\n\n  if (queryClient) {\n    return queryClient\n  }\n\n  if (!client) {\n    throw new Error('No QueryClient set, use QueryClientProvider to set one')\n  }\n\n  return client\n}\n\nexport type QueryClientProviderProps = {\n  client: QueryClient\n  children?: React.ReactNode\n}\n\nexport const QueryClientProvider = ({\n  client,\n  children,\n}: QueryClientProviderProps): React.JSX.Element => {\n  React.useEffect(() => {\n    client.mount()\n    return () => {\n      client.unmount()\n    }\n  }, [client])\n\n  return (\n    <QueryClientContext.Provider value={client}>\n      {children}\n    </QueryClientContext.Provider>\n  )\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,YAAuB;AAuCnB;AAnCG,IAAM,qBAA2B;AAAA,EACtC;AACF;AAEO,IAAM,iBAAiB,CAAC,gBAA8B;AAC3D,QAAM,SAAe,iBAAW,kBAAkB;AAElD,MAAI,aAAa;AACf,WAAO;AAAA,EACT;AAEA,MAAI,CAAC,QAAQ;AACX,UAAM,IAAI,MAAM,wDAAwD;AAAA,EAC1E;AAEA,SAAO;AACT;AAOO,IAAM,sBAAsB,CAAC;AAAA,EAClC;AAAA,EACA;AACF,MAAmD;AACjD,EAAM,gBAAU,MAAM;AACpB,WAAO,MAAM;AACb,WAAO,MAAM;AACX,aAAO,QAAQ;AAAA,IACjB;AAAA,EACF,GAAG,CAAC,MAAM,CAAC;AAEX,SACE,4CAAC,mBAAmB,UAAnB,EAA4B,OAAO,QACjC,UACH;AAEJ;", "names": []}