import React from 'react';
import { FaHashtag, FaCalendarAlt, FaFileAlt } from 'react-icons/fa';
import { getNextNumeroSerie, getStatistics } from '../../services/numeroSerieService';

interface NumeroSerieDisplayProps {
  type?: 'annonce-legale' | 'domiciliation' | 'juridique';
  showStats?: boolean;
  className?: string;
}

const NumeroSerieDisplay: React.FC<NumeroSerieDisplayProps> = ({ 
  type = 'annonce-legale', 
  showStats = false,
  className = ''
}) => {
  const nextNumero = getNextNumeroSerie(type);
  const stats = getStatistics();

  const getTypeInfo = (type: string) => {
    switch (type) {
      case 'annonce-legale':
        return {
          label: 'Annonce Légale',
          icon: <FaFileAlt className="text-orange-500" />,
          color: 'orange',
          count: stats.annonceLegale.lastNumber
        };
      case 'domiciliation':
        return {
          label: 'Domiciliation',
          icon: <FaCalendarAlt className="text-blue-500" />,
          color: 'blue',
          count: stats.domiciliation.lastNumber
        };
      case 'juridique':
        return {
          label: 'Service Juridique',
          icon: <FaHashtag className="text-green-500" />,
          color: 'green',
          count: stats.juridique.lastNumber
        };
      default:
        return {
          label: 'Document',
          icon: <FaFileAlt className="text-gray-500" />,
          color: 'gray',
          count: 0
        };
    }
  };

  const typeInfo = getTypeInfo(type);

  return (
    <div className={`bg-white rounded-lg shadow-md p-4 ${className}`}>
      <div className="flex items-center justify-between">
        <div className="flex items-center">
          <div className="mr-3">
            {typeInfo.icon}
          </div>
          <div>
            <h4 className="text-sm font-medium text-gray-700">Prochain numéro</h4>
            <p className="text-xs text-gray-500">{typeInfo.label}</p>
          </div>
        </div>
        <div className="text-right">
          <p className={`text-lg font-bold text-${typeInfo.color}-600`}>
            {nextNumero}
          </p>
          {showStats && (
            <p className="text-xs text-gray-500">
              {typeInfo.count} générés cette année
            </p>
          )}
        </div>
      </div>
      
      {showStats && (
        <div className="mt-4 pt-4 border-t border-gray-200">
          <div className="grid grid-cols-3 gap-4 text-center">
            <div>
              <p className="text-lg font-bold text-orange-600">{stats.annonceLegale.lastNumber}</p>
              <p className="text-xs text-gray-500">Annonces</p>
            </div>
            <div>
              <p className="text-lg font-bold text-blue-600">{stats.domiciliation.lastNumber}</p>
              <p className="text-xs text-gray-500">Domiciliations</p>
            </div>
            <div>
              <p className="text-lg font-bold text-green-600">{stats.juridique.lastNumber}</p>
              <p className="text-xs text-gray-500">Juridiques</p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default NumeroSerieDisplay;
