var q=function(I){return q=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(H){return typeof H}:function(H){return H&&typeof Symbol=="function"&&H.constructor===Symbol&&H!==Symbol.prototype?"symbol":typeof H},q(I)},W=function(I,H){var T=Object.keys(I);if(Object.getOwnPropertySymbols){var X=Object.getOwnPropertySymbols(I);H&&(X=X.filter(function(K){return Object.getOwnPropertyDescriptor(I,K).enumerable})),T.push.apply(T,X)}return T},D=function(I){for(var H=1;H<arguments.length;H++){var T=arguments[H]!=null?arguments[H]:{};H%2?W(Object(T),!0).forEach(function(X){C1(I,X,T[X])}):Object.getOwnPropertyDescriptors?Object.defineProperties(I,Object.getOwnPropertyDescriptors(T)):W(Object(T)).forEach(function(X){Object.defineProperty(I,X,Object.getOwnPropertyDescriptor(T,X))})}return I},C1=function(I,H,T){if(H=G1(H),H in I)Object.defineProperty(I,H,{value:T,enumerable:!0,configurable:!0,writable:!0});else I[H]=T;return I},G1=function(I){var H=H1(I,"string");return q(H)=="symbol"?H:String(H)},H1=function(I,H){if(q(I)!="object"||!I)return I;var T=I[Symbol.toPrimitive];if(T!==void 0){var X=T.call(I,H||"default");if(q(X)!="object")return X;throw new TypeError("@@toPrimitive must return a primitive value.")}return(H==="string"?String:Number)(I)};(function(I){var H=Object.defineProperty,T=function E(G,C){for(var B in C)H(G,B,{get:C[B],enumerable:!0,configurable:!0,set:function J(U){return C[B]=function(){return U}}})},X={lessThanXSeconds:{one:"d\u01B0\u1EDBi 1 gi\xE2y",other:"d\u01B0\u1EDBi {{count}} gi\xE2y"},xSeconds:{one:"1 gi\xE2y",other:"{{count}} gi\xE2y"},halfAMinute:"n\u1EEDa ph\xFAt",lessThanXMinutes:{one:"d\u01B0\u1EDBi 1 ph\xFAt",other:"d\u01B0\u1EDBi {{count}} ph\xFAt"},xMinutes:{one:"1 ph\xFAt",other:"{{count}} ph\xFAt"},aboutXHours:{one:"kho\u1EA3ng 1 gi\u1EDD",other:"kho\u1EA3ng {{count}} gi\u1EDD"},xHours:{one:"1 gi\u1EDD",other:"{{count}} gi\u1EDD"},xDays:{one:"1 ng\xE0y",other:"{{count}} ng\xE0y"},aboutXWeeks:{one:"kho\u1EA3ng 1 tu\u1EA7n",other:"kho\u1EA3ng {{count}} tu\u1EA7n"},xWeeks:{one:"1 tu\u1EA7n",other:"{{count}} tu\u1EA7n"},aboutXMonths:{one:"kho\u1EA3ng 1 th\xE1ng",other:"kho\u1EA3ng {{count}} th\xE1ng"},xMonths:{one:"1 th\xE1ng",other:"{{count}} th\xE1ng"},aboutXYears:{one:"kho\u1EA3ng 1 n\u0103m",other:"kho\u1EA3ng {{count}} n\u0103m"},xYears:{one:"1 n\u0103m",other:"{{count}} n\u0103m"},overXYears:{one:"h\u01A1n 1 n\u0103m",other:"h\u01A1n {{count}} n\u0103m"},almostXYears:{one:"g\u1EA7n 1 n\u0103m",other:"g\u1EA7n {{count}} n\u0103m"}},K=function E(G,C,B){var J,U=X[G];if(typeof U==="string")J=U;else if(C===1)J=U.one;else J=U.other.replace("{{count}}",String(C));if(B!==null&&B!==void 0&&B.addSuffix)if(B.comparison&&B.comparison>0)return J+" n\u1EEFa";else return J+" tr\u01B0\u1EDBc";return J};function N(E){return function(){var G=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},C=G.width?String(G.width):E.defaultWidth,B=E.formats[C]||E.formats[E.defaultWidth];return B}}var $={full:"EEEE, 'ng\xE0y' d MMMM 'n\u0103m' y",long:"'ng\xE0y' d MMMM 'n\u0103m' y",medium:"d MMM 'n\u0103m' y",short:"dd/MM/y"},M={full:"HH:mm:ss zzzz",long:"HH:mm:ss z",medium:"HH:mm:ss",short:"HH:mm"},S={full:"{{date}} {{time}}",long:"{{date}} {{time}}",medium:"{{date}} {{time}}",short:"{{date}} {{time}}"},R={date:N({formats:$,defaultWidth:"full"}),time:N({formats:M,defaultWidth:"full"}),dateTime:N({formats:S,defaultWidth:"full"})},L={lastWeek:"eeee 'tu\u1EA7n tr\u01B0\u1EDBc v\xE0o l\xFAc' p",yesterday:"'h\xF4m qua v\xE0o l\xFAc' p",today:"'h\xF4m nay v\xE0o l\xFAc' p",tomorrow:"'ng\xE0y mai v\xE0o l\xFAc' p",nextWeek:"eeee 't\u1EDBi v\xE0o l\xFAc' p",other:"P"},V=function E(G,C,B,J){return L[G]};function O(E){return function(G,C){var B=C!==null&&C!==void 0&&C.context?String(C.context):"standalone",J;if(B==="formatting"&&E.formattingValues){var U=E.defaultFormattingWidth||E.defaultWidth,Y=C!==null&&C!==void 0&&C.width?String(C.width):U;J=E.formattingValues[Y]||E.formattingValues[U]}else{var Z=E.defaultWidth,x=C!==null&&C!==void 0&&C.width?String(C.width):E.defaultWidth;J=E.values[x]||E.values[Z]}var A=E.argumentCallback?E.argumentCallback(G):G;return J[A]}}var v={narrow:["TCN","SCN"],abbreviated:["tr\u01B0\u1EDBc CN","sau CN"],wide:["tr\u01B0\u1EDBc C\xF4ng Nguy\xEAn","sau C\xF4ng Nguy\xEAn"]},j={narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["Qu\xFD 1","Qu\xFD 2","Qu\xFD 3","Qu\xFD 4"]},f={narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["qu\xFD I","qu\xFD II","qu\xFD III","qu\xFD IV"]},_={narrow:["1","2","3","4","5","6","7","8","9","10","11","12"],abbreviated:["Thg 1","Thg 2","Thg 3","Thg 4","Thg 5","Thg 6","Thg 7","Thg 8","Thg 9","Thg 10","Thg 11","Thg 12"],wide:["Th\xE1ng M\u1ED9t","Th\xE1ng Hai","Th\xE1ng Ba","Th\xE1ng T\u01B0","Th\xE1ng N\u0103m","Th\xE1ng S\xE1u","Th\xE1ng B\u1EA3y","Th\xE1ng T\xE1m","Th\xE1ng Ch\xEDn","Th\xE1ng M\u01B0\u1EDDi","Th\xE1ng M\u01B0\u1EDDi M\u1ED9t","Th\xE1ng M\u01B0\u1EDDi Hai"]},w={narrow:["01","02","03","04","05","06","07","08","09","10","11","12"],abbreviated:["thg 1","thg 2","thg 3","thg 4","thg 5","thg 6","thg 7","thg 8","thg 9","thg 10","thg 11","thg 12"],wide:["th\xE1ng 01","th\xE1ng 02","th\xE1ng 03","th\xE1ng 04","th\xE1ng 05","th\xE1ng 06","th\xE1ng 07","th\xE1ng 08","th\xE1ng 09","th\xE1ng 10","th\xE1ng 11","th\xE1ng 12"]},P={narrow:["CN","T2","T3","T4","T5","T6","T7"],short:["CN","Th 2","Th 3","Th 4","Th 5","Th 6","Th 7"],abbreviated:["CN","Th\u1EE9 2","Th\u1EE9 3","Th\u1EE9 4","Th\u1EE9 5","Th\u1EE9 6","Th\u1EE9 7"],wide:["Ch\u1EE7 Nh\u1EADt","Th\u1EE9 Hai","Th\u1EE9 Ba","Th\u1EE9 T\u01B0","Th\u1EE9 N\u0103m","Th\u1EE9 S\xE1u","Th\u1EE9 B\u1EA3y"]},F={narrow:{am:"am",pm:"pm",midnight:"n\u1EEDa \u0111\xEAm",noon:"tr",morning:"sg",afternoon:"ch",evening:"t\u1ED1i",night:"\u0111\xEAm"},abbreviated:{am:"AM",pm:"PM",midnight:"n\u1EEDa \u0111\xEAm",noon:"tr\u01B0a",morning:"s\xE1ng",afternoon:"chi\u1EC1u",evening:"t\u1ED1i",night:"\u0111\xEAm"},wide:{am:"SA",pm:"CH",midnight:"n\u1EEDa \u0111\xEAm",noon:"tr\u01B0a",morning:"s\xE1ng",afternoon:"chi\u1EC1u",evening:"t\u1ED1i",night:"\u0111\xEAm"}},h={narrow:{am:"am",pm:"pm",midnight:"n\u1EEDa \u0111\xEAm",noon:"tr",morning:"sg",afternoon:"ch",evening:"t\u1ED1i",night:"\u0111\xEAm"},abbreviated:{am:"AM",pm:"PM",midnight:"n\u1EEDa \u0111\xEAm",noon:"tr\u01B0a",morning:"s\xE1ng",afternoon:"chi\u1EC1u",evening:"t\u1ED1i",night:"\u0111\xEAm"},wide:{am:"SA",pm:"CH",midnight:"n\u1EEDa \u0111\xEAm",noon:"gi\u1EEFa tr\u01B0a",morning:"v\xE0o bu\u1ED5i s\xE1ng",afternoon:"v\xE0o bu\u1ED5i chi\u1EC1u",evening:"v\xE0o bu\u1ED5i t\u1ED1i",night:"v\xE0o ban \u0111\xEAm"}},k=function E(G,C){var B=Number(G),J=C===null||C===void 0?void 0:C.unit;if(J==="quarter")switch(B){case 1:return"I";case 2:return"II";case 3:return"III";case 4:return"IV"}else if(J==="day")switch(B){case 1:return"th\u1EE9 2";case 2:return"th\u1EE9 3";case 3:return"th\u1EE9 4";case 4:return"th\u1EE9 5";case 5:return"th\u1EE9 6";case 6:return"th\u1EE9 7";case 7:return"ch\u1EE7 nh\u1EADt"}else if(J==="week")if(B===1)return"th\u1EE9 nh\u1EA5t";else return"th\u1EE9 "+B;else if(J==="dayOfYear")if(B===1)return"\u0111\u1EA7u ti\xEAn";else return"th\u1EE9 "+B;return String(B)},b={ordinalNumber:k,era:O({values:v,defaultWidth:"wide"}),quarter:O({values:j,defaultWidth:"wide",formattingValues:f,defaultFormattingWidth:"wide",argumentCallback:function E(G){return G-1}}),month:O({values:_,defaultWidth:"wide",formattingValues:w,defaultFormattingWidth:"wide"}),day:O({values:P,defaultWidth:"wide"}),dayPeriod:O({values:F,defaultWidth:"wide",formattingValues:h,defaultFormattingWidth:"wide"})};function Q(E){return function(G){var C=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},B=C.width,J=B&&E.matchPatterns[B]||E.matchPatterns[E.defaultMatchWidth],U=G.match(J);if(!U)return null;var Y=U[0],Z=B&&E.parsePatterns[B]||E.parsePatterns[E.defaultParseWidth],x=Array.isArray(Z)?c(Z,function(z){return z.test(Y)}):m(Z,function(z){return z.test(Y)}),A;A=E.valueCallback?E.valueCallback(x):x,A=C.valueCallback?C.valueCallback(A):A;var B1=G.slice(Y.length);return{value:A,rest:B1}}}var m=function E(G,C){for(var B in G)if(Object.prototype.hasOwnProperty.call(G,B)&&C(G[B]))return B;return},c=function E(G,C){for(var B=0;B<G.length;B++)if(C(G[B]))return B;return};function y(E){return function(G){var C=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},B=G.match(E.matchPattern);if(!B)return null;var J=B[0],U=G.match(E.parsePattern);if(!U)return null;var Y=E.valueCallback?E.valueCallback(U[0]):U[0];Y=C.valueCallback?C.valueCallback(Y):Y;var Z=G.slice(J.length);return{value:Y,rest:Z}}}var d=/^(\d+)/i,g=/\d+/i,p={narrow:/^(tcn|scn)/i,abbreviated:/^(trước CN|sau CN)/i,wide:/^(trước Công Nguyên|sau Công Nguyên)/i},u={any:[/^t/i,/^s/i]},l={narrow:/^([1234]|i{1,3}v?)/i,abbreviated:/^q([1234]|i{1,3}v?)/i,wide:/^quý ([1234]|i{1,3}v?)/i},i={any:[/(1|i)$/i,/(2|ii)$/i,/(3|iii)$/i,/(4|iv)$/i]},n={narrow:/^(0?[2-9]|10|11|12|0?1)/i,abbreviated:/^thg[ _]?(0?[1-9](?!\d)|10|11|12)/i,wide:/^tháng ?(Một|Hai|Ba|Tư|Năm|Sáu|Bảy|Tám|Chín|Mười|Mười ?Một|Mười ?Hai|0?[1-9](?!\d)|10|11|12)/i},s={narrow:[/0?1$/i,/0?2/i,/3/,/4/,/5/,/6/,/7/,/8/,/9/,/10/,/11/,/12/],abbreviated:[/^thg[ _]?0?1(?!\d)/i,/^thg[ _]?0?2/i,/^thg[ _]?0?3/i,/^thg[ _]?0?4/i,/^thg[ _]?0?5/i,/^thg[ _]?0?6/i,/^thg[ _]?0?7/i,/^thg[ _]?0?8/i,/^thg[ _]?0?9/i,/^thg[ _]?10/i,/^thg[ _]?11/i,/^thg[ _]?12/i],wide:[/^tháng ?(Một|0?1(?!\d))/i,/^tháng ?(Hai|0?2)/i,/^tháng ?(Ba|0?3)/i,/^tháng ?(Tư|0?4)/i,/^tháng ?(Năm|0?5)/i,/^tháng ?(Sáu|0?6)/i,/^tháng ?(Bảy|0?7)/i,/^tháng ?(Tám|0?8)/i,/^tháng ?(Chín|0?9)/i,/^tháng ?(Mười|10)/i,/^tháng ?(Mười ?Một|11)/i,/^tháng ?(Mười ?Hai|12)/i]},o={narrow:/^(CN|T2|T3|T4|T5|T6|T7)/i,short:/^(CN|Th ?2|Th ?3|Th ?4|Th ?5|Th ?6|Th ?7)/i,abbreviated:/^(CN|Th ?2|Th ?3|Th ?4|Th ?5|Th ?6|Th ?7)/i,wide:/^(Chủ ?Nhật|Chúa ?Nhật|thứ ?Hai|thứ ?Ba|thứ ?Tư|thứ ?Năm|thứ ?Sáu|thứ ?Bảy)/i},r={narrow:[/CN/i,/2/i,/3/i,/4/i,/5/i,/6/i,/7/i],short:[/CN/i,/2/i,/3/i,/4/i,/5/i,/6/i,/7/i],abbreviated:[/CN/i,/2/i,/3/i,/4/i,/5/i,/6/i,/7/i],wide:[/(Chủ|Chúa) ?Nhật/i,/Hai/i,/Ba/i,/Tư/i,/Năm/i,/Sáu/i,/Bảy/i]},e={narrow:/^(a|p|nửa đêm|trưa|(giờ) (sáng|chiều|tối|đêm))/i,abbreviated:/^(am|pm|nửa đêm|trưa|(giờ) (sáng|chiều|tối|đêm))/i,wide:/^(ch[^i]*|sa|nửa đêm|trưa|(giờ) (sáng|chiều|tối|đêm))/i},a={any:{am:/^(a|sa)/i,pm:/^(p|ch[^i]*)/i,midnight:/nửa đêm/i,noon:/trưa/i,morning:/sáng/i,afternoon:/chiều/i,evening:/tối/i,night:/^đêm/i}},t={ordinalNumber:y({matchPattern:d,parsePattern:g,valueCallback:function E(G){return parseInt(G,10)}}),era:Q({matchPatterns:p,defaultMatchWidth:"wide",parsePatterns:u,defaultParseWidth:"any"}),quarter:Q({matchPatterns:l,defaultMatchWidth:"wide",parsePatterns:i,defaultParseWidth:"any",valueCallback:function E(G){return G+1}}),month:Q({matchPatterns:n,defaultMatchWidth:"wide",parsePatterns:s,defaultParseWidth:"wide"}),day:Q({matchPatterns:o,defaultMatchWidth:"wide",parsePatterns:r,defaultParseWidth:"wide"}),dayPeriod:Q({matchPatterns:e,defaultMatchWidth:"wide",parsePatterns:a,defaultParseWidth:"any"})},E1={code:"vi",formatDistance:K,formatLong:R,formatRelative:V,localize:b,match:t,options:{weekStartsOn:1,firstWeekContainsDate:1}};window.dateFns=D(D({},window.dateFns),{},{locale:D(D({},(I=window.dateFns)===null||I===void 0?void 0:I.locale),{},{vi:E1})})})();

//# debugId=44186F48A3F1546D64756e2164756e21
