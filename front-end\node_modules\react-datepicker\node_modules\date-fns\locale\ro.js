import { formatDistance } from "./ro/_lib/formatDistance.js";
import { formatLong } from "./ro/_lib/formatLong.js";
import { formatRelative } from "./ro/_lib/formatRelative.js";
import { localize } from "./ro/_lib/localize.js";
import { match } from "./ro/_lib/match.js";

/**
 * @category Locales
 * @summary Romanian locale.
 * @language Romanian
 * @iso-639-2 ron
 * <AUTHOR> [@jsergiu](https://github.com/jsergiu)
 * <AUTHOR> [@aocneanu](https://github.com/aocneanu)
 * <AUTHOR> [@gandesc](https://github.com/gandesc)
 */
export const ro = {
  code: "ro",
  formatDistance: formatDistance,
  formatLong: formatLong,
  formatRelative: formatRelative,
  localize: localize,
  match: match,
  options: {
    weekStartsOn: 1 /* Monday */,
    firstWeekContainsDate: 1,
  },
};

// Fallback for modularized imports:
export default ro;
