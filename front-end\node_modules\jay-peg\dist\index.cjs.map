{"mappings": ";;;;;;;;;;;;;;;;ACEA,MAAM,iCAAW,IAAI,0BAAS;IAC5B,YAAY,IAAI,0BAAS;IACzB,OAAO,IAAI,0BAAS;AACtB;AAEA,MAAM,kCAAY;IAChB,MAAM,IAAM;IACZ,QAAQ;IACR,QAAQ,IAAI,yBAAQ,gCAAU,CAAC,SAAW,OAAO,MAAM,GAAG;AAC5D;IAEA,2CAAe;;;;AEbR,MAAM,4CAAY,CAAC,OAAO;IAC/B,OAAO,KAAK,CAAC,OAAO;AACtB;AAEO,MAAM,4CAAe,CAAC,OAAO;IAClC,OAAO,AAAC,KAAK,CAAC,OAAO,IAAI,IAAK,KAAK,CAAC,SAAS,EAAE;AACjD;AAEO,MAAM,4CAAe,CAAC,OAAO;IAClC,OAAO,KAAK,CAAC,OAAO,GAAI,KAAK,CAAC,SAAS,EAAE,IAAI;AAC/C;AAEO,MAAM,4CAAe,CAAC,OAAO;IAClC,OAAO,wCAAY,OAAO,YAAY;AACxC;AAEO,MAAM,4CAAe,CAAC,OAAO;IAClC,OAAO,0CAAY,OAAO,YAAY;AACxC;AAEO,MAAM,4CAAwB,CAAC;IACpC,OAAO,MAAM,IAAI,CAAC,YAAY,CAAC,OAC7B,KAAK,QAAQ,CAAC,IAAI,QAAQ,CAAC,GAAG,MAC9B,IAAI,CAAC;AACT;AAEA,MAAM,gCAAU,IAAI,YAAY;AAEzB,MAAM,4CAAqB,CAAC;IACjC,OAAO,8BAAQ,MAAM,CAAC;AACxB;AAEO,MAAM,2CAAyB,CAAC;IACrC,MAAM,cAAc,OAAO,MAAM,CAAC,CAAC,QAAQ,MAAQ,SAAS,IAAI,MAAM,EAAE;IACxE,MAAM,oBAAoB,IAAI,WAAW;IAEzC,IAAI,SAAS;IAEb,OAAO,OAAO,CAAC,CAAC;QACd,kBAAkB,GAAG,CAAC,KAAK;QAC3B,UAAU,IAAI,MAAM;IACtB;IAEA,OAAO;AACT;AAEO,MAAM,0CAAc,CAAC,OAAO;IACjC,OACE,AAAC,KAAK,CAAC,OAAO,IAAI,KACjB,KAAK,CAAC,SAAS,EAAE,IAAI,KACrB,KAAK,CAAC,SAAS,EAAE,IAAI,IACtB,KAAK,CAAC,SAAS,EAAE;AAErB;AAEO,MAAM,4CAAc,CAAC,OAAO;IACjC,OACE,KAAK,CAAC,OAAO,GACZ,KAAK,CAAC,SAAS,EAAE,IAAI,IACrB,KAAK,CAAC,SAAS,EAAE,IAAI,KACrB,KAAK,CAAC,SAAS,EAAE,IAAI;AAE1B;;;AD3DA,MAAM;IACJ,OAAO,MAAM,EAAE,MAAM,EAAE;QACrB,MAAM,SAAS,CAAC;QAEhB,IAAI,SAAS,OAAO,MAAM,CAAC,KAAK,CAC9B,OAAO,GAAG,EACV,OAAO,GAAG,GAAG,OAAO,MAAM,GAAG;QAG/B,MAAO,OAAO,MAAM,GAAG,EAAG;YACxB,IAAI,SAAS;YAEb,MAAM,WAAW,EAAE;YACnB,MAAM,aAAa,CAAA,GAAA,yCAAQ,EAAE,QAAQ;YACrC,MAAM,UAAU,OAAO,KAAK,CAAC,QAAQ,SAAS;YAE9C,UAAU;YAEV,KAAK,MAAM,UAAU,QAAS;gBAC5B,SAAS,IAAI,CAAC,OAAO,KAAK,CAAC,QAAQ,SAAS;gBAC5C,UAAU;YACZ;YAEA,SAAS,OAAO,KAAK,CAAC;YAEtB,MAAM,CAAC,WAAW,GAAG,CAAA,GAAA,wCAAqB,EAAE;QAC9C;QAEA,OAAO,GAAG,IAAI,OAAO,MAAM,GAAG;QAE9B,OAAO;IACT;AACF;AAEA,MAAM,iDAA2B;IAC/B,MAAM,IAAM;IACZ,QAAQ;IACR,QAAQ,IAAI;AACd;IAEA,2CAAe;;;;AEzCf,MAAM,kCAAY;IAChB,MAAM,IAAM;IACZ,QAAQ;IACR,QAAQ,IAAI,yBACV,IAAI,0BAAS;QACX,YAAY,IAAI,0BAAS;QACzB,MAAM,IAAI,0BAAS;IACrB,IACA,CAAC,SAAW,AAAC,CAAA,OAAO,MAAM,GAAG,CAAA,IAAK;AAEtC;IAEA,2CAAe;;;;ACZf,MAAM,kCAAY;IAChB,MAAM,IAAM;IACZ,QAAQ;IACR,iBAAiB;AACnB;IAEA,2CAAe;;;;ACNf,MAAM,yCAAmB;IACvB,MAAM,IAAM;IACZ,UAAU,IAAI,4BAAW,0BAAS;AACpC;IAEA,2CAAe;;;;;ACMf,MAAM,6BAAO;IACX,KAAK;QACH,QAAQ;QACR,QAAQ;QACR,QAAQ;QACR,QAAQ;QACR,QAAQ;QACR,QAAQ;QACR,QAAQ;QACR,QAAQ;QACR,QAAQ;QACR,QAAQ;QACR,QAAQ;QACR,QAAQ;QACR,QAAQ;QACR,QAAQ;QACR,QAAQ;QACR,QAAQ;QACR,QAAQ;QACR,QAAQ;QACR,QAAQ;QACR,QAAQ;QACR,QAAQ;QACR,QAAQ;QACR,QAAQ;QACR,QAAQ;QACR,QAAQ;QACR,QAAQ;QACR,QAAQ;QACR,QAAQ;QACR,QAAQ;QACR,QAAQ;QACR,QAAQ;QACR,QAAQ;QACR,QAAQ;QACR,MAAM;QACN,MAAM;QACN,MAAM;QACN,MAAM;QACN,MAAM;QACN,MAAM;QACN,MAAM;QACN,MAAM;QACN,MAAM;QACN,MAAM;QACN,MAAM;QACN,MAAM;QACN,MAAM;QACN,MAAM;QACN,MAAM;QACN,MAAM;QACN,MAAM;QACN,MAAM;QACN,MAAM;QACN,MAAM;QACN,MAAM;QACN,MAAM;QACN,MAAM;QACN,MAAM;QACN,MAAM;QACN,MAAM;QACN,MAAM;QACN,MAAM;QACN,MAAM;QACN,MAAM;QACN,MAAM;QACN,MAAM;QACN,MAAM;QACN,MAAM;QACN,MAAM;QACN,MAAM;QACN,MAAM;QACN,MAAM;QACN,MAAM;QACN,MAAM;QACN,MAAM;QACN,MAAM;QACN,MAAM;QACN,MAAM;QACN,MAAM;QACN,MAAM;QACN,MAAM;QACN,MAAM;QACN,MAAM;QACN,MAAM;QACN,MAAM;QACN,MAAM;QACN,MAAM;QACN,MAAM;QACN,MAAM;QACN,MAAM;QACN,MAAM;QACN,MAAM;QACN,MAAM;QACN,MAAM;QACN,MAAM;QACN,MAAM;QACN,MAAM;QACN,MAAM;QACN,MAAM;QACN,MAAM;QACN,MAAM;QACN,MAAM;QACN,MAAM;IACR;IACA,KAAK;QACH,QAAQ;QACR,QAAQ;QACR,QAAQ;QACR,QAAQ;QACR,QAAQ;QACR,QAAQ;QACR,QAAQ;QACR,QAAQ;QACR,QAAQ;QACR,QAAQ;QACR,QAAQ;QACR,QAAQ;QACR,QAAQ;QACR,QAAQ;QACR,QAAQ;QACR,QAAQ;QACR,QAAQ;QACR,QAAQ;QACR,QAAQ;QACR,QAAQ;QACR,QAAQ;QACR,QAAQ;QACR,QAAQ;QACR,QAAQ;QACR,QAAQ;QACR,QAAQ;QACR,QAAQ;QACR,QAAQ;QACR,QAAQ;QACR,QAAQ;QACR,QAAQ;QACR,QAAQ;IACV;AACF;AAEA,MAAM;IACJ,YAAY,SAAS,CAAE;QACrB,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,KAAK,GAAG;YAAC;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;SAAE;IACtD;IAEA,aAAa,SAAS,EAAE,UAAU,EAAE,gBAAgB,EAAE;QACpD,OAAQ;YACN,KAAK;gBACH,OAAO,UAAU,QAAQ,CAAC,SAAS,OAAO,CAAC,QAAQ;YACrD,KAAK;gBACH,OAAO,UAAU,QAAQ,CAAC,QAAQ,OAAO,CAAC,QAAQ;YACpD,KAAK;gBACH,OAAO,OAAO,UAAU,QAAQ,CAAC;YACnC;gBACE,OAAO,IAAI,CAAC,4BAA4B,CAAC,WAAW,YAAY;QACpE;IACF;IAEA,6BAA6B,SAAS,EAAE,UAAU,EAAE,gBAAgB,EAAE;QACpE,MAAM,WAAW,EAAE;QACnB,MAAM,kBAAkB,IAAI,CAAC,KAAK,CAAC,WAAW;QAC9C,IAAK,IAAI,IAAI,GAAG,IAAI,kBAAkB,KAAK,EACzC,SAAS,IAAI,CAAC,IAAI,CAAC,kCAAkC,CAAC,WAAW,YAAY,IAAI;QAEnF,OAAO,SAAS,MAAM,KAAK,IAAI,QAAQ,CAAC,EAAE,GAAG;IAC/C;IAEA,mCAAmC,SAAS,EAAE,UAAU,EAAE,GAAG,EAAE;QAC7D,MAAM,SAAS,CAAC,MACd,IAAI,CAAC,SAAS,GACV,CAAA,GAAA,yCAAW,EAAE,WAAW,OACxB,CAAA,GAAA,yCAAW,EAAE,WAAW;QAE9B,MAAM,SAAS,CAAC,MACd,IAAI,CAAC,SAAS,GACV,CAAA,GAAA,yCAAW,EAAE,WAAW,OACxB,CAAA,GAAA,yCAAW,EAAE,WAAW;QAE9B,MAAM,QAAQ,CAAC,MACb,IAAI,CAAC,SAAS,GACV,CAAA,GAAA,uCAAU,EAAE,WAAW,OACvB,CAAA,GAAA,yCAAU,EAAE,WAAW;QAE7B,OAAQ;YACN,KAAK;gBACH,OAAO,CAAA,GAAA,yCAAQ,EAAE,WAAW;YAC9B,KAAK;gBACH,OAAO,OAAO;YAChB,KAAK;gBACH,OAAO,OAAO;YAChB,KAAK;gBACH,OAAO,OAAO,OAAO,OAAO,MAAM;YACpC,KAAK;gBACH,OAAO,MAAM;YACf,KAAK;gBACH,OAAO,MAAM,OAAO,MAAM,MAAM;QAEpC;IACF;IAEA,kBAAkB,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,KAAK,EAAE;QACnD,IAAI,MAAM,IAAI;QAEd,MAAM,UAAU,CAAC;QAEjB,MAAM,SAAS,CAAC,MACd,IAAI,CAAC,SAAS,GAAG,CAAA,GAAA,yCAAW,EAAE,QAAQ,OAAO,CAAA,GAAA,yCAAW,EAAE,QAAQ;QAEpE,MAAM,SAAS,CAAC,MACd,IAAI,CAAC,SAAS,GAAG,CAAA,GAAA,yCAAW,EAAE,QAAQ,OAAO,CAAA,GAAA,yCAAW,EAAE,QAAQ;QAEpE,MAAM,kBAAkB,OAAO;QAE/B,IAAK,IAAI,IAAI,GAAG,IAAI,iBAAiB,IAAK;YACxC,MAAM,aAAa,OAAO,KAAK,CAAC,KAAK,MAAM;YAC3C,MAAM,aAAa,OAAO,MAAM;YAChC,MAAM,mBAAmB,OAAO,MAAM;YACtC,MAAM,kBAAkB,IAAI,CAAC,KAAK,CAAC,WAAW;YAC9C,MAAM,aAAa,mBAAmB;YAEtC,IAAI,YAAY,OAAO,KAAK,CAAC,MAAM,GAAG,MAAM;YAE5C,IAAI,aAAa,GAAG;gBAClB,MAAM,aAAa,IAAI,CAAC,SAAS,GAC7B,CAAA,GAAA,yCAAW,EAAE,WAAW,KACxB,CAAA,GAAA,yCAAW,EAAE,WAAW;gBAE5B,YAAY,OAAO,KAAK,CAAC,YAAY,aAAa;YACpD;YAEA,MAAM,WAAW,IAAI,CAAC,YAAY,CAAC,WAAW,YAAY;YAE1D,MAAM,YAAY,IAAI,CAAC,SAAS,GAC5B,CAAA,GAAA,yCAAoB,EAAE,cACtB,CAAA,GAAA,yCAAoB,EAAE,WAAW,OAAO;YAE5C,MAAM,UAAU,IAAI,CAAC,UAAU;YAE/B,OAAO,CAAC,QAAQ,GAAG;YAEnB,OAAO;QACT;QAEA,OAAO;IACT;IAEA,OAAO,MAAM,EAAE,MAAM,EAAE;QACrB,MAAM,SAAS,OAAO,MAAM,CAAC,KAAK,CAAC,OAAO,GAAG,GAAG;QAChD,MAAM,mBAAmB,OAAO,gBAAgB;QAEhD,IAAI,mBAAmB,OAAO,MAAM,EAAE;YACpC,OAAO,GAAG,IAAI,OAAO,MAAM,CAAC,MAAM,GAAG;YACrC,OAAO,CAAC;QACV;QAEA,MAAM,UAAU,IAAI,CAAC,iBAAiB,CAAC,QAAQ,2BAAK,GAAG,EAAE;QACzD,MAAM,kBAAE,cAAc,qBAAE,iBAAiB,EAAE,GAAG;QAE9C,IAAI,gBACF,QAAQ,OAAO,GAAG,IAAI,CAAC,iBAAiB,CACtC,QACA,2BAAK,GAAG,EACR;QAIJ,IAAI,mBAAmB;YACrB,MAAM,MAAM;YACZ,QAAQ,OAAO,GAAG,IAAI,CAAC,iBAAiB,CAAC,QAAQ,2BAAK,GAAG,EAAE,KAAK;QAClE;QAEA,OAAO,GAAG,IAAI,OAAO,MAAM,CAAC,MAAM,GAAG;QAErC,OAAO;IACT;AACF;AAEA,MAAM,gCAAU,CAAC;IACf,MAAM,SAAS,YAAY,8BAAa;IACxC,MAAM,SAAS,YAAY,8BAAa;IAExC,OAAO,IAAI,0BAAS;QAClB,UAAU;QACV,kBAAkB;QAClB,SAAS,IAAI,iCAAW;IAC1B;AACF;AAEA,MAAM;IACJ,OAAO,MAAM,EAAE,MAAM,EAAE;QACrB,MAAM,YAAY,CAAA,GAAA,yCAAiB,EACjC,OAAO,MAAM,CAAC,KAAK,CAAC,OAAO,GAAG,EAAE,OAAO,GAAG,GAAG;QAG/C,MAAM,YAAY,cAAc;QAEhC,OAAO,GAAG,IAAI;QAEd,MAAM,OAAO,8BAAQ,WAAW,MAAM,CAAC,QAAQ;QAE/C,OAAO,KAAK,OAAO;IACrB;AACF;AAEA,MAAM,mCAAa;IACjB,MAAM,IAAM;IACZ,QAAQ;IACR,YAAY,IAAI,0BAAS;IACzB,SAAS,IAAI;AACf;IAEA,2CAAe;;;;ACpUf,MAAM,mCAAa;IACjB,MAAM,IAAM;IACZ,QAAQ;IACR,YAAY,IAAI,0BAAS;IACzB,SAAS;IACT,OAAO;IACP,UAAU;IACV,UAAU;IACV,gBAAgB;IAChB,iBAAiB;AACnB;IAEA,2CAAe;;;;ACZf,MAAM;IACJ,OAAO,MAAM,EAAE;QACb,MAAM,SAAS,OAAO,MAAM,CAAC,KAAK,CAAC,OAAO,GAAG;QAE7C,IAAI,SAAS;QACb,IAAI,IAAI,OAAO,OAAO,CAAC;QAEvB,MAAO,MAAM,GAAI;YACf,SAAS;YAET,MAAM,WAAW,MAAM,CAAC,SAAS,EAAE;YACnC,MAAM,eAAe,YAAY,QAAQ,YAAY;YAErD,IAAI,aAAa,QAAQ,CAAC,cAAc;YAExC,IAAI,OAAO,OAAO,CAAC,MAAM,IAAI;QAC/B;QAEA,OAAO,GAAG,IAAI;QAEd,OAAO,OAAO,KAAK,CAAC,GAAG;IACzB;AACF;AAEA,MAAM,kDAA4B,IAAI,0BAAS;IAC7C,uBAAuB;IACvB,oBAAoB,IAAI,0BAAS;AACnC;AAEA,MAAM,kCAAY;IAChB,MAAM,IAAM;IACZ,QAAQ;IACR,yBAAyB;IACzB,yBAAyB,IAAI,yBAC3B,iDACA,CAAC,SAAW,OAAO,uBAAuB;IAE5C,iBAAiB;IACjB,eAAe;IACf,4BAA4B,IAAI,0BAAS;IACzC,MAAM,IAAI;AACZ;IAEA,2CAAe;;;;AC3Cf,MAAM,4CAAsB,IAAI,0BAAS;IACvC,IAAI;IACJ,iBAAiB;IACjB,qBAAqB;AACvB;AAEA,MAAM,2CAAqB;IACzB,MAAM,IAAM;IACZ,QAAQ;IACR,WAAW;IACX,QAAQ;IACR,OAAO;IACP,oBAAoB;IACpB,YAAY,IAAI,yBACd,2CACA,CAAC,SAAW,OAAO,kBAAkB;AAEzC;IAEA,2CAAe;;;ACrBf,MAAM,2CAAqB;IACzB,MAAM,IAAM;AACd;IAEA,2CAAe;;;AXSf,MAAM,sCAAgB;IACpB,QAAQ;IACR,KAAK,IAAI,0BAAS,CAAC,SAAW,OAAO,MAAM,GAAG;AAChD;AAEA,MAAM,uCAAiB,MAAM,IAC1B,IAAI,CAAC,GACL,MAAM,CAAC,CAAC,KAAK,GAAG,IAAO,CAAA;QAAE,GAAG,GAAG;QAAE,CAAC,IAAI,OAAO,EAAE;IAAc,CAAA,GAAI,CAAC;AAErE,MAAM,+BAAS,IAAI,mCAAkB,6BAAY;IAC/C,GAAG,oCAAc;IACjB,QAAQ,CAAA,GAAA,wCAAiB;IACzB,QAAQ,CAAA,GAAA,wCAAiB;IACzB,QAAQ,CAAA,GAAA,wCAAiB;IACzB,QAAQ,CAAA,GAAA,wCAAiB;IACzB,QAAQ,CAAA,GAAA,wCAAuB;IAC/B,QAAQ,CAAA,GAAA,wCAAiB;IACzB,QAAQ,CAAA,GAAA,wCAAiB;IACzB,QAAQ,CAAA,GAAA,wCAAiB;IACzB,QAAQ,CAAA,GAAA,wCAAiB;IACzB,QAAQ,CAAA,GAAA,wCAAiB;IACzB,QAAQ,CAAA,GAAA,wCAAiB;IACzB,QAAQ,CAAA,GAAA,wCAAQ;IAChB,QAAQ,CAAA,GAAA,wCAAiB;IACzB,QAAQ,CAAA,GAAA,wCAAiB;IACzB,QAAQ,CAAA,GAAA,wCAAiB;IACzB,QAAQ,CAAA,GAAA,wCAAiB;IACzB,QAAQ,CAAA,GAAA,wCAAe;IACvB,QAAQ,CAAA,GAAA,wCAAQ;IAChB,QAAQ,CAAA,GAAA,wCAAQ;IAChB,QAAQ,CAAA,GAAA,wCAAQ;IAChB,QAAQ,CAAA,GAAA,wCAAS;IACjB,QAAQ,CAAA,GAAA,wCAAS;AACnB;AAEA,MAAM,6BAAO,IAAI,yBAAQ;AAEzB,MAAM,+BAAS,CAAC;IACd,MAAM,UAAU,2BAAK,UAAU,CAAC;IAChC,OAAO,QAAQ,GAAG,CAAC,CAAC,WAAE,OAAO,EAAE,GAAG,MAAM,GAAM,CAAA;YAAE,MAAM;YAAS,GAAG,IAAI;QAAC,CAAA;AACzE;IAEA,2CAAe;YAAE;AAAO", "sources": ["src/index.js", "src/markers/dac.js", "src/markers/dht.js", "src/markers/utils.js", "src/markers/dqt.js", "src/markers/dri.js", "src/markers/eoi.js", "src/markers/exif.js", "src/markers/jfif.js", "src/markers/sos.js", "src/markers/sof.js", "src/markers/soi.js"], "sourcesContent": ["import * as r from \"restructure\";\n\nimport DACMarker from \"./markers/dac.js\";\nimport DefineHuffmanTableMarker from \"./markers/dht.js\";\nimport DQTMarker from \"./markers/dqt.js\";\nimport DRIMarker from \"./markers/dri.js\";\nimport EndOfImageMarker from \"./markers/eoi.js\";\nimport EXIFMarker from \"./markers/exif.js\";\nimport J<PERSON>FMarker from \"./markers/jfif.js\";\nimport SOSMarker from \"./markers/sos.js\";\nimport StartOfFrameMarker from \"./markers/sof.js\";\nimport StartOfImageMarker from \"./markers/soi.js\";\n\nconst UnknownMarker = {\n  length: r.uint16be,\n  buf: new r.Buffer((parent) => parent.length - 2),\n};\n\nconst unknownMarkers = Array(63)\n  .fill(0)\n  .reduce((acc, v, i) => ({ ...acc, [i + 0xffc0]: UnknownMarker }), {});\n\nconst Marker = new r.VersionedStruct(r.uint16be, {\n  ...unknownMarkers,\n  0xffc0: StartOfFrameMarker,\n  0xffc1: StartOfFrameMarker,\n  0xffc2: StartOfFrameMarker,\n  0xffc3: StartOfFrameMarker,\n  0xffc4: DefineHuffmanTableMarker,\n  0xffc5: StartOfFrameMarker,\n  0xffc6: StartOfFrameMarker,\n  0xffc7: StartOfFrameMarker,\n  0xffc9: StartOfFrameMarker,\n  0xffca: StartOfFrameMarker,\n  0xffcb: StartOfFrameMarker,\n  0xffcc: DACMarker,\n  0xffcd: StartOfFrameMarker,\n  0xffce: StartOfFrameMarker,\n  0xffcf: StartOfFrameMarker,\n  0xffd8: StartOfImageMarker,\n  0xffd9: EndOfImageMarker,\n  0xffda: SOSMarker,\n  0xffdb: DQTMarker,\n  0xffdd: DRIMarker,\n  0xffe0: JFIFMarker,\n  0xffe1: EXIFMarker,\n});\n\nconst JPEG = new r.Array(Marker);\n\nconst decode = (buffer) => {\n  const markers = JPEG.fromBuffer(buffer);\n  return markers.map(({ version, ...rest }) => ({ type: version, ...rest }));\n};\n\nexport default { decode };\n", "import * as r from \"restructure\";\n\nconst DACTable = new r.Struct({\n  identifier: new r<PERSON><PERSON><PERSON><PERSON>(1),\n  value: new r<PERSON><PERSON>uffer(1),\n});\n\nconst DACMarker = {\n  name: () => \"DAC\",\n  length: r.uint16be,\n  tables: new r.<PERSON>y(DACTable, (parent) => parent.length / 2),\n};\n\nexport default DACMarker;\n", "import * as r from \"restructure\";\nimport { concatenateUint8Arrays, readUInt8 } from \"./utils.js\";\n\nclass HuffmanTableElements {\n  decode(stream, parent) {\n    const tables = {};\n\n    let buffer = stream.buffer.slice(\n      stream.pos,\n      stream.pos + parent.length - 2,\n    );\n\n    while (buffer.length > 0) {\n      let offset = 1;\n\n      const elements = [];\n      const identifier = readUInt8(buffer, 0);\n      const lengths = buffer.slice(offset, offset + 16);\n\n      offset += 16;\n\n      for (const length of lengths) {\n        elements.push(buffer.slice(offset, offset + length));\n        offset += length;\n      }\n\n      buffer = buffer.slice(offset);\n\n      tables[identifier] = concatenateUint8Arrays(elements);\n    }\n\n    stream.pos += parent.length - 2;\n\n    return tables;\n  }\n}\n\nconst DefineHuffmanTableMarker = {\n  name: () => \"DHT\",\n  length: r.uint16be,\n  tables: new HuffmanTableElements(),\n};\n\nexport default DefineHuffmanTableMarker;\n", "export const readUInt8 = (array, offset) => {\n  return array[offset];\n};\n\nexport const readUInt16BE = (array, offset) => {\n  return (array[offset] << 8) | array[offset + 1];\n};\n\nexport const readUInt16LE = (array, offset) => {\n  return array[offset] | (array[offset + 1] << 8);\n};\n\nexport const readUInt32BE = (array, offset) => {\n  return readInt32BE(array, offset) >>> 0;\n};\n\nexport const readUInt32LE = (array, offset) => {\n  return readInt32LE(array, offset) >>> 0;\n};\n\nexport const uint8ArrayToHexString = (uint8Array) => {\n  return Array.from(uint8Array, (byte) =>\n    byte.toString(16).padStart(2, \"0\"),\n  ).join(\"\");\n};\n\nconst decoder = new TextDecoder(\"utf-8\");\n\nexport const uint8ArrayToString = (uint8Array) => {\n  return decoder.decode(uint8Array);\n};\n\nexport const concatenateUint8Arrays = (arrays) => {\n  const totalLength = arrays.reduce((length, arr) => length + arr.length, 0);\n  const concatenatedArray = new Uint8Array(totalLength);\n\n  let offset = 0;\n\n  arrays.forEach((arr) => {\n    concatenatedArray.set(arr, offset);\n    offset += arr.length;\n  });\n\n  return concatenatedArray;\n};\n\nexport const readInt32BE = (array, offset) => {\n  return (\n    (array[offset] << 24) |\n    (array[offset + 1] << 16) |\n    (array[offset + 2] << 8) |\n    array[offset + 3]\n  );\n};\n\nexport const readInt32LE = (array, offset) => {\n  return (\n    array[offset] |\n    (array[offset + 1] << 8) |\n    (array[offset + 2] << 16) |\n    (array[offset + 3] << 24)\n  );\n};\n", "import * as r from \"restructure\";\n\nconst DQTMarker = {\n  name: () => \"DQT\",\n  length: r.uint16be,\n  tables: new r.A<PERSON>y(\n    new r.Struct({\n      identifier: new r<PERSON><PERSON>uffer(1),\n      data: new r<PERSON><PERSON>(64),\n    }),\n    (parent) => (parent.length - 2) / 65,\n  ),\n};\n\nexport default DQTMarker;\n", "import * as r from \"restructure\";\n\nconst DRIMarker = {\n  name: () => \"DRI\",\n  length: r.uint16be,\n  restartInterval: r.uint16be,\n};\n\nexport default DRIMarker;\n", "import * as r from \"restructure\";\n\nconst EndOfImageMarker = {\n  name: () => \"EOI\",\n  afterEOI: new r.Reserved(r.uint8, Infinity),\n};\n\nexport default EndOfImageMarker;\n", "import * as r from \"restructure\";\nimport {\n  readUInt8,\n  readUInt16BE,\n  readUInt16LE,\n  readUInt32BE,\n  readUInt32LE,\n  uint8ArrayToHexString,\n  uint8ArrayToString,\n  readInt32BE,\n  readInt32LE,\n} from \"./utils.js\";\n\nconst tags = {\n  ifd: {\n    \"010e\": \"imageDescription\",\n    \"010f\": \"make\",\n    \"011a\": \"xResolution\",\n    \"011b\": \"yResolution\",\n    \"011c\": \"planarConfiguration\",\n    \"012d\": \"transferFunction\",\n    \"013b\": \"artist\",\n    \"013e\": \"whitePoint\",\n    \"013f\": \"primaryChromaticities\",\n    \"0100\": \"imageWidth\",\n    \"0101\": \"imageHeight\",\n    \"0102\": \"bitsPerSample\",\n    \"0103\": \"compression\",\n    \"0106\": \"photometricInterpretation\",\n    \"0110\": \"model\",\n    \"0111\": \"stripOffsets\",\n    \"0112\": \"orientation\",\n    \"0115\": \"samplesPerPixel\",\n    \"0116\": \"rowsPerStrip\",\n    \"0117\": \"stripByteCounts\",\n    \"0128\": \"resolutionUnit\",\n    \"0131\": \"software\",\n    \"0132\": \"dateTime\",\n    \"0201\": \"jpegInterchangeFormat\",\n    \"0202\": \"jpegInterchangeFormatLength\",\n    \"0211\": \"ycbCrCoefficients\",\n    \"0212\": \"ycbCrSubSampling\",\n    \"0213\": \"ycbCrPositioning\",\n    \"0214\": \"referenceBlackWhite\",\n    \"829a\": \"exposureTime\",\n    \"829d\": \"fNumber\",\n    \"920a\": \"focalLength\",\n    \"927c\": \"makerNote\",\n    8298: \"copyright\",\n    8769: \"exifIFDPointer\",\n    8822: \"exposureProgram\",\n    8824: \"spectralSensitivity\",\n    8825: \"gpsInfoIFDPointer\",\n    8827: \"photographicSensitivity\",\n    8828: \"oecf\",\n    8830: \"sensitivityType\",\n    8831: \"standardOutputSensitivity\",\n    8832: \"recommendedExposureIndex\",\n    8833: \"isoSpeed\",\n    8834: \"isoSpeedLatitudeyyy\",\n    8835: \"isoSpeedLatitudezzz\",\n    9000: \"exifVersion\",\n    9003: \"dateTimeOriginal\",\n    9004: \"dateTimeDigitized\",\n    9101: \"componentsConfiguration\",\n    9102: \"compressedBitsPerPixel\",\n    9201: \"shutterSpeedValue\",\n    9202: \"apertureValue\",\n    9203: \"brightnessValue\",\n    9204: \"exposureBiasValue\",\n    9205: \"maxApertureValue\",\n    9206: \"subjectDistance\",\n    9207: \"meteringMode\",\n    9208: \"lightSource\",\n    9209: \"flash\",\n    9214: \"subjectArea\",\n    9286: \"userComment\",\n    9290: \"subSecTime\",\n    9291: \"subSecTimeOriginal\",\n    9292: \"subSecTimeDigitized\",\n    a000: \"flashpixVersion\",\n    a001: \"colorSpace\",\n    a002: \"pixelXDimension\",\n    a003: \"pixelYDimension\",\n    a004: \"relatedSoundFile\",\n    a005: \"interoperabilityIFDPointer\",\n    a20b: \"flashEnergy\",\n    a20c: \"spatialFrequencyResponse\",\n    a20e: \"focalPlaneXResolution\",\n    a20f: \"focalPlaneYResolution\",\n    a40a: \"sharpness\",\n    a40b: \"deviceSettingDescription\",\n    a40c: \"subjectDistanceRange\",\n    a210: \"focalPlaneResolutionUnit\",\n    a214: \"subjectLocation\",\n    a215: \"exposureIndex\",\n    a217: \"sensingMethod\",\n    a300: \"fileSource\",\n    a301: \"sceneType\",\n    a302: \"cfaPattern\",\n    a401: \"customRendered\",\n    a402: \"exposureMode\",\n    a403: \"whiteBalance\",\n    a404: \"digitalZoomRatio\",\n    a405: \"focalLengthIn35mmFilm\",\n    a406: \"sceneCaptureType\",\n    a407: \"gainControl\",\n    a408: \"contrast\",\n    a409: \"saturation\",\n    a420: \"imageUniqueID\",\n    a430: \"cameraOwnerName\",\n    a431: \"bodySerialNumber\",\n    a432: \"lensSpecification\",\n    a433: \"lensMake\",\n    a434: \"lensModel\",\n    a435: \"lensSerialNumber\",\n    a500: \"gamma\",\n  },\n  gps: {\n    \"0000\": \"gpsVersionID\",\n    \"0001\": \"gpsLatitudeRef\",\n    \"0002\": \"gpsLatitude\",\n    \"0003\": \"gpsLongitudeRef\",\n    \"0004\": \"gpsLongitude\",\n    \"0005\": \"gpsAltitudeRef\",\n    \"0006\": \"gpsAltitude\",\n    \"0007\": \"gpsTimeStamp\",\n    \"0008\": \"gpsSatellites\",\n    \"0009\": \"gpsStatus\",\n    \"000a\": \"gpsMeasureMode\",\n    \"000b\": \"gpsDOP\",\n    \"000c\": \"gpsSpeedRef\",\n    \"000d\": \"gpsSpeed\",\n    \"000e\": \"gpsTrackRef\",\n    \"000f\": \"gpsTrack\",\n    \"0010\": \"gpsImgDirectionRef\",\n    \"0011\": \"gpsImgDirection\",\n    \"0012\": \"gpsMapDatum\",\n    \"0013\": \"gpsDestLatitudeRef\",\n    \"0014\": \"gpsDestLatitude\",\n    \"0015\": \"gpsDestLongitudeRef\",\n    \"0016\": \"gpsDestLongitude\",\n    \"0017\": \"gpsDestBearingRef\",\n    \"0018\": \"gpsDestBearing\",\n    \"0019\": \"gpsDestDistanceRef\",\n    \"001a\": \"gpsDestDistance\",\n    \"001b\": \"gpsProcessingMethod\",\n    \"001c\": \"gpsAreaInformation\",\n    \"001d\": \"gpsDateStamp\",\n    \"001e\": \"gpsDifferential\",\n    \"001f\": \"gpsHPositioningError\",\n  },\n};\n\nclass IDFEntries {\n  constructor(bigEndian) {\n    this.bigEndian = bigEndian;\n    this.bytes = [0, 1, 1, 2, 4, 8, 1, 1, 2, 4, 8, 4, 8];\n  }\n\n  _getTagValue(dataValue, dataFormat, componentsNumber) {\n    switch (dataFormat) {\n      case 2:\n        return dataValue.toString(\"ascii\").replace(/\\0+$/, \"\");\n      case 129:\n        return dataValue.toString(\"utf8\").replace(/\\0+$/, \"\");\n      case 7:\n        return \"0x\" + dataValue.toString(\"hex\");\n      default:\n        return this._getTagValueForNumericalData(dataValue, dataFormat, componentsNumber);\n    }\n  }\n\n  _getTagValueForNumericalData(dataValue, dataFormat, componentsNumber) {\n    const tagValue = [];\n    const componentsBytes = this.bytes[dataFormat];\n    for (let i = 0; i < componentsNumber; i += 1) {\n      tagValue.push(this._getSingleTagValueForNumericalData(dataValue, dataFormat, i * componentsBytes));\n    }\n    return tagValue.length === 1 ? tagValue[0] : tagValue;\n  }\n\n  _getSingleTagValueForNumericalData(dataValue, dataFormat, pos) {\n    const uint16 = (pos) =>\n      this.bigEndian\n        ? readUInt16BE(dataValue, pos)\n        : readUInt16LE(dataValue, pos);\n\n    const uint32 = (pos) =>\n      this.bigEndian\n        ? readUInt32BE(dataValue, pos)\n        : readUInt32LE(dataValue, pos);\n\n    const int32 = (pos) =>\n      this.bigEndian\n        ? readInt32BE(dataValue, pos)\n        : readInt32LE(dataValue, pos);\n\n    switch (dataFormat) {\n      case 1:\n        return readUInt8(dataValue, pos)\n      case 3:\n        return uint16(pos);\n      case 4:\n        return uint32(pos);\n      case 5:\n        return uint32(pos) / uint32(pos + 4);\n      case 9:\n        return int32(pos);\n      case 10: {\n        return int32(pos) / int32(pos + 4);\n      }\n    }\n  }\n\n  _decodeIDFEntries(buffer, tags, offset, log = false) {\n    let pos = 2 + offset;\n\n    const entries = {};\n\n    const uint16 = (pos) =>\n      this.bigEndian ? readUInt16BE(buffer, pos) : readUInt16LE(buffer, pos);\n\n    const uint32 = (pos) =>\n      this.bigEndian ? readUInt32BE(buffer, pos) : readUInt32LE(buffer, pos);\n\n    const numberOfEntries = uint16(offset);\n\n    for (let i = 0; i < numberOfEntries; i++) {\n      const tagAddress = buffer.slice(pos, pos + 2);\n      const dataFormat = uint16(pos + 2);\n      const componentsNumber = uint32(pos + 4);\n      const componentsBytes = this.bytes[dataFormat];\n      const dataLength = componentsNumber * componentsBytes;\n\n      let dataValue = buffer.slice(pos + 8, pos + 12);\n\n      if (dataLength > 4) {\n        const dataOffset = this.bigEndian\n          ? readUInt32BE(dataValue, 0)\n          : readUInt32LE(dataValue, 0);\n\n        dataValue = buffer.slice(dataOffset, dataOffset + dataLength);\n      }\n\n      const tagValue = this._getTagValue(dataValue, dataFormat, componentsNumber);\n\n      const tagNumber = this.bigEndian\n        ? uint8ArrayToHexString(tagAddress)\n        : uint8ArrayToHexString(tagAddress.reverse());\n\n      const tagName = tags[tagNumber];\n\n      entries[tagName] = tagValue;\n\n      pos += 12;\n    }\n\n    return entries;\n  }\n\n  decode(stream, parent) {\n    const buffer = stream.buffer.slice(stream.pos - 8);\n    const offsetToFirstIFD = parent.offsetToFirstIFD;\n\n    if (offsetToFirstIFD > buffer.length) {\n      stream.pos += parent.parent.length - 16;\n      return {};\n    }\n\n    const entries = this._decodeIDFEntries(buffer, tags.ifd, offsetToFirstIFD);\n    const { exifIFDPointer, gpsInfoIFDPointer } = entries;\n\n    if (exifIFDPointer) {\n      entries.subExif = this._decodeIDFEntries(\n        buffer,\n        tags.ifd,\n        exifIFDPointer,\n      );\n    }\n\n    if (gpsInfoIFDPointer) {\n      const gps = gpsInfoIFDPointer;\n      entries.gpsInfo = this._decodeIDFEntries(buffer, tags.gps, gps, true);\n    }\n\n    stream.pos += parent.parent.length - 16;\n\n    return entries;\n  }\n}\n\nconst IFDData = (bigEndian) => {\n  const uint16 = bigEndian ? r.uint16be : r.uint16le;\n  const uint32 = bigEndian ? r.uint32be : r.uint32le;\n\n  return new r.Struct({\n    fortyTwo: uint16,\n    offsetToFirstIFD: uint32,\n    entries: new IDFEntries(bigEndian),\n  });\n};\n\nclass TIFFHeader {\n  decode(stream, parent) {\n    const byteOrder = uint8ArrayToString(\n      stream.buffer.slice(stream.pos, stream.pos + 2),\n    );\n\n    const bigEndian = byteOrder === \"MM\";\n\n    stream.pos += 2;\n\n    const data = IFDData(bigEndian).decode(stream, parent);\n\n    return data.entries;\n  }\n}\n\nconst EXIFMarker = {\n  name: () => \"EXIF\",\n  length: r.uint16be,\n  identifier: new r.String(6),\n  entries: new TIFFHeader(),\n};\n\nexport default EXIFMarker;\n", "import * as r from \"restructure\";\n\nconst JFIFMarker = {\n  name: () => \"JFIF\",\n  length: r.uint16be,\n  identifier: new r.String(5),\n  version: r.uint16be,\n  units: r.uint8,\n  xDensity: r.uint16be,\n  yDensity: r.uint16be,\n  thumbnailWidth: r.uint8,\n  thumbnailHeight: r.uint8,\n};\n\nexport default JFIFMarker;\n", "import * as r from \"restructure\";\n\nclass ImageData {\n  decode(stream) {\n    const buffer = stream.buffer.slice(stream.pos);\n\n    let length = 0;\n    let i = buffer.indexOf(0xff);\n\n    while (i !== -1) {\n      length = i;\n\n      const nextByte = buffer[length + 1];\n      const comesRestart = nextByte >= 0xd0 && nextByte <= 0xd7;\n\n      if (nextByte !== 0x00 && !comesRestart) break;\n\n      i = buffer.indexOf(0xff, i + 1);\n    }\n\n    stream.pos += length;\n\n    return buffer.slice(0, length);\n  }\n}\n\nconst SOSComponentSpecification = new r.Struct({\n  scanComponentSelector: r.uint8,\n  entropyCodingTable: new r.Buffer(1),\n});\n\nconst SOSMarker = {\n  name: () => \"SOS\",\n  length: r.uint16be,\n  numberOfImageComponents: r.uint8,\n  componentSpecifications: new r.Array(\n    SOSComponentSpecification,\n    (parent) => parent.numberOfImageComponents,\n  ),\n  startOfSpectral: r.uint8,\n  endOfSpectral: r.uint8,\n  successiveApproximationBit: new r.Buffer(1),\n  data: new ImageData(),\n};\n\nexport default SOSMarker;\n", "import * as r from \"restructure\";\n\nconst FrameColorComponent = new r.Struct({\n  id: r.uint8,\n  samplingFactors: r.uint8,\n  quantizationTableId: r.uint8,\n});\n\nconst StartOfFrameMarker = {\n  name: () => \"SOF\",\n  length: r.uint16be,\n  precision: r.uint8,\n  height: r.uint16be,\n  width: r.uint16be,\n  numberOfComponents: r.uint8,\n  components: new r.A<PERSON>y(\n    FrameColorComponent,\n    (parent) => parent.numberOfComponents,\n  ),\n};\n\nexport default StartOfFrameMarker;\n", "const StartOfImageMarker = {\n  name: () => \"SOI\",\n};\n\nexport default StartOfImageMarker;\n"], "names": [], "version": 3, "file": "index.cjs.map"}