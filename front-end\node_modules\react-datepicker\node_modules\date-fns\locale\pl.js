import { formatDistance } from "./pl/_lib/formatDistance.js";
import { formatLong } from "./pl/_lib/formatLong.js";
import { formatRelative } from "./pl/_lib/formatRelative.js";
import { localize } from "./pl/_lib/localize.js";
import { match } from "./pl/_lib/match.js";

/**
 * @category Locales
 * @summary Polish locale.
 * @language Polish
 * @iso-639-2 pol
 * <AUTHOR> [@ertrzyiks](https://github.com/ertrzyiks)
 * <AUTHOR> RAG [@justrag](https://github.com/justrag)
 * <AUTHOR> [@mikolajgrzyb](https://github.com/mikolajgrzyb)
 * <AUTHOR> [@mutisz](https://github.com/mutisz)
 */
export const pl = {
  code: "pl",
  formatDistance: formatDistance,
  formatLong: formatLong,
  formatRelative: formatRelative,
  localize: localize,
  match: match,
  options: {
    weekStartsOn: 1 /* Monday */,
    firstWeekContainsDate: 4,
  },
};

// Fallback for modularized imports:
export default pl;
