!function(t,r){"object"==typeof exports&&"undefined"!=typeof module?module.exports=r():"function"==typeof define&&define.amd?define(r):(t=t||self).SVGArcToCubicBezier=r()}(this,function(){"use strict";function O(t,r,a,n,e,o,i){var h=t.x,u=t.y;return{x:n*(h*=r)-e*(u*=a)+o,y:e*h+n*u+i}}function P(t,r,a,n){var e=t*a+r*n;return 1<e&&(e=1),e<-1&&(e=-1),(t*n-r*a<0?-1:1)*Math.acos(e)}var R=function(t,r){if(Array.isArray(t))return t;if(Symbol.iterator in Object(t))return function(t,r){var a=[],n=!0,e=!1,o=void 0;try{for(var i,h=t[Symbol.iterator]();!(n=(i=h.next()).done)&&(a.push(i.value),!r||a.length!==r);n=!0);}catch(t){e=!0,o=t}finally{try{!n&&h.return&&h.return()}finally{if(e)throw o}}return a}(t,r);throw new TypeError("Invalid attempt to destructure non-iterable instance")},V=2*Math.PI;return function(t){var r=t.px,a=t.py,n=t.cx,e=t.cy,u=t.rx,c=t.ry,o=t.xAxisRotation,i=void 0===o?0:o,h=t.largeArcFlag,f=void 0===h?0:h,s=t.sweepFlag,y=void 0===s?0:s,M=[];if(0===u||0===c)return[];var p=Math.sin(i*V/360),v=Math.cos(i*V/360),x=v*(r-n)/2+p*(a-e)/2,l=-p*(r-n)/2+v*(a-e)/2;if(0==x&&0==l)return[];u=Math.abs(u),c=Math.abs(c);var d=Math.pow(x,2)/Math.pow(u,2)+Math.pow(l,2)/Math.pow(c,2);1<d&&(u*=Math.sqrt(d),c*=Math.sqrt(d));var w=function(t,r,a,n,e,o,i,h,u,c,f,s){var y=Math.pow(e,2),M=Math.pow(o,2),p=Math.pow(f,2),v=Math.pow(s,2),x=y*M-y*v-M*p;x<0&&(x=0),x/=y*v+M*p;var l=(x=Math.sqrt(x)*(i===h?-1:1))*e/o*s,d=x*-o/e*f,w=c*l-u*d+(t+a)/2,b=u*l+c*d+(r+n)/2,m=(f-l)/e,A=(s-d)/o,g=(-f-l)/e,q=(-s-d)/o,S=P(1,0,m,A),j=P(m,A,g,q);return 0===h&&0<j&&(j-=V),1===h&&j<0&&(j+=V),[w,b,S,j]}(r,a,n,e,u,c,f,y,p,v,x,l),b=R(w,4),m=b[0],A=b[1],g=b[2],q=b[3],S=Math.abs(q)/(V/4);Math.abs(1-S)<1e-7&&(S=1);var j,F,I,T,z,B,C,E=Math.max(Math.ceil(S),1);q/=E;for(var G=0;G<E;G++)M.push((j=g,void 0,I=1.5707963267948966===(F=q)?.551915024494:-1.5707963267948966===F?-.551915024494:4/3*Math.tan(F/4),T=Math.cos(j),z=Math.sin(j),B=Math.cos(j+F),C=Math.sin(j+F),[{x:T-z*I,y:z+T*I},{x:B+C*I,y:C-B*I},{x:B,y:C}])),g+=q;return M.map(function(t){var r=O(t[0],u,c,v,p,m,A),a=r.x,n=r.y,e=O(t[1],u,c,v,p,m,A),o=e.x,i=e.y,h=O(t[2],u,c,v,p,m,A);return{x1:a,y1:n,x2:o,y2:i,x:h.x,y:h.y}})}});
