(()=>{var M;function R(B,C){var G=Object.keys(B);if(Object.getOwnPropertySymbols){var J=Object.getOwnPropertySymbols(B);C&&(J=J.filter(function(X){return Object.getOwnPropertyDescriptor(B,X).enumerable})),G.push.apply(G,J)}return G}function I(B){for(var C=1;C<arguments.length;C++){var G=arguments[C]!=null?arguments[C]:{};C%2?R(Object(G),!0).forEach(function(J){P(B,J,G[J])}):Object.getOwnPropertyDescriptors?Object.defineProperties(B,Object.getOwnPropertyDescriptors(G)):R(Object(G)).forEach(function(J){Object.defineProperty(B,J,Object.getOwnPropertyDescriptor(G,J))})}return B}function P(B,C,G){if(C=F(C),C in B)Object.defineProperty(B,C,{value:G,enumerable:!0,configurable:!0,writable:!0});else B[C]=G;return B}function F(B){var C=D(B,"string");return K(C)=="symbol"?C:String(C)}function D(B,C){if(K(B)!="object"||!B)return B;var G=B[Symbol.toPrimitive];if(G!==void 0){var J=G.call(B,C||"default");if(K(J)!="object")return J;throw new TypeError("@@toPrimitive must return a primitive value.")}return(C==="string"?String:Number)(B)}function v(B,C){return f(B)||b(B,C)||h(B,C)||w()}function w(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function h(B,C){if(!B)return;if(typeof B==="string")return V(B,C);var G=Object.prototype.toString.call(B).slice(8,-1);if(G==="Object"&&B.constructor)G=B.constructor.name;if(G==="Map"||G==="Set")return Array.from(B);if(G==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(G))return V(B,C)}function V(B,C){if(C==null||C>B.length)C=B.length;for(var G=0,J=new Array(C);G<C;G++)J[G]=B[G];return J}function b(B,C){var G=B==null?null:typeof Symbol!="undefined"&&B[Symbol.iterator]||B["@@iterator"];if(G!=null){var J,X,Z,U,H=[],Y=!0,Q=!1;try{if(Z=(G=G.call(B)).next,C===0){if(Object(G)!==G)return;Y=!1}else for(;!(Y=(J=Z.call(G)).done)&&(H.push(J.value),H.length!==C);Y=!0);}catch(q){Q=!0,X=q}finally{try{if(!Y&&G.return!=null&&(U=G.return(),Object(U)!==U))return}finally{if(Q)throw X}}return H}}function f(B){if(Array.isArray(B))return B}function K(B){return K=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(C){return typeof C}:function(C){return C&&typeof Symbol=="function"&&C.constructor===Symbol&&C!==Symbol.prototype?"symbol":typeof C},K(B)}var k=Object.defineProperty,LB=function B(C,G){for(var J in G)k(C,J,{get:G[J],enumerable:!0,configurable:!0,set:function X(Z){return G[J]=function(){return Z}}})},m={lessThanXSeconds:{one:"\u043F\u043E\u043C\u0430\u043B\u043A\u0443 \u043E\u0434 \u0441\u0435\u043A\u0443\u043D\u0434\u0430",other:"\u043F\u043E\u043C\u0430\u043B\u043A\u0443 \u043E\u0434 {{count}} \u0441\u0435\u043A\u0443\u043D\u0434\u0438"},xSeconds:{one:"1 \u0441\u0435\u043A\u0443\u043D\u0434\u0430",other:"{{count}} \u0441\u0435\u043A\u0443\u043D\u0434\u0438"},halfAMinute:"\u043F\u043E\u043B\u043E\u0432\u0438\u043D\u0430 \u043C\u0438\u043D\u0443\u0442\u0430",lessThanXMinutes:{one:"\u043F\u043E\u043C\u0430\u043B\u043A\u0443 \u043E\u0434 \u043C\u0438\u043D\u0443\u0442\u0430",other:"\u043F\u043E\u043C\u0430\u043B\u043A\u0443 \u043E\u0434 {{count}} \u043C\u0438\u043D\u0443\u0442\u0438"},xMinutes:{one:"1 \u043C\u0438\u043D\u0443\u0442\u0430",other:"{{count}} \u043C\u0438\u043D\u0443\u0442\u0438"},aboutXHours:{one:"\u043E\u043A\u043E\u043B\u0443 1 \u0447\u0430\u0441",other:"\u043E\u043A\u043E\u043B\u0443 {{count}} \u0447\u0430\u0441\u0430"},xHours:{one:"1 \u0447\u0430\u0441",other:"{{count}} \u0447\u0430\u0441\u0430"},xDays:{one:"1 \u0434\u0435\u043D",other:"{{count}} \u0434\u0435\u043D\u0430"},aboutXWeeks:{one:"\u043E\u043A\u043E\u043B\u0443 1 \u043D\u0435\u0434\u0435\u043B\u0430",other:"\u043E\u043A\u043E\u043B\u0443 {{count}} \u043C\u0435\u0441\u0435\u0446\u0438"},xWeeks:{one:"1 \u043D\u0435\u0434\u0435\u043B\u0430",other:"{{count}} \u043D\u0435\u0434\u0435\u043B\u0438"},aboutXMonths:{one:"\u043E\u043A\u043E\u043B\u0443 1 \u043C\u0435\u0441\u0435\u0446",other:"\u043E\u043A\u043E\u043B\u0443 {{count}} \u043D\u0435\u0434\u0435\u043B\u0438"},xMonths:{one:"1 \u043C\u0435\u0441\u0435\u0446",other:"{{count}} \u043C\u0435\u0441\u0435\u0446\u0438"},aboutXYears:{one:"\u043E\u043A\u043E\u043B\u0443 1 \u0433\u043E\u0434\u0438\u043D\u0430",other:"\u043E\u043A\u043E\u043B\u0443 {{count}} \u0433\u043E\u0434\u0438\u043D\u0438"},xYears:{one:"1 \u0433\u043E\u0434\u0438\u043D\u0430",other:"{{count}} \u0433\u043E\u0434\u0438\u043D\u0438"},overXYears:{one:"\u043F\u043E\u0432\u0435\u045C\u0435 \u043E\u0434 1 \u0433\u043E\u0434\u0438\u043D\u0430",other:"\u043F\u043E\u0432\u0435\u045C\u0435 \u043E\u0434 {{count}} \u0433\u043E\u0434\u0438\u043D\u0438"},almostXYears:{one:"\u0431\u0435\u0437\u043C\u0430\u043B\u043A\u0443 1 \u0433\u043E\u0434\u0438\u043D\u0430",other:"\u0431\u0435\u0437\u043C\u0430\u043B\u043A\u0443 {{count}} \u0433\u043E\u0434\u0438\u043D\u0438"}},_=function B(C,G,J){var X,Z=m[C];if(typeof Z==="string")X=Z;else if(G===1)X=Z.one;else X=Z.other.replace("{{count}}",String(G));if(J!==null&&J!==void 0&&J.addSuffix)if(J.comparison&&J.comparison>0)return"\u0437\u0430 "+X;else return"\u043F\u0440\u0435\u0434 "+X;return X};function x(B){return function(){var C=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},G=C.width?String(C.width):B.defaultWidth,J=B.formats[G]||B.formats[B.defaultWidth];return J}}var g={full:"EEEE, dd MMMM yyyy",long:"dd MMMM yyyy",medium:"dd MMM yyyy",short:"dd/MM/yyyy"},c={full:"HH:mm:ss zzzz",long:"HH:mm:ss z",medium:"HH:mm:ss",short:"H:mm"},y={any:"{{date}} {{time}}"},u={date:x({formats:g,defaultWidth:"full"}),time:x({formats:c,defaultWidth:"full"}),dateTime:x({formats:y,defaultWidth:"any"})},OB=7,p=365.2425,d=Math.pow(10,8)*24*60*60*1000,PB=-d,FB=604800000,DB=86400000,vB=60000,wB=3600000,hB=1000,bB=525600,fB=43200,kB=1440,mB=60,_B=3,gB=12,cB=4,l=3600,yB=60,A=l*24,uB=A*7,i=A*p,s=i/12,pB=s*3,W=Symbol.for("constructDateFrom");function S(B,C){if(typeof B==="function")return B(C);if(B&&K(B)==="object"&&W in B)return B[W](C);if(B instanceof Date)return new B.constructor(C);return new Date(C)}function n(B){for(var C=arguments.length,G=new Array(C>1?C-1:0),J=1;J<C;J++)G[J-1]=arguments[J];var X=S.bind(null,B||G.find(function(Z){return K(Z)==="object"}));return G.map(X)}function r(){return $}function dB(B){$=B}var $={};function o(B,C){return S(C||B,B)}function j(B,C){var G,J,X,Z,U,H,Y=r(),Q=(G=(J=(X=(Z=C===null||C===void 0?void 0:C.weekStartsOn)!==null&&Z!==void 0?Z:C===null||C===void 0||(U=C.locale)===null||U===void 0||(U=U.options)===null||U===void 0?void 0:U.weekStartsOn)!==null&&X!==void 0?X:Y.weekStartsOn)!==null&&J!==void 0?J:(H=Y.locale)===null||H===void 0||(H=H.options)===null||H===void 0?void 0:H.weekStartsOn)!==null&&G!==void 0?G:0,q=o(B,C===null||C===void 0?void 0:C.in),E=q.getDay(),jB=(E<Q?7:0)+E-Q;return q.setDate(q.getDate()-jB),q.setHours(0,0,0,0),q}function L(B,C,G){var J=n(G===null||G===void 0?void 0:G.in,B,C),X=v(J,2),Z=X[0],U=X[1];return+j(Z,G)===+j(U,G)}function a(B){var C=z[B];switch(B){case 0:case 3:case 6:return"'\u043C\u0438\u043D\u0430\u0442\u0430\u0442\u0430 "+C+" \u0432\u043E' p";case 1:case 2:case 4:case 5:return"'\u043C\u0438\u043D\u0430\u0442\u0438\u043E\u0442 "+C+" \u0432\u043E' p"}}function O(B){var C=z[B];switch(B){case 0:case 3:case 6:return"'\u043E\u0432\u0430 "+C+" \u0432o' p";case 1:case 2:case 4:case 5:return"'\u043E\u0432\u043E\u0458 "+C+" \u0432o' p"}}function t(B){var C=z[B];switch(B){case 0:case 3:case 6:return"'\u0441\u043B\u0435\u0434\u043D\u0430\u0442\u0430 "+C+" \u0432o' p";case 1:case 2:case 4:case 5:return"'\u0441\u043B\u0435\u0434\u043D\u0438\u043E\u0442 "+C+" \u0432o' p"}}var z=["\u043D\u0435\u0434\u0435\u043B\u0430","\u043F\u043E\u043D\u0435\u0434\u0435\u043B\u043D\u0438\u043A","\u0432\u0442\u043E\u0440\u043D\u0438\u043A","\u0441\u0440\u0435\u0434\u0430","\u0447\u0435\u0442\u0432\u0440\u0442\u043E\u043A","\u043F\u0435\u0442\u043E\u043A","\u0441\u0430\u0431\u043E\u0442\u0430"],e={lastWeek:function B(C,G,J){var X=C.getDay();if(L(C,G,J))return O(X);else return a(X)},yesterday:"'\u0432\u0447\u0435\u0440\u0430 \u0432\u043E' p",today:"'\u0434\u0435\u043D\u0435\u0441 \u0432\u043E' p",tomorrow:"'\u0443\u0442\u0440\u0435 \u0432\u043E' p",nextWeek:function B(C,G,J){var X=C.getDay();if(L(C,G,J))return O(X);else return t(X)},other:"P"},BB=function B(C,G,J,X){var Z=e[C];if(typeof Z==="function")return Z(G,J,X);return Z};function N(B){return function(C,G){var J=G!==null&&G!==void 0&&G.context?String(G.context):"standalone",X;if(J==="formatting"&&B.formattingValues){var Z=B.defaultFormattingWidth||B.defaultWidth,U=G!==null&&G!==void 0&&G.width?String(G.width):Z;X=B.formattingValues[U]||B.formattingValues[Z]}else{var H=B.defaultWidth,Y=G!==null&&G!==void 0&&G.width?String(G.width):B.defaultWidth;X=B.values[Y]||B.values[H]}var Q=B.argumentCallback?B.argumentCallback(C):C;return X[Q]}}var CB={narrow:["\u043F\u0440.\u043D.\u0435.","\u043D.\u0435."],abbreviated:["\u043F\u0440\u0435\u0434 \u043D. \u0435.","\u043D. \u0435."],wide:["\u043F\u0440\u0435\u0434 \u043D\u0430\u0448\u0430\u0442\u0430 \u0435\u0440\u0430","\u043D\u0430\u0448\u0430\u0442\u0430 \u0435\u0440\u0430"]},GB={narrow:["1","2","3","4"],abbreviated:["1-\u0432\u0438 \u043A\u0432.","2-\u0440\u0438 \u043A\u0432.","3-\u0442\u0438 \u043A\u0432.","4-\u0442\u0438 \u043A\u0432."],wide:["1-\u0432\u0438 \u043A\u0432\u0430\u0440\u0442\u0430\u043B","2-\u0440\u0438 \u043A\u0432\u0430\u0440\u0442\u0430\u043B","3-\u0442\u0438 \u043A\u0432\u0430\u0440\u0442\u0430\u043B","4-\u0442\u0438 \u043A\u0432\u0430\u0440\u0442\u0430\u043B"]},JB={abbreviated:["\u0458\u0430\u043D","\u0444\u0435\u0432","\u043C\u0430\u0440","\u0430\u043F\u0440","\u043C\u0430\u0458","\u0458\u0443\u043D","\u0458\u0443\u043B","\u0430\u0432\u0433","\u0441\u0435\u043F\u0442","\u043E\u043A\u0442","\u043D\u043E\u0435\u043C","\u0434\u0435\u043A"],wide:["\u0458\u0430\u043D\u0443\u0430\u0440\u0438","\u0444\u0435\u0432\u0440\u0443\u0430\u0440\u0438","\u043C\u0430\u0440\u0442","\u0430\u043F\u0440\u0438\u043B","\u043C\u0430\u0458","\u0458\u0443\u043D\u0438","\u0458\u0443\u043B\u0438","\u0430\u0432\u0433\u0443\u0441\u0442","\u0441\u0435\u043F\u0442\u0435\u043C\u0432\u0440\u0438","\u043E\u043A\u0442\u043E\u043C\u0432\u0440\u0438","\u043D\u043E\u0435\u043C\u0432\u0440\u0438","\u0434\u0435\u043A\u0435\u043C\u0432\u0440\u0438"]},XB={narrow:["\u041D","\u041F","\u0412","\u0421","\u0427","\u041F","\u0421"],short:["\u043D\u0435","\u043F\u043E","\u0432\u0442","\u0441\u0440","\u0447\u0435","\u043F\u0435","\u0441\u0430"],abbreviated:["\u043D\u0435\u0434","\u043F\u043E\u043D","\u0432\u0442\u043E","\u0441\u0440\u0435","\u0447\u0435\u0442","\u043F\u0435\u0442","\u0441\u0430\u0431"],wide:["\u043D\u0435\u0434\u0435\u043B\u0430","\u043F\u043E\u043D\u0435\u0434\u0435\u043B\u043D\u0438\u043A","\u0432\u0442\u043E\u0440\u043D\u0438\u043A","\u0441\u0440\u0435\u0434\u0430","\u0447\u0435\u0442\u0432\u0440\u0442\u043E\u043A","\u043F\u0435\u0442\u043E\u043A","\u0441\u0430\u0431\u043E\u0442\u0430"]},ZB={wide:{am:"\u043F\u0440\u0435\u0442\u043F\u043B\u0430\u0434\u043D\u0435",pm:"\u043F\u043E\u043F\u043B\u0430\u0434\u043D\u0435",midnight:"\u043F\u043E\u043B\u043D\u043E\u045C",noon:"\u043D\u0430\u043F\u043B\u0430\u0434\u043D\u0435",morning:"\u043D\u0430\u0443\u0442\u0440\u043E",afternoon:"\u043F\u043E\u043F\u043B\u0430\u0434\u043D\u0435",evening:"\u043D\u0430\u0432\u0435\u0447\u0435\u0440",night:"\u043D\u043E\u045C\u0435"}},UB=function B(C,G){var J=Number(C),X=J%100;if(X>20||X<10)switch(X%10){case 1:return J+"-\u0432\u0438";case 2:return J+"-\u0440\u0438";case 7:case 8:return J+"-\u043C\u0438"}return J+"-\u0442\u0438"},HB={ordinalNumber:UB,era:N({values:CB,defaultWidth:"wide"}),quarter:N({values:GB,defaultWidth:"wide",argumentCallback:function B(C){return C-1}}),month:N({values:JB,defaultWidth:"wide"}),day:N({values:XB,defaultWidth:"wide"}),dayPeriod:N({values:ZB,defaultWidth:"wide"})};function T(B){return function(C){var G=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},J=G.width,X=J&&B.matchPatterns[J]||B.matchPatterns[B.defaultMatchWidth],Z=C.match(X);if(!Z)return null;var U=Z[0],H=J&&B.parsePatterns[J]||B.parsePatterns[B.defaultParseWidth],Y=Array.isArray(H)?YB(H,function(E){return E.test(U)}):QB(H,function(E){return E.test(U)}),Q;Q=B.valueCallback?B.valueCallback(Y):Y,Q=G.valueCallback?G.valueCallback(Q):Q;var q=C.slice(U.length);return{value:Q,rest:q}}}function QB(B,C){for(var G in B)if(Object.prototype.hasOwnProperty.call(B,G)&&C(B[G]))return G;return}function YB(B,C){for(var G=0;G<B.length;G++)if(C(B[G]))return G;return}function qB(B){return function(C){var G=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},J=C.match(B.matchPattern);if(!J)return null;var X=J[0],Z=C.match(B.parsePattern);if(!Z)return null;var U=B.valueCallback?B.valueCallback(Z[0]):Z[0];U=G.valueCallback?G.valueCallback(U):U;var H=C.slice(X.length);return{value:U,rest:H}}}var KB=/^(\d+)(-?[врмт][и])?/i,EB=/\d+/i,NB={narrow:/^((пр)?н\.?\s?е\.?)/i,abbreviated:/^((пр)?н\.?\s?е\.?)/i,wide:/^(пред нашата ера|нашата ера)/i},TB={any:[/^п/i,/^н/i]},IB={narrow:/^[1234]/i,abbreviated:/^[1234](-?[врт]?и?)? кв.?/i,wide:/^[1234](-?[врт]?и?)? квартал/i},MB={any:[/1/i,/2/i,/3/i,/4/i]},xB={narrow:/^[нпвсч]/i,short:/^(не|по|вт|ср|че|пе|са)/i,abbreviated:/^(нед|пон|вто|сре|чет|пет|саб)/i,wide:/^(недела|понеделник|вторник|среда|четврток|петок|сабота)/i},zB={narrow:[/^н/i,/^п/i,/^в/i,/^с/i,/^ч/i,/^п/i,/^с/i],any:[/^н[ед]/i,/^п[он]/i,/^вт/i,/^ср/i,/^ч[ет]/i,/^п[ет]/i,/^с[аб]/i]},RB={abbreviated:/^(јан|фев|мар|апр|мај|јун|јул|авг|сеп|окт|ноем|дек)/i,wide:/^(јануари|февруари|март|април|мај|јуни|јули|август|септември|октомври|ноември|декември)/i},VB={any:[/^ја/i,/^Ф/i,/^мар/i,/^ап/i,/^мај/i,/^јун/i,/^јул/i,/^ав/i,/^се/i,/^окт/i,/^но/i,/^де/i]},AB={any:/^(претп|попл|полноќ|утро|пладне|вечер|ноќ)/i},WB={any:{am:/претпладне/i,pm:/попладне/i,midnight:/полноќ/i,noon:/напладне/i,morning:/наутро/i,afternoon:/попладне/i,evening:/навечер/i,night:/ноќе/i}},SB={ordinalNumber:qB({matchPattern:KB,parsePattern:EB,valueCallback:function B(C){return parseInt(C,10)}}),era:T({matchPatterns:NB,defaultMatchWidth:"wide",parsePatterns:TB,defaultParseWidth:"any"}),quarter:T({matchPatterns:IB,defaultMatchWidth:"wide",parsePatterns:MB,defaultParseWidth:"any",valueCallback:function B(C){return C+1}}),month:T({matchPatterns:RB,defaultMatchWidth:"wide",parsePatterns:VB,defaultParseWidth:"any"}),day:T({matchPatterns:xB,defaultMatchWidth:"wide",parsePatterns:zB,defaultParseWidth:"any"}),dayPeriod:T({matchPatterns:AB,defaultMatchWidth:"any",parsePatterns:WB,defaultParseWidth:"any"})},$B={code:"mk",formatDistance:_,formatLong:u,formatRelative:BB,localize:HB,match:SB,options:{weekStartsOn:1,firstWeekContainsDate:4}};window.dateFns=I(I({},window.dateFns),{},{locale:I(I({},(M=window.dateFns)===null||M===void 0?void 0:M.locale),{},{mk:$B})})})();

//# debugId=79B0EFD8A1F6EA3F64756E2164756E21
