var S=function(J,G){var Z=Object.keys(J);if(Object.getOwnPropertySymbols){var A=Object.getOwnPropertySymbols(J);G&&(A=A.filter(function(L){return Object.getOwnPropertyDescriptor(J,L).enumerable})),Z.push.apply(Z,A)}return Z},V=function(J){for(var G=1;G<arguments.length;G++){var Z=arguments[G]!=null?arguments[G]:{};G%2?S(Object(Z),!0).forEach(function(A){T0(J,A,Z[A])}):Object.getOwnPropertyDescriptors?Object.defineProperties(J,Object.getOwnPropertyDescriptors(Z)):S(Object(Z)).forEach(function(A){Object.defineProperty(J,A,Object.getOwnPropertyDescriptor(Z,A))})}return J},T0=function(J,G,Z){if(G=K0(G),G in J)Object.defineProperty(J,G,{value:Z,enumerable:!0,configurable:!0,writable:!0});else J[G]=Z;return J},K0=function(J){var G=N0(J,"string");return z(G)=="symbol"?G:String(G)},N0=function(J,G){if(z(J)!="object"||!J)return J;var Z=J[Symbol.toPrimitive];if(Z!==void 0){var A=Z.call(J,G||"default");if(z(A)!="object")return A;throw new TypeError("@@toPrimitive must return a primitive value.")}return(G==="string"?String:Number)(J)},z=function(J){return z=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(G){return typeof G}:function(G){return G&&typeof Symbol=="function"&&G.constructor===Symbol&&G!==Symbol.prototype?"symbol":typeof G},z(J)};(function(J){var G=Object.defineProperty,Z=function H(B,U){for(var X in U)G(B,X,{get:U[X],enumerable:!0,configurable:!0,set:function C(E){return U[X]=function(){return E}}})},A={lessThanXSeconds:{one:"\u043F\u043E-\u043C\u0430\u043B\u043A\u043E \u043E\u0442 \u0441\u0435\u043A\u0443\u043D\u0434\u0430",other:"\u043F\u043E-\u043C\u0430\u043B\u043A\u043E \u043E\u0442 {{count}} \u0441\u0435\u043A\u0443\u043D\u0434\u0438"},xSeconds:{one:"1 \u0441\u0435\u043A\u0443\u043D\u0434\u0430",other:"{{count}} \u0441\u0435\u043A\u0443\u043D\u0434\u0438"},halfAMinute:"\u043F\u043E\u043B\u043E\u0432\u0438\u043D \u043C\u0438\u043D\u0443\u0442\u0430",lessThanXMinutes:{one:"\u043F\u043E-\u043C\u0430\u043B\u043A\u043E \u043E\u0442 \u043C\u0438\u043D\u0443\u0442\u0430",other:"\u043F\u043E-\u043C\u0430\u043B\u043A\u043E \u043E\u0442 {{count}} \u043C\u0438\u043D\u0443\u0442\u0438"},xMinutes:{one:"1 \u043C\u0438\u043D\u0443\u0442\u0430",other:"{{count}} \u043C\u0438\u043D\u0443\u0442\u0438"},aboutXHours:{one:"\u043E\u043A\u043E\u043B\u043E \u0447\u0430\u0441",other:"\u043E\u043A\u043E\u043B\u043E {{count}} \u0447\u0430\u0441\u0430"},xHours:{one:"1 \u0447\u0430\u0441",other:"{{count}} \u0447\u0430\u0441\u0430"},xDays:{one:"1 \u0434\u0435\u043D",other:"{{count}} \u0434\u043D\u0438"},aboutXWeeks:{one:"\u043E\u043A\u043E\u043B\u043E \u0441\u0435\u0434\u043C\u0438\u0446\u0430",other:"\u043E\u043A\u043E\u043B\u043E {{count}} \u0441\u0435\u0434\u043C\u0438\u0446\u0438"},xWeeks:{one:"1 \u0441\u0435\u0434\u043C\u0438\u0446\u0430",other:"{{count}} \u0441\u0435\u0434\u043C\u0438\u0446\u0438"},aboutXMonths:{one:"\u043E\u043A\u043E\u043B\u043E \u043C\u0435\u0441\u0435\u0446",other:"\u043E\u043A\u043E\u043B\u043E {{count}} \u043C\u0435\u0441\u0435\u0446\u0430"},xMonths:{one:"1 \u043C\u0435\u0441\u0435\u0446",other:"{{count}} \u043C\u0435\u0441\u0435\u0446\u0430"},aboutXYears:{one:"\u043E\u043A\u043E\u043B\u043E \u0433\u043E\u0434\u0438\u043D\u0430",other:"\u043E\u043A\u043E\u043B\u043E {{count}} \u0433\u043E\u0434\u0438\u043D\u0438"},xYears:{one:"1 \u0433\u043E\u0434\u0438\u043D\u0430",other:"{{count}} \u0433\u043E\u0434\u0438\u043D\u0438"},overXYears:{one:"\u043D\u0430\u0434 \u0433\u043E\u0434\u0438\u043D\u0430",other:"\u043D\u0430\u0434 {{count}} \u0433\u043E\u0434\u0438\u043D\u0438"},almostXYears:{one:"\u043F\u043E\u0447\u0442\u0438 \u0433\u043E\u0434\u0438\u043D\u0430",other:"\u043F\u043E\u0447\u0442\u0438 {{count}} \u0433\u043E\u0434\u0438\u043D\u0438"}},L=function H(B,U,X){var C,E=A[B];if(typeof E==="string")C=E;else if(U===1)C=E.one;else C=E.other.replace("{{count}}",String(U));if(X!==null&&X!==void 0&&X.addSuffix)if(X.comparison&&X.comparison>0)return"\u0441\u043B\u0435\u0434 "+C;else return"\u043F\u0440\u0435\u0434\u0438 "+C;return C};function x(H){return function(){var B=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},U=B.width?String(B.width):H.defaultWidth,X=H.formats[U]||H.formats[H.defaultWidth];return X}}var W={full:"EEEE, dd MMMM yyyy",long:"dd MMMM yyyy",medium:"dd MMM yyyy",short:"dd/MM/yyyy"},F={full:"HH:mm:ss zzzz",long:"HH:mm:ss z",medium:"HH:mm:ss",short:"H:mm"},w={any:"{{date}} {{time}}"},h={date:x({formats:W,defaultWidth:"full"}),time:x({formats:F,defaultWidth:"full"}),dateTime:x({formats:w,defaultWidth:"any"})};function j(H){var B=Object.prototype.toString.call(H);if(H instanceof Date||z(H)==="object"&&B==="[object Date]")return new H.constructor(+H);else if(typeof H==="number"||B==="[object Number]"||typeof H==="string"||B==="[object String]")return new Date(H);else return new Date(NaN)}function _(){return D}function z0(H){D=H}var D={};function P(H,B){var U,X,C,E,Y,I,T=_(),Q=(U=(X=(C=(E=B===null||B===void 0?void 0:B.weekStartsOn)!==null&&E!==void 0?E:B===null||B===void 0||(Y=B.locale)===null||Y===void 0||(Y=Y.options)===null||Y===void 0?void 0:Y.weekStartsOn)!==null&&C!==void 0?C:T.weekStartsOn)!==null&&X!==void 0?X:(I=T.locale)===null||I===void 0||(I=I.options)===null||I===void 0?void 0:I.weekStartsOn)!==null&&U!==void 0?U:0,K=j(H),N=K.getDay(),q0=(N<Q?7:0)+N-Q;return K.setDate(K.getDate()-q0),K.setHours(0,0,0,0),K}function $(H,B,U){var X=P(H,U),C=P(B,U);return+X===+C}var b=function H(B){var U=v[B];switch(B){case 0:case 3:case 6:return"'\u043C\u0438\u043D\u0430\u043B\u0430\u0442\u0430 "+U+" \u0432' p";case 1:case 2:case 4:case 5:return"'\u043C\u0438\u043D\u0430\u043B\u0438\u044F "+U+" \u0432' p"}},O=function H(B){var U=v[B];if(B===2)return"'\u0432\u044A\u0432 "+U+" \u0432' p";else return"'\u0432 "+U+" \u0432' p"},c=function H(B){var U=v[B];switch(B){case 0:case 3:case 6:return"'\u0441\u043B\u0435\u0434\u0432\u0430\u0449\u0430\u0442\u0430 "+U+" \u0432' p";case 1:case 2:case 4:case 5:return"'\u0441\u043B\u0435\u0434\u0432\u0430\u0449\u0438\u044F "+U+" \u0432' p"}},v=["\u043D\u0435\u0434\u0435\u043B\u044F","\u043F\u043E\u043D\u0435\u0434\u0435\u043B\u043D\u0438\u043A","\u0432\u0442\u043E\u0440\u043D\u0438\u043A","\u0441\u0440\u044F\u0434\u0430","\u0447\u0435\u0442\u0432\u044A\u0440\u0442\u044A\u043A","\u043F\u0435\u0442\u044A\u043A","\u0441\u044A\u0431\u043E\u0442\u0430"],f=function H(B,U,X){var C=j(B),E=C.getDay();if($(C,U,X))return O(E);else return b(E)},k=function H(B,U,X){var C=j(B),E=C.getDay();if($(C,U,X))return O(E);else return c(E)},y={lastWeek:f,yesterday:"'\u0432\u0447\u0435\u0440\u0430 \u0432' p",today:"'\u0434\u043D\u0435\u0441 \u0432' p",tomorrow:"'\u0443\u0442\u0440\u0435 \u0432' p",nextWeek:k,other:"P"},m=function H(B,U,X,C){var E=y[B];if(typeof E==="function")return E(U,X,C);return E};function M(H){return function(B,U){var X=U!==null&&U!==void 0&&U.context?String(U.context):"standalone",C;if(X==="formatting"&&H.formattingValues){var E=H.defaultFormattingWidth||H.defaultWidth,Y=U!==null&&U!==void 0&&U.width?String(U.width):E;C=H.formattingValues[Y]||H.formattingValues[E]}else{var I=H.defaultWidth,T=U!==null&&U!==void 0&&U.width?String(U.width):H.defaultWidth;C=H.values[T]||H.values[I]}var Q=H.argumentCallback?H.argumentCallback(B):B;return C[Q]}}var g=function H(B){return B==="year"||B==="week"||B==="minute"||B==="second"},l=function H(B){return B==="quarter"},q=function H(B,U,X,C,E){var Y=l(U)?E:g(U)?C:X;return B+"-"+Y},p={narrow:["\u043F\u0440.\u043D.\u0435.","\u043D.\u0435."],abbreviated:["\u043F\u0440\u0435\u0434\u0438 \u043D. \u0435.","\u043D. \u0435."],wide:["\u043F\u0440\u0435\u0434\u0438 \u043D\u043E\u0432\u0430\u0442\u0430 \u0435\u0440\u0430","\u043D\u043E\u0432\u0430\u0442\u0430 \u0435\u0440\u0430"]},d={narrow:["1","2","3","4"],abbreviated:["1-\u0432\u043E \u0442\u0440\u0438\u043C\u0435\u0441.","2-\u0440\u043E \u0442\u0440\u0438\u043C\u0435\u0441.","3-\u0442\u043E \u0442\u0440\u0438\u043C\u0435\u0441.","4-\u0442\u043E \u0442\u0440\u0438\u043C\u0435\u0441."],wide:["1-\u0432\u043E \u0442\u0440\u0438\u043C\u0435\u0441\u0435\u0447\u0438\u0435","2-\u0440\u043E \u0442\u0440\u0438\u043C\u0435\u0441\u0435\u0447\u0438\u0435","3-\u0442\u043E \u0442\u0440\u0438\u043C\u0435\u0441\u0435\u0447\u0438\u0435","4-\u0442\u043E \u0442\u0440\u0438\u043C\u0435\u0441\u0435\u0447\u0438\u0435"]},u={abbreviated:["\u044F\u043D\u0443","\u0444\u0435\u0432","\u043C\u0430\u0440","\u0430\u043F\u0440","\u043C\u0430\u0439","\u044E\u043D\u0438","\u044E\u043B\u0438","\u0430\u0432\u0433","\u0441\u0435\u043F","\u043E\u043A\u0442","\u043D\u043E\u0435","\u0434\u0435\u043A"],wide:["\u044F\u043D\u0443\u0430\u0440\u0438","\u0444\u0435\u0432\u0440\u0443\u0430\u0440\u0438","\u043C\u0430\u0440\u0442","\u0430\u043F\u0440\u0438\u043B","\u043C\u0430\u0439","\u044E\u043D\u0438","\u044E\u043B\u0438","\u0430\u0432\u0433\u0443\u0441\u0442","\u0441\u0435\u043F\u0442\u0435\u043C\u0432\u0440\u0438","\u043E\u043A\u0442\u043E\u043C\u0432\u0440\u0438","\u043D\u043E\u0435\u043C\u0432\u0440\u0438","\u0434\u0435\u043A\u0435\u043C\u0432\u0440\u0438"]},i={narrow:["\u041D","\u041F","\u0412","\u0421","\u0427","\u041F","\u0421"],short:["\u043D\u0434","\u043F\u043D","\u0432\u0442","\u0441\u0440","\u0447\u0442","\u043F\u0442","\u0441\u0431"],abbreviated:["\u043D\u0435\u0434","\u043F\u043E\u043D","\u0432\u0442\u043E","\u0441\u0440\u044F","\u0447\u0435\u0442","\u043F\u0435\u0442","\u0441\u044A\u0431"],wide:["\u043D\u0435\u0434\u0435\u043B\u044F","\u043F\u043E\u043D\u0435\u0434\u0435\u043B\u043D\u0438\u043A","\u0432\u0442\u043E\u0440\u043D\u0438\u043A","\u0441\u0440\u044F\u0434\u0430","\u0447\u0435\u0442\u0432\u044A\u0440\u0442\u044A\u043A","\u043F\u0435\u0442\u044A\u043A","\u0441\u044A\u0431\u043E\u0442\u0430"]},s={wide:{am:"\u043F\u0440\u0435\u0434\u0438 \u043E\u0431\u044F\u0434",pm:"\u0441\u043B\u0435\u0434 \u043E\u0431\u044F\u0434",midnight:"\u0432 \u043F\u043E\u043B\u0443\u043D\u043E\u0449",noon:"\u043D\u0430 \u043E\u0431\u044F\u0434",morning:"\u0441\u0443\u0442\u0440\u0438\u043D\u0442\u0430",afternoon:"\u0441\u043B\u0435\u0434\u043E\u0431\u0435\u0434",evening:"\u0432\u0435\u0447\u0435\u0440\u0442\u0430",night:"\u043F\u0440\u0435\u0437 \u043D\u043E\u0449\u0442\u0430"}},n=function H(B,U){var X=Number(B),C=U===null||U===void 0?void 0:U.unit;if(X===0)return q(0,C,"\u0435\u0432","\u0435\u0432\u0430","\u0435\u0432\u043E");else if(X%1000===0)return q(X,C,"\u0435\u043D","\u043D\u0430","\u043D\u043E");else if(X%100===0)return q(X,C,"\u0442\u0435\u043D","\u0442\u043D\u0430","\u0442\u043D\u043E");var E=X%100;if(E>20||E<10)switch(E%10){case 1:return q(X,C,"\u0432\u0438","\u0432\u0430","\u0432\u043E");case 2:return q(X,C,"\u0440\u0438","\u0440\u0430","\u0440\u043E");case 7:case 8:return q(X,C,"\u043C\u0438","\u043C\u0430","\u043C\u043E")}return q(X,C,"\u0442\u0438","\u0442\u0430","\u0442\u043E")},r={ordinalNumber:n,era:M({values:p,defaultWidth:"wide"}),quarter:M({values:d,defaultWidth:"wide",argumentCallback:function H(B){return B-1}}),month:M({values:u,defaultWidth:"wide"}),day:M({values:i,defaultWidth:"wide"}),dayPeriod:M({values:s,defaultWidth:"wide"})};function R(H){return function(B){var U=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},X=U.width,C=X&&H.matchPatterns[X]||H.matchPatterns[H.defaultMatchWidth],E=B.match(C);if(!E)return null;var Y=E[0],I=X&&H.parsePatterns[X]||H.parsePatterns[H.defaultParseWidth],T=Array.isArray(I)?a(I,function(N){return N.test(Y)}):o(I,function(N){return N.test(Y)}),Q;Q=H.valueCallback?H.valueCallback(T):T,Q=U.valueCallback?U.valueCallback(Q):Q;var K=B.slice(Y.length);return{value:Q,rest:K}}}var o=function H(B,U){for(var X in B)if(Object.prototype.hasOwnProperty.call(B,X)&&U(B[X]))return X;return},a=function H(B,U){for(var X=0;X<B.length;X++)if(U(B[X]))return X;return};function e(H){return function(B){var U=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},X=B.match(H.matchPattern);if(!X)return null;var C=X[0],E=B.match(H.parsePattern);if(!E)return null;var Y=H.valueCallback?H.valueCallback(E[0]):E[0];Y=U.valueCallback?U.valueCallback(Y):Y;var I=B.slice(C.length);return{value:Y,rest:I}}}var t=/^(\d+)(-?[врмт][аи]|-?т?(ен|на)|-?(ев|ева))?/i,B0=/\d+/i,H0={narrow:/^((пр)?н\.?\s?е\.?)/i,abbreviated:/^((пр)?н\.?\s?е\.?)/i,wide:/^(преди новата ера|новата ера|нова ера)/i},U0={any:[/^п/i,/^н/i]},X0={narrow:/^[1234]/i,abbreviated:/^[1234](-?[врт]?o?)? тримес.?/i,wide:/^[1234](-?[врт]?о?)? тримесечие/i},C0={any:[/1/i,/2/i,/3/i,/4/i]},E0={narrow:/^[нпвсч]/i,short:/^(нд|пн|вт|ср|чт|пт|сб)/i,abbreviated:/^(нед|пон|вто|сря|чет|пет|съб)/i,wide:/^(неделя|понеделник|вторник|сряда|четвъртък|петък|събота)/i},G0={narrow:[/^н/i,/^п/i,/^в/i,/^с/i,/^ч/i,/^п/i,/^с/i],any:[/^н[ед]/i,/^п[он]/i,/^вт/i,/^ср/i,/^ч[ет]/i,/^п[ет]/i,/^с[ъб]/i]},J0={abbreviated:/^(яну|фев|мар|апр|май|юни|юли|авг|сеп|окт|ное|дек)/i,wide:/^(януари|февруари|март|април|май|юни|юли|август|септември|октомври|ноември|декември)/i},Y0={any:[/^я/i,/^ф/i,/^мар/i,/^ап/i,/^май/i,/^юн/i,/^юл/i,/^ав/i,/^се/i,/^окт/i,/^но/i,/^де/i]},Z0={any:/^(преди о|след о|в по|на о|през|веч|сут|следо)/i},A0={any:{am:/^преди о/i,pm:/^след о/i,midnight:/^в пол/i,noon:/^на об/i,morning:/^сут/i,afternoon:/^следо/i,evening:/^веч/i,night:/^през н/i}},I0={ordinalNumber:e({matchPattern:t,parsePattern:B0,valueCallback:function H(B){return parseInt(B,10)}}),era:R({matchPatterns:H0,defaultMatchWidth:"wide",parsePatterns:U0,defaultParseWidth:"any"}),quarter:R({matchPatterns:X0,defaultMatchWidth:"wide",parsePatterns:C0,defaultParseWidth:"any",valueCallback:function H(B){return B+1}}),month:R({matchPatterns:J0,defaultMatchWidth:"wide",parsePatterns:Y0,defaultParseWidth:"any"}),day:R({matchPatterns:E0,defaultMatchWidth:"wide",parsePatterns:G0,defaultParseWidth:"any"}),dayPeriod:R({matchPatterns:Z0,defaultMatchWidth:"any",parsePatterns:A0,defaultParseWidth:"any"})},Q0={code:"bg",formatDistance:L,formatLong:h,formatRelative:m,localize:r,match:I0,options:{weekStartsOn:1,firstWeekContainsDate:1}};window.dateFns=V(V({},window.dateFns),{},{locale:V(V({},(J=window.dateFns)===null||J===void 0?void 0:J.locale),{},{bg:Q0})})})();

//# debugId=83DD9B3A3B88A26A64756e2164756e21
