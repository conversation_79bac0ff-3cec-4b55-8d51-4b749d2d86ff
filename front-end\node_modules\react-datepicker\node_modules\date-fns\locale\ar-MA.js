import { formatDistance } from "./ar-MA/_lib/formatDistance.js";
import { formatLong } from "./ar-MA/_lib/formatLong.js";
import { formatRelative } from "./ar-MA/_lib/formatRelative.js";
import { localize } from "./ar-MA/_lib/localize.js";
import { match } from "./ar-MA/_lib/match.js";

/**
 * @category Locales
 * @summary Arabic locale (Moroccan Arabic).
 * @language Moroccan Arabic
 * @iso-639-2 ara
 * <AUTHOR> [@rramiachraf](https://github.com/rramiachraf)
 */
export const arMA = {
  code: "ar-MA",
  formatDistance: formatDistance,
  formatLong: formatLong,
  formatRelative: formatRelative,
  localize: localize,
  match: match,
  options: {
    // Monday is 1
    weekStartsOn: 1,
    firstWeekContainsDate: 1,
  },
};

// Fallback for modularized imports:
export default arMA;
