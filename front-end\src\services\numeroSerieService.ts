// Service pour gérer la génération automatique des numéros de série

interface NumeroSerieData {
  lastNumber: number;
  prefix: string;
  year: number;
}

// Clés pour le localStorage
const STORAGE_KEYS = {
  ANNONCE_LEGALE: 'charikti_annonce_legale_counter',
  DOMICILIATION: 'charikti_domiciliation_counter',
  JURIDIQUE: 'charikti_juridique_counter',
  JOURNAL: 'charikti_journal_counter'
};

/**
 * Génère un numéro de série automatique incrémenté
 * @param type - Type de document (annonce-legale, domiciliation, juridique, journal)
 * @param prefix - Préfixe personnalisé (optionnel)
 * @returns Numéro de série formaté
 */
export const generateNumeroSerie = (
  type: 'annonce-legale' | 'domiciliation' | 'juridique' | 'journal',
  prefix?: string
): string => {
  const currentYear = new Date().getFullYear();
  const storageKey = getStorageKey(type);
  
  // Récupérer les données existantes
  const existingData = getStoredData(storageKey);
  
  // Vérifier si on est dans une nouvelle année
  const shouldResetCounter = existingData.year !== currentYear;
  
  // Calculer le nouveau numéro
  const newNumber = shouldResetCounter ? 1 : existingData.lastNumber + 1;
  
  // Déterminer le préfixe
  const finalPrefix = prefix || getDefaultPrefix(type);
  
  // Sauvegarder les nouvelles données
  const newData: NumeroSerieData = {
    lastNumber: newNumber,
    prefix: finalPrefix,
    year: currentYear
  };
  
  localStorage.setItem(storageKey, JSON.stringify(newData));
  
  // Formater le numéro de série
  return formatNumeroSerie(finalPrefix, currentYear, newNumber);
};

/**
 * Obtient la clé de stockage selon le type
 */
const getStorageKey = (type: string): string => {
  switch (type) {
    case 'annonce-legale':
      return STORAGE_KEYS.ANNONCE_LEGALE;
    case 'domiciliation':
      return STORAGE_KEYS.DOMICILIATION;
    case 'juridique':
      return STORAGE_KEYS.JURIDIQUE;
    case 'journal':
      return STORAGE_KEYS.JOURNAL;
    default:
      return STORAGE_KEYS.ANNONCE_LEGALE;
  }
};

/**
 * Récupère les données stockées ou retourne des valeurs par défaut
 */
const getStoredData = (storageKey: string): NumeroSerieData => {
  try {
    const stored = localStorage.getItem(storageKey);
    if (stored) {
      return JSON.parse(stored);
    }
  } catch (error) {
    console.warn('Erreur lors de la lecture du localStorage:', error);
  }
  
  // Valeurs par défaut
  return {
    lastNumber: 0,
    prefix: 'AL',
    year: new Date().getFullYear()
  };
};

/**
 * Obtient le préfixe par défaut selon le type
 */
const getDefaultPrefix = (type: string): string => {
  switch (type) {
    case 'annonce-legale':
      return 'AL';
    case 'domiciliation':
      return 'DOM';
    case 'juridique':
      return 'JUR';
    case 'journal':
      return 'JRN';
    default:
      return 'AL';
  }
};

/**
 * Formate le numéro de série final
 * Format: PREFIX-YYYY-NNNN (ex: AL-2024-0001)
 */
const formatNumeroSerie = (prefix: string, year: number, number: number): string => {
  const paddedNumber = number.toString().padStart(4, '0');
  return `${prefix}-${year}-${paddedNumber}`;
};

/**
 * Obtient le prochain numéro sans l'incrémenter (pour prévisualisation)
 */
export const getNextNumeroSerie = (
  type: 'annonce-legale' | 'domiciliation' | 'juridique' | 'journal',
  prefix?: string
): string => {
  const currentYear = new Date().getFullYear();
  const storageKey = getStorageKey(type);
  const existingData = getStoredData(storageKey);
  
  const shouldResetCounter = existingData.year !== currentYear;
  const nextNumber = shouldResetCounter ? 1 : existingData.lastNumber + 1;
  const finalPrefix = prefix || getDefaultPrefix(type);
  
  return formatNumeroSerie(finalPrefix, currentYear, nextNumber);
};

/**
 * Réinitialise le compteur pour un type donné
 */
export const resetCounter = (type: 'annonce-legale' | 'domiciliation' | 'juridique' | 'journal'): void => {
  const storageKey = getStorageKey(type);
  localStorage.removeItem(storageKey);
};

/**
 * Obtient les statistiques des numéros générés
 */
export const getStatistics = () => {
  const stats = {
    annonceLegale: getStoredData(STORAGE_KEYS.ANNONCE_LEGALE),
    domiciliation: getStoredData(STORAGE_KEYS.DOMICILIATION),
    juridique: getStoredData(STORAGE_KEYS.JURIDIQUE),
    journal: getStoredData(STORAGE_KEYS.JOURNAL)
  };

  return {
    ...stats,
    total: stats.annonceLegale.lastNumber + stats.domiciliation.lastNumber + stats.juridique.lastNumber + stats.journal.lastNumber
  };
};

/**
 * Valide un numéro de série
 */
export const validateNumeroSerie = (numeroSerie: string): boolean => {
  const pattern = /^[A-Z]{2,4}-\d{4}-\d{4}$/;
  return pattern.test(numeroSerie);
};

/**
 * Parse un numéro de série pour extraire ses composants
 */
export const parseNumeroSerie = (numeroSerie: string) => {
  const parts = numeroSerie.split('-');
  if (parts.length !== 3) {
    throw new Error('Format de numéro de série invalide');
  }
  
  return {
    prefix: parts[0],
    year: parseInt(parts[1]),
    number: parseInt(parts[2])
  };
};
