{"name": "jay-peg", "description": "Performant JPEG decoder", "version": "1.1.1", "type": "module", "license": "MIT", "main": "./dist/index.cjs", "module": "./src/index.js", "source": "./src/index.js", "exports": {"import": "./src/index.js", "require": "./dist/index.cjs"}, "targets": {"module": false}, "keywords": ["binary", "jpeg", "decode"], "files": ["src", "dist", "LICENSE", "README.md"], "scripts": {"test": "vitest", "benchmark": "node ./benchmark", "format": "yarn prettier . --write", "build": "parcel build", "prepublishOnly": "parcel build"}, "dependencies": {"restructure": "^3.0.0"}, "devDependencies": {"benchmark": "^2.1.4", "colors": "^1.4.0", "parcel": "^2.11.0", "prettier": "3.2.4", "vitest": "^1.2.2"}}