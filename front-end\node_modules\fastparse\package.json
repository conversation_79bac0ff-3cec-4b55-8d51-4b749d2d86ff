{"name": "fastparse", "version": "1.1.2", "description": "A very simple and stupid parser, based on a statemachine and regular expressions.", "main": "lib/Parser.js", "scripts": {"pretest": "npm run lint", "test": "mocha", "travis": "npm run cover -- --report lcovonly", "lint": "eslint lib", "precover": "npm run lint", "cover": "istanbul cover node_modules/mocha/bin/_mocha", "publish-patch": "mocha && npm version patch && git push && git push --tags && npm publish"}, "repository": {"type": "git", "url": "https://github.com/webpack/fastparse.git"}, "keywords": ["parser", "regexp"], "files": ["lib"], "author": "<PERSON> @sokra", "license": "MIT", "bugs": {"url": "https://github.com/webpack/fastparse/issues"}, "homepage": "https://github.com/webpack/fastparse", "devDependencies": {"coveralls": "^2.11.2", "eslint": "^0.21.2", "istanbul": "^0.3.14", "mocha": "^2.2.5", "should": "^6.0.3"}}