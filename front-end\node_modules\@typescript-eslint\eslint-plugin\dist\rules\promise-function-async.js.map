{"version": 3, "file": "promise-function-async.js", "sourceRoot": "", "sources": ["../../src/rules/promise-function-async.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AACA,oDAA2E;AAC3E,+CAAiC;AAEjC,kCAQiB;AAcjB,kBAAe,IAAA,iBAAU,EAAsB;IAC7C,IAAI,EAAE,wBAAwB;IAC9B,IAAI,EAAE;QACJ,IAAI,EAAE,YAAY;QAClB,OAAO,EAAE,MAAM;QACf,IAAI,EAAE;YACJ,WAAW,EACT,0EAA0E;YAC5E,oBAAoB,EAAE,IAAI;SAC3B;QACD,QAAQ,EAAE;YACR,YAAY,EAAE,+CAA+C;SAC9D;QACD,MAAM,EAAE;YACN;gBACE,IAAI,EAAE,QAAQ;gBACd,UAAU,EAAE;oBACV,QAAQ,EAAE;wBACR,WAAW,EACT,yDAAyD;wBAC3D,IAAI,EAAE,SAAS;qBAChB;oBACD,mBAAmB,EAAE;wBACnB,WAAW,EACT,qEAAqE;wBACvE,IAAI,EAAE,OAAO;wBACb,KAAK,EAAE;4BACL,IAAI,EAAE,QAAQ;yBACf;qBACF;oBACD,mBAAmB,EAAE;wBACnB,WAAW,EAAE,mCAAmC;wBAChD,IAAI,EAAE,SAAS;qBAChB;oBACD,yBAAyB,EAAE;wBACzB,WAAW,EAAE,oDAAoD;wBACjE,IAAI,EAAE,SAAS;qBAChB;oBACD,wBAAwB,EAAE;wBACxB,WAAW,EAAE,8CAA8C;wBAC3D,IAAI,EAAE,SAAS;qBAChB;oBACD,uBAAuB,EAAE;wBACvB,WAAW,EACT,0DAA0D;wBAC5D,IAAI,EAAE,SAAS;qBAChB;iBACF;gBACD,oBAAoB,EAAE,KAAK;aAC5B;SACF;KACF;IACD,cAAc,EAAE;QACd;YACE,QAAQ,EAAE,IAAI;YACd,mBAAmB,EAAE,EAAE;YACvB,mBAAmB,EAAE,IAAI;YACzB,yBAAyB,EAAE,IAAI;YAC/B,wBAAwB,EAAE,IAAI;YAC9B,uBAAuB,EAAE,IAAI;SAC9B;KACF;IACD,MAAM,CACJ,OAAO,EACP,CACE,EACE,QAAQ,EACR,mBAAmB,EACnB,mBAAmB,EACnB,yBAAyB,EACzB,wBAAwB,EACxB,uBAAuB,GACxB,EACF;QAED,MAAM,sBAAsB,GAAG,IAAI,GAAG,CAAC;YACrC,SAAS;YACT,qEAAqE;YACrE,oEAAoE;YACpE,GAAG,mBAAoB;SACxB,CAAC,CAAC;QACH,MAAM,QAAQ,GAAG,IAAA,wBAAiB,EAAC,OAAO,CAAC,CAAC;QAC5C,MAAM,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC;QAElD,SAAS,YAAY,CACnB,IAG+B;YAE/B,MAAM,UAAU,GAAG,QAAQ,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC,iBAAiB,EAAE,CAAC;YACxE,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC;gBACvB,OAAO;YACT,CAAC;YACD,MAAM,UAAU,GAAG,OAAO,CAAC,wBAAwB,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;YAEnE,IACE,CAAC,IAAA,6BAAsB,EACrB,UAAU;YACV,oEAAoE;YACpE,QAAS,EACT,sBAAsB;YACtB,qIAAqI;YACrI,IAAI,CAAC,UAAU,IAAI,IAAI,CACxB,EACD,CAAC;gBACD,+BAA+B;gBAC/B,OAAO;YACT,CAAC;YAED,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,0BAA0B,EAAE,CAAC;gBACnE,iCAAiC;gBACjC,OAAO;YACT,CAAC;YAED,IACE,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,QAAQ;gBAC3C,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,gBAAgB,CAAC;gBACvD,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,KAAK,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,KAAK,CAAC,EAC1D,CAAC;gBACD,qCAAqC;gBACrC,OAAO;YACT,CAAC;YAED,IAAI,IAAA,oBAAa,EAAC,UAAU,EAAE,EAAE,CAAC,SAAS,CAAC,GAAG,GAAG,EAAE,CAAC,SAAS,CAAC,OAAO,CAAC,EAAE,CAAC;gBACvE,+DAA+D;gBAC/D,OAAO,OAAO,CAAC,MAAM,CAAC;oBACpB,SAAS,EAAE,cAAc;oBACzB,IAAI;oBACJ,GAAG,EAAE,IAAA,yBAAkB,EAAC,IAAI,EAAE,OAAO,CAAC,UAAU,CAAC;iBAClD,CAAC,CAAC;YACL,CAAC;YAED,OAAO,CAAC,MAAM,CAAC;gBACb,SAAS,EAAE,cAAc;gBACzB,IAAI;gBACJ,GAAG,EAAE,IAAA,yBAAkB,EAAC,IAAI,EAAE,OAAO,CAAC,UAAU,CAAC;gBACjD,GAAG,EAAE,KAAK,CAAC,EAAE;oBACX,IACE,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,gBAAgB;wBACpD,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,QAAQ,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,EACpE,CAAC;wBACD,wEAAwE;wBACxE,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;wBAE3B,kCAAkC;wBAClC,IAAI,QAAQ,GAAG,IAAA,iBAAU,EACvB,OAAO,CAAC,UAAU,CAAC,aAAa,CAAC,MAAM,CAAC,EACxC,wBAAiB,CAAC,YAAY,CAAC,WAAW,EAAE,QAAQ,CAAC,CACtD,CAAC;wBAEF,8CAA8C;wBAC9C,IACE,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,gBAAgB;4BAC/C,MAAM,CAAC,UAAU,CAAC,MAAM,EACxB,CAAC;4BACD,MAAM,aAAa,GACjB,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;4BAClD,QAAQ,GAAG,IAAA,iBAAU,EACnB,OAAO,CAAC,UAAU,CAAC,aAAa,CAAC,aAAa,CAAC,EAC/C,wBAAiB,CAAC,YAAY,CAAC,WAAW,EAAE,gBAAgB,CAAC,CAC9D,CAAC;wBACJ,CAAC;wBAED,uEAAuE;wBACvE,OACE,QAAQ,CAAC,IAAI,KAAK,uBAAe,CAAC,OAAO;4BACzC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,EACvC,CAAC;4BACD,QAAQ,GAAG,IAAA,iBAAU,EACnB,OAAO,CAAC,UAAU,CAAC,aAAa,CAAC,QAAQ,CAAC,EAC1C,wBAAiB,CAAC,YAAY,CAAC,OAAO,EAAE,SAAS,CAAC,CACnD,CAAC;wBACJ,CAAC;wBAED,2DAA2D;wBAC3D,MAAM,WAAW,GAAG,CAAC,OAAO,CAAC,UAAU,CAAC,cAAc,CACpD,IAAA,iBAAU,EACR,OAAO,CAAC,UAAU,CAAC,cAAc,CAAC,QAAQ,CAAC,EAC3C,wBAAiB,CAAC,YAAY,CAAC,OAAO,EAAE,SAAS,CAAC,CACnD,EACD,QAAQ,CACT,CAAC;wBAEF,IAAI,IAAI,GAAG,QAAQ,CAAC;wBACpB,IAAI,WAAW,EAAE,CAAC;4BAChB,IAAI,GAAG,IAAI,IAAI,EAAE,CAAC;wBACpB,CAAC;wBACD,OAAO,KAAK,CAAC,gBAAgB,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;oBAChD,CAAC;oBAED,OAAO,KAAK,CAAC,gBAAgB,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;gBAChD,CAAC;aACF,CAAC,CAAC;QACL,CAAC;QAED,OAAO;YACL,GAAG,CAAC,mBAAmB,IAAI;gBACzB,wCAAwC,CACtC,IAAsC;oBAEtC,YAAY,CAAC,IAAI,CAAC,CAAC;gBACrB,CAAC;aACF,CAAC;YACF,GAAG,CAAC,yBAAyB,IAAI;gBAC/B,oCAAoC,CAClC,IAAkC;oBAElC,YAAY,CAAC,IAAI,CAAC,CAAC;gBACrB,CAAC;aACF,CAAC;YACF,mCAAmC,CACjC,IAAiC;gBAEjC,IACE,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,gBAAgB;oBACpD,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,QAAQ,EAC7B,CAAC;oBACD,IAAI,uBAAuB,EAAE,CAAC;wBAC5B,YAAY,CAAC,IAAI,CAAC,CAAC;oBACrB,CAAC;oBACD,OAAO;gBACT,CAAC;gBACD,IAAI,wBAAwB,EAAE,CAAC;oBAC7B,YAAY,CAAC,IAAI,CAAC,CAAC;gBACrB,CAAC;YACH,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC,CAAC"}