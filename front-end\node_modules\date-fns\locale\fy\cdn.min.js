var q=function(J){return q=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(H){return typeof H}:function(H){return H&&typeof Symbol=="function"&&H.constructor===Symbol&&H!==Symbol.prototype?"symbol":typeof H},q(J)},D=function(J,H){var X=Object.keys(J);if(Object.getOwnPropertySymbols){var I=Object.getOwnPropertySymbols(J);H&&(I=I.filter(function(M){return Object.getOwnPropertyDescriptor(J,M).enumerable})),X.push.apply(X,I)}return X},K=function(J){for(var H=1;H<arguments.length;H++){var X=arguments[H]!=null?arguments[H]:{};H%2?D(Object(X),!0).forEach(function(I){t(J,I,X[I])}):Object.getOwnPropertyDescriptors?Object.defineProperties(J,Object.getOwnPropertyDescriptors(X)):D(Object(X)).forEach(function(I){Object.defineProperty(J,I,Object.getOwnPropertyDescriptor(X,I))})}return J},t=function(J,H,X){if(H=CC(H),H in J)Object.defineProperty(J,H,{value:X,enumerable:!0,configurable:!0,writable:!0});else J[H]=X;return J},CC=function(J){var H=UC(J,"string");return q(H)=="symbol"?H:String(H)},UC=function(J,H){if(q(J)!="object"||!J)return J;var X=J[Symbol.toPrimitive];if(X!==void 0){var I=X.call(J,H||"default");if(q(I)!="object")return I;throw new TypeError("@@toPrimitive must return a primitive value.")}return(H==="string"?String:Number)(J)};(function(J){var H=Object.defineProperty,X=function C(B,U){for(var G in U)H(B,G,{get:U[G],enumerable:!0,configurable:!0,set:function Y(Z){return U[G]=function(){return Z}}})},I={lessThanXSeconds:{one:"minder as 1 sekonde",other:"minder as {{count}} sekonden"},xSeconds:{one:"1 sekonde",other:"{{count}} sekonden"},halfAMinute:"oardel min\xFAt",lessThanXMinutes:{one:"minder as 1 min\xFAt",other:"minder as {{count}} minuten"},xMinutes:{one:"1 min\xFAt",other:"{{count}} minuten"},aboutXHours:{one:"sawat 1 oere",other:"sawat {{count}} oere"},xHours:{one:"1 oere",other:"{{count}} oere"},xDays:{one:"1 dei",other:"{{count}} dagen"},aboutXWeeks:{one:"sawat 1 wike",other:"sawat {{count}} wiken"},xWeeks:{one:"1 wike",other:"{{count}} wiken"},aboutXMonths:{one:"sawat 1 moanne",other:"sawat {{count}} moannen"},xMonths:{one:"1 moanne",other:"{{count}} moannen"},aboutXYears:{one:"sawat 1 jier",other:"sawat {{count}} jier"},xYears:{one:"1 jier",other:"{{count}} jier"},overXYears:{one:"mear as 1 jier",other:"mear as {{count}}s jier"},almostXYears:{one:"hast 1 jier",other:"hast {{count}} jier"}},M=function C(B,U,G){var Y,Z=I[B];if(typeof Z==="string")Y=Z;else if(U===1)Y=Z.one;else Y=Z.other.replace("{{count}}",String(U));if(G!==null&&G!==void 0&&G.addSuffix)if(G.comparison&&G.comparison>0)return"oer "+Y;else return Y+" lyn";return Y};function N(C){return function(){var B=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},U=B.width?String(B.width):C.defaultWidth,G=C.formats[U]||C.formats[C.defaultWidth];return G}}var W={full:"EEEE d MMMM y",long:"d MMMM y",medium:"d MMM y",short:"dd-MM-y"},$={full:"HH:mm:ss zzzz",long:"HH:mm:ss z",medium:"HH:mm:ss",short:"HH:mm"},S={full:"{{date}} 'om' {{time}}",long:"{{date}} 'om' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},R={date:N({formats:W,defaultWidth:"full"}),time:N({formats:$,defaultWidth:"full"}),dateTime:N({formats:S,defaultWidth:"full"})},L={lastWeek:"'\xF4fr\xFBne' eeee 'om' p",yesterday:"'juster om' p",today:"'hjoed om' p",tomorrow:"'moarn om' p",nextWeek:"eeee 'om' p",other:"P"},V=function C(B,U,G,Y){return L[B]};function O(C){return function(B,U){var G=U!==null&&U!==void 0&&U.context?String(U.context):"standalone",Y;if(G==="formatting"&&C.formattingValues){var Z=C.defaultFormattingWidth||C.defaultWidth,T=U!==null&&U!==void 0&&U.width?String(U.width):Z;Y=C.formattingValues[T]||C.formattingValues[Z]}else{var A=C.defaultWidth,x=U!==null&&U!==void 0&&U.width?String(U.width):C.defaultWidth;Y=C.values[x]||C.values[A]}var E=C.argumentCallback?C.argumentCallback(B):B;return Y[E]}}var j={narrow:["f.K.","n.K."],abbreviated:["f.Kr.","n.Kr."],wide:["foar Kristus","nei Kristus"]},f={narrow:["1","2","3","4"],abbreviated:["K1","K2","K3","K4"],wide:["1e fearnsjier","2e fearnsjier","3e fearnsjier","4e fearnsjier"]},P={narrow:["j","f","m","a","m","j","j","a","s","o","n","d"],abbreviated:["jan.","feb.","mrt.","apr.","mai.","jun.","jul.","aug.","sep.","okt.","nov.","des."],wide:["jannewaris","febrewaris","maart","april","maaie","juny","july","augustus","septimber","oktober","novimber","desimber"]},w={narrow:["s","m","t","w","t","f","s"],short:["si","mo","ti","wo","to","fr","so"],abbreviated:["snein","moa","tii","woa","ton","fre","sneon"],wide:["snein","moandei","tiisdei","woansdei","tongersdei","freed","sneon"]},_={narrow:{am:"AM",pm:"PM",midnight:"middernacht",noon:"middei",morning:"moarns",afternoon:"middeis",evening:"j\xFBns",night:"nachts"},abbreviated:{am:"AM",pm:"PM",midnight:"middernacht",noon:"middei",morning:"moarns",afternoon:"middeis",evening:"j\xFBns",night:"nachts"},wide:{am:"AM",pm:"PM",midnight:"middernacht",noon:"middei",morning:"moarns",afternoon:"middeis",evening:"j\xFBns",night:"nachts"}},v=function C(B,U){var G=Number(B);return G+"e"},F={ordinalNumber:v,era:O({values:j,defaultWidth:"wide"}),quarter:O({values:f,defaultWidth:"wide",argumentCallback:function C(B){return B-1}}),month:O({values:P,defaultWidth:"wide"}),day:O({values:w,defaultWidth:"wide"}),dayPeriod:O({values:_,defaultWidth:"wide"})};function Q(C){return function(B){var U=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},G=U.width,Y=G&&C.matchPatterns[G]||C.matchPatterns[C.defaultMatchWidth],Z=B.match(Y);if(!Z)return null;var T=Z[0],A=G&&C.parsePatterns[G]||C.parsePatterns[C.defaultParseWidth],x=Array.isArray(A)?k(A,function(z){return z.test(T)}):b(A,function(z){return z.test(T)}),E;E=C.valueCallback?C.valueCallback(x):x,E=U.valueCallback?U.valueCallback(E):E;var a=B.slice(T.length);return{value:E,rest:a}}}var b=function C(B,U){for(var G in B)if(Object.prototype.hasOwnProperty.call(B,G)&&U(B[G]))return G;return},k=function C(B,U){for(var G=0;G<B.length;G++)if(U(B[G]))return G;return};function m(C){return function(B){var U=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},G=B.match(C.matchPattern);if(!G)return null;var Y=G[0],Z=B.match(C.parsePattern);if(!Z)return null;var T=C.valueCallback?C.valueCallback(Z[0]):Z[0];T=U.valueCallback?U.valueCallback(T):T;var A=B.slice(Y.length);return{value:T,rest:A}}}var h=/^(\d+)e?/i,c=/\d+/i,y={narrow:/^([fn]\.? ?K\.?)/,abbreviated:/^([fn]\. ?Kr\.?)/,wide:/^((foar|nei) Kristus)/},p={any:[/^f/,/^n/]},d={narrow:/^[1234]/i,abbreviated:/^K[1234]/i,wide:/^[1234]e fearnsjier/i},u={any:[/1/i,/2/i,/3/i,/4/i]},g={narrow:/^[jfmasond]/i,abbreviated:/^(jan.|feb.|mrt.|apr.|mai.|jun.|jul.|aug.|sep.|okt.|nov.|des.)/i,wide:/^(jannewaris|febrewaris|maart|april|maaie|juny|july|augustus|septimber|oktober|novimber|desimber)/i},l={narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^jan/i,/^feb/i,/^m(r|a)/i,/^apr/i,/^mai/i,/^jun/i,/^jul/i,/^aug/i,/^sep/i,/^okt/i,/^nov/i,/^des/i]},i={narrow:/^[smtwf]/i,short:/^(si|mo|ti|wo|to|fr|so)/i,abbreviated:/^(snein|moa|tii|woa|ton|fre|sneon)/i,wide:/^(snein|moandei|tiisdei|woansdei|tongersdei|freed|sneon)/i},n={narrow:[/^s/i,/^m/i,/^t/i,/^w/i,/^t/i,/^f/i,/^s/i],any:[/^sn/i,/^mo/i,/^ti/i,/^wo/i,/^to/i,/^fr/i,/^sn/i]},s={any:/^(am|pm|middernacht|middeis|moarns|middei|jûns|nachts)/i},o={any:{am:/^am/i,pm:/^pm/i,midnight:/^middernacht/i,noon:/^middei/i,morning:/moarns/i,afternoon:/^middeis/i,evening:/jûns/i,night:/nachts/i}},r={ordinalNumber:m({matchPattern:h,parsePattern:c,valueCallback:function C(B){return parseInt(B,10)}}),era:Q({matchPatterns:y,defaultMatchWidth:"wide",parsePatterns:p,defaultParseWidth:"any"}),quarter:Q({matchPatterns:d,defaultMatchWidth:"wide",parsePatterns:u,defaultParseWidth:"any",valueCallback:function C(B){return B+1}}),month:Q({matchPatterns:g,defaultMatchWidth:"wide",parsePatterns:l,defaultParseWidth:"any"}),day:Q({matchPatterns:i,defaultMatchWidth:"wide",parsePatterns:n,defaultParseWidth:"any"}),dayPeriod:Q({matchPatterns:s,defaultMatchWidth:"any",parsePatterns:o,defaultParseWidth:"any"})},e={code:"fy",formatDistance:M,formatLong:R,formatRelative:V,localize:F,match:r,options:{weekStartsOn:1,firstWeekContainsDate:4}};window.dateFns=K(K({},window.dateFns),{},{locale:K(K({},(J=window.dateFns)===null||J===void 0?void 0:J.locale),{},{fy:e})})})();

//# debugId=2A7DA2C42F2436EE64756e2164756e21
