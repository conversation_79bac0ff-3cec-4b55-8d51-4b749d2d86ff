.alert {
    display: grid;
    width: 100%;
    grid-auto-flow: row;
    align-content: flex-start;
    align-items: center;
    justify-items: center;
    gap: 1rem;
    text-align: center
}
@media (min-width: 640px) {
    .alert {
        grid-auto-flow: column;
        grid-template-columns: auto minmax(auto,1fr);
        justify-items: start;
        text-align: start
    }
}
.artboard {
    width: 100%
}
.avatar {
    position: relative;
    display: inline-flex
}
  .avatar > div {
    display: block;
    aspect-ratio: 1 / 1;
    overflow: hidden
}
  .avatar img {
    height: 100%;
    width: 100%;
    object-fit: cover
}
  .avatar.placeholder > div {
    display: flex;
    align-items: center;
    justify-content: center
}
.badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 200ms;
  transition-timing-function: cubic-bezier(0, 0, 0.2, 1);
  height: 1.25rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
  width: fit-content;
  padding-left: 0.563rem;
  padding-right: 0.563rem
}
.btm-nav {
  position: fixed;
  bottom: 0px;
  left: 0px;
  right: 0px;
  display: flex;
  width: 100%;
  flex-direction: row;
  align-items: center;
  justify-content: space-around;
  padding-bottom: env(safe-area-inset-bottom)
}
  .btm-nav > * {
  position: relative;
  display: flex;
  height: 100%;
  flex-basis: 100%;
  cursor: pointer;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 0.25rem
}
.breadcrumbs {
    max-width: 100%;
    overflow-x: auto
}
  .breadcrumbs > ul,
  .breadcrumbs > ol {
    display: flex;
    align-items: center;
    white-space: nowrap;
    min-height: min-content
}
  .breadcrumbs > ul > li, .breadcrumbs > ol > li {
    display: flex;
    align-items: center
}
  .breadcrumbs > ul > li > a, .breadcrumbs > ol > li > a {
    display: flex;
    cursor: pointer;
    align-items: center
}
  @media(hover:hover) {
    .breadcrumbs > ul > li > a:hover, .breadcrumbs > ol > li > a:hover {
        text-decoration-line: underline
    }
}
.btn {
  display: inline-flex;
  height: 3rem;
  min-height: 3rem;
  flex-shrink: 0;
  cursor: pointer;
  user-select: none;
  flex-wrap: wrap;
  align-items: center;
  justify-content: center;
  border-radius: var(--rounded-btn, 0.5rem);
  border-color: transparent;
  padding-left: 1rem;
  padding-right: 1rem;
  text-align: center;
  font-size: 0.875rem;
  line-height: 1em
}
  /* disabled */
  .btn-disabled,
  .btn[disabled],
  .btn:disabled {
  pointer-events: none
}
  /* shapes */
  .btn-square {
  height: 3rem;
  width: 3rem;
  padding: 0px
}
  .btn-circle {
  height: 3rem;
  width: 3rem;
  border-radius: 9999px;
  padding: 0px
}

/* radio input and checkbox as button */

:where(.btn:is(input[type="checkbox"])),
:where(.btn:is(input[type="radio"])) {
  width: auto;
  appearance: none
}

.btn:is(input[type="checkbox"]):after,
.btn:is(input[type="radio"]):after {
  --tw-content: attr(aria-label);
  content: var(--tw-content)
}
.card {
      position: relative;
      display: flex;
      flex-direction: column
}
  .card:focus {
      outline: 2px solid transparent;
      outline-offset: 2px
}
  .card-body {
      display: flex;
      flex: 1 1 auto;
      flex-direction: column
}
  .card-body :where(p) {
      flex-grow: 1
}
  .card-actions {
      display: flex;
      flex-wrap: wrap;
      align-items: flex-start;
      gap: 0.5rem
}
  .card figure {
      display: flex;
      align-items: center;
      justify-content: center
}
  .card.image-full {
      display: grid
}
  .card.image-full:before {
      position: relative;
      content: ""
}
  .card.image-full:before,
    .card.image-full > * {
      grid-column-start: 1;
      grid-row-start: 1
}
  .card.image-full > figure img {
      height: 100%;
      object-fit: cover
}
  .card.image-full > .card-body {
      position: relative
}
.carousel {
  display: inline-flex;
  overflow-x: scroll;
  scroll-snap-type: x mandatory;
  scroll-behavior: smooth;
}
  .carousel-vertical {
  flex-direction: column;
  overflow-y: scroll;
    scroll-snap-type: y mandatory;
}
  .carousel-item {
  box-sizing: content-box;
  display: flex;
  flex: none;
    scroll-snap-align: start;
}
  .carousel-start .carousel-item {
    scroll-snap-align: start;
  }
  .carousel-center .carousel-item {
    scroll-snap-align: center;
  }
  .carousel-end .carousel-item {
    scroll-snap-align: end;
  }
.chat {
    display: grid;
    grid-template-columns: repeat(2, minmax(0, 1fr));
    column-gap: 0.75rem;
    padding-top: 0.25rem;
    padding-bottom: 0.25rem;
}
  .chat-image {
    grid-row: span 2 / span 2;
    align-self: flex-end;
}
  .chat-header {
    grid-row-start: 1;
    font-size: 0.875rem;
    line-height: 1.25rem;
}
  .chat-footer {
    grid-row-start: 3;
    font-size: 0.875rem;
    line-height: 1.25rem;
}
  .chat-bubble {
    position: relative;
    display: block;
    width: fit-content;
    padding-left: 1rem;
    padding-right: 1rem;
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
    max-width: 90%;
}
  .chat-bubble:before {
    position: absolute;
    bottom: 0px;
    height: 0.75rem;
    width: 0.75rem;
      background-color: inherit;
      content: "";
      mask-size: contain;
      mask-repeat: no-repeat;
      mask-position: center;
}
  .chat-start {
    place-items: start;
    grid-template-columns: auto 1fr;
}
  .chat-start .chat-header {
    grid-column-start: 2;
}
  .chat-start .chat-footer {
    grid-column-start: 2;
}
  .chat-start .chat-image {
    grid-column-start: 1;
}
  .chat-start .chat-bubble {
    grid-column-start: 2;
}
  .chat-start .chat-bubble:before {
        mask-image: url("data:image/svg+xml,%3csvg width='3' height='3' xmlns='http://www.w3.org/2000/svg'%3e%3cpath fill='black' d='m 0 3 L 3 3 L 3 0 C 3 1 1 3 0 3'/%3e%3c/svg%3e");
      }
  [dir="rtl"] .chat-start .chat-bubble:before {
          mask-image: url("data:image/svg+xml,%3csvg width='3' height='3' xmlns='http://www.w3.org/2000/svg'%3e%3cpath fill='black' d='m 0 3 L 1 3 L 3 3 C 2 3 0 1 0 0'/%3e%3c/svg%3e");
        }
  .chat-end {
    place-items: end;
    grid-template-columns: 1fr auto;
}
  .chat-end .chat-header {
    grid-column-start: 1;
}
  .chat-end .chat-footer {
    grid-column-start: 1;
}
  .chat-end .chat-image {
    grid-column-start: 2;
}
  .chat-end .chat-bubble {
    grid-column-start: 1;
}
  .chat-end .chat-bubble:before {
        mask-image: url("data:image/svg+xml,%3csvg width='3' height='3' xmlns='http://www.w3.org/2000/svg'%3e%3cpath fill='black' d='m 0 3 L 1 3 L 3 3 C 2 3 0 1 0 0'/%3e%3c/svg%3e");
      }
  [dir="rtl"] .chat-end .chat-bubble:before {
          mask-image: url("data:image/svg+xml,%3csvg width='3' height='3' xmlns='http://www.w3.org/2000/svg'%3e%3cpath fill='black' d='m 0 3 L 3 3 L 3 0 C 3 1 1 3 0 3'/%3e%3c/svg%3e");
        }
.checkbox {
    flex-shrink: 0
}
.collapse:not(td):not(tr):not(colgroup) {
  visibility: visible;
}
.collapse {
  position: relative;
  display: grid;
  overflow: hidden;
  grid-template-rows: auto 0fr;
  transition: grid-template-rows 0.2s;
}
.collapse-title,
.collapse > input[type="checkbox"],
.collapse > input[type="radio"],
.collapse-content {
  grid-column-start: 1;
  grid-row-start: 1;
}
.collapse > input[type="checkbox"],
.collapse > input[type="radio"] {
  appearance: none;
  opacity: 0;
}
.collapse-content {
  visibility: hidden;
  grid-column-start: 1;
  grid-row-start: 2;
  min-height: 0px;
  transition: visibility 0.2s;
}
.collapse[open],
.collapse-open,
.collapse:focus:not(.collapse-close) {
  grid-template-rows: auto 1fr;
}
.collapse:not(.collapse-close):has(> input[type="checkbox"]:checked),
.collapse:not(.collapse-close):has(> input[type="radio"]:checked) {
  grid-template-rows: auto 1fr;
}
.collapse[open] > .collapse-content,
.collapse-open > .collapse-content,
.collapse:focus:not(.collapse-close) > .collapse-content,
.collapse:not(.collapse-close) > input[type="checkbox"]:checked ~ .collapse-content,
.collapse:not(.collapse-close) > input[type="radio"]:checked ~ .collapse-content {
  visibility: visible;
  min-height: fit-content;
}
:root .countdown {
  line-height: 1em;
}
.countdown {
  display: inline-flex;
}
.countdown > * {
    height: 1em;
    display: inline-block;
    overflow-y: hidden;
  }
.countdown > *:before {
      position: relative;
      content: "00\A 01\A 02\A 03\A 04\A 05\A 06\A 07\A 08\A 09\A 10\A 11\A 12\A 13\A 14\A 15\A 16\A 17\A 18\A 19\A 20\A 21\A 22\A 23\A 24\A 25\A 26\A 27\A 28\A 29\A 30\A 31\A 32\A 33\A 34\A 35\A 36\A 37\A 38\A 39\A 40\A 41\A 42\A 43\A 44\A 45\A 46\A 47\A 48\A 49\A 50\A 51\A 52\A 53\A 54\A 55\A 56\A 57\A 58\A 59\A 60\A 61\A 62\A 63\A 64\A 65\A 66\A 67\A 68\A 69\A 70\A 71\A 72\A 73\A 74\A 75\A 76\A 77\A 78\A 79\A 80\A 81\A 82\A 83\A 84\A 85\A 86\A 87\A 88\A 89\A 90\A 91\A 92\A 93\A 94\A 95\A 96\A 97\A 98\A 99\A";
      white-space: pre;
      top: calc(var(--value) * -1em);
    }
.diff {
  position: relative;
  display: grid;
  width: 100%;
  overflow: hidden;
  container-type: inline-size;
  grid-template-columns: auto 1fr
}
.diff-resizer {
  position: relative;
  top: 50%;
  z-index: 1;
  height: 3rem;
  width: 25rem;
  min-width: 1rem;
  max-width: calc(100cqi - 1rem);
  resize: horizontal;
  overflow: hidden;
  opacity: 0;
  transform-origin: 100% 100%;
  scale: 4;
  translate: 1.5rem -1.5rem;
  clip-path: inset(calc(100% - 0.75rem) 0 0 calc(100% - 0.75rem))
}
.diff-resizer,
.diff-item-1,
.diff-item-2 {
  position: relative;
  grid-column-start: 1;
  grid-row-start: 1
}
.diff-item-1:after {
  pointer-events: none;
  position: absolute;
  bottom: 0px;
  right: 1px;
  top: 50%;
  z-index: 1;
  height: 2rem;
  width: 2rem;
  --tw-content: '';
  content: var(--tw-content);
  translate: 50% -50%
}
.diff-item-2 {
  overflow: hidden
}
.diff-item-1 > *,
.diff-item-2 > * {
  pointer-events: none;
  position: absolute;
  bottom: 0px;
  left: 0px;
  top: 0px;
  height: 100%;
  width: 100cqi;
  max-width: none;
  object-fit: cover;
  object-position: center
}
.divider {
    display: flex;
    flex-direction: row;
    align-items: center;
    align-self: stretch
}
  .divider:before,
  .divider:after {
    height: 0.125rem;
    width: 100%;
    flex-grow: 1;
    --tw-content: '';
    content: var(--tw-content)
}
  .divider-start:before {
    display: none
}
  .divider-end:after {
    display: none
}
.drawer {
  position: relative;
  display: grid;
  grid-auto-columns: max-content auto;
}
  .drawer-content {
  grid-column-start: 2;
  grid-row-start: 1;
  min-width: 0px;
}
  .drawer-side {
  pointer-events: none;
  position: fixed;
  inset-inline-start: 0px;
  top: 0px;
  grid-column-start: 1;
  grid-row-start: 1;
  display: grid;
  width: 100%;
  grid-template-columns: repeat(1, minmax(0, 1fr));
  grid-template-rows: repeat(1, minmax(0, 1fr));
  align-items: flex-start;
  justify-items: start;
  overflow-x: hidden;
  overflow-y: hidden;
  overscroll-behavior: contain;
    height: 100vh;
    height: 100dvh;
}
  .drawer-side > .drawer-overlay {
  position: sticky;
  top: 0px;
  place-self: stretch;
}
  .drawer-side > * {
  grid-column-start: 1;
  grid-row-start: 1;
}
  .drawer-side > *:not(.drawer-overlay) {
  transition-property: transform;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
  transition-timing-function: cubic-bezier(0, 0, 0.2, 1);
  will-change: transform;
      transform: translateX(-100%);
}
  [dir="rtl"] .drawer-side > *:not(.drawer-overlay) {
        transform: translateX(100%);
      }
  .drawer-toggle {
  position: fixed;
  height: 0px;
  width: 0px;
  appearance: none;
  opacity: 0;
}
  .drawer-toggle:checked ~ .drawer-side {
  pointer-events: auto;
  visibility: visible;
  overflow-y: auto;
}
  .drawer-toggle:checked ~ .drawer-side > *:not(.drawer-overlay) {
          transform: translateX(0%);
        }
  .drawer-end {
    grid-auto-columns: auto max-content;
  }
  .drawer-end .drawer-toggle ~ .drawer-content {
  grid-column-start: 1;
}
  .drawer-end .drawer-toggle ~ .drawer-side {
  grid-column-start: 2;
  justify-items: end;
}
  .drawer-end .drawer-toggle ~ .drawer-side > *:not(.drawer-overlay) {
        transform: translateX(100%);
      }
  [dir="rtl"] .drawer-end .drawer-toggle ~ .drawer-side > *:not(.drawer-overlay) {
          transform: translateX(-100%);
        }
  .drawer-end .drawer-toggle:checked ~ .drawer-side > *:not(.drawer-overlay) {
        transform: translateX(0%);
      }
.dropdown {
  position: relative;
  display: inline-block
}
.dropdown > *:not(summary):focus {
  outline: 2px solid transparent;
  outline-offset: 2px
}
.dropdown .dropdown-content {
  position: absolute
}
.dropdown:is(:not(details)) .dropdown-content {
  visibility: hidden;
  opacity: 0
}
.dropdown-end .dropdown-content {
  inset-inline-end: 0px
}
.dropdown-left .dropdown-content {
  bottom: auto;
  inset-inline-end: 100%;
  top: 0px
}
.dropdown-right .dropdown-content {
  bottom: auto;
  inset-inline-start: 100%;
  top: 0px
}
.dropdown-bottom .dropdown-content {
  bottom: auto;
  top: 100%
}
.dropdown-top .dropdown-content {
  bottom: 100%;
  top: auto
}
.dropdown-end.dropdown-right .dropdown-content {
  bottom: 0px;
  top: auto
}
.dropdown-end.dropdown-left .dropdown-content {
  bottom: 0px;
  top: auto
}
.dropdown.dropdown-open .dropdown-content,
.dropdown:not(.dropdown-hover):focus .dropdown-content,
.dropdown:focus-within .dropdown-content {
  visibility: visible;
  opacity: 1
}
@media (hover: hover) {
  .dropdown.dropdown-hover:hover .dropdown-content {
    visibility: visible;
    opacity: 1
  }
}
.dropdown:is(details) summary::-webkit-details-marker {
  display: none
}
.file-input {
    height: 3rem;
    flex-shrink: 1;
    padding-inline-end: 1rem;
    font-size: 0.875rem;
    line-height: 1.25rem;
    line-height: 2
}
  .file-input::file-selector-button {
    margin-inline-end: 1rem;
    display: inline-flex;
    height: 100%;
    flex-shrink: 0;
    cursor: pointer;
    user-select: none;
    flex-wrap: wrap;
    align-items: center;
    justify-content: center;
    padding-left: 1rem;
    padding-right: 1rem;
    text-align: center;
    font-size: 0.875rem;
    line-height: 1.25rem;
    transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 200ms;
    transition-timing-function: cubic-bezier(0, 0, 0.2, 1);
    line-height: 1em
}
.footer {
  display: grid;
  width: 100%;
  grid-auto-flow: row;
  place-items: start;
}
  .footer > * {
  display: grid;
  place-items: start;
}
  .footer-center {
  place-items: center;
  text-align: center;
}
  .footer-center > * {
  place-items: center;
}
@media (min-width: 48rem) {
  .footer {
    grid-auto-flow: column;
  }
  .footer-center {
    grid-auto-flow: row dense;
  }
}
.form-control {
    display: flex;
    flex-direction: column
}
.label {
    display: flex;
    user-select: none;
    align-items: center;
    justify-content: space-between
}
.hero {
    display: grid;
    width: 100%;
    place-items: center;
    background-size: cover;
    background-position: center
}
  .hero > * {
    grid-column-start: 1;
    grid-row-start: 1
}
  .hero-overlay {
    grid-column-start: 1;
    grid-row-start: 1;
    height: 100%;
    width: 100%
}
  .hero-content {
    z-index: 0;
    display: flex;
    align-items: center;
    justify-content: center
}
.indicator {
  position: relative;
  display: inline-flex;
  width: max-content;
}
  .indicator :where(.indicator-item) {
    z-index: 1;
    position: absolute;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
    white-space: nowrap;
  }
.input {
    flex-shrink: 1;
    appearance: none;
    height: 3rem;
    padding-left: 1rem;
    padding-right: 1rem;
    font-size: 0.875rem;
    line-height: 1.25rem;
    line-height: 2
}
.input[type="number"]::-webkit-inner-spin-button,
.input-md[type="number"]::-webkit-inner-spin-button {
    margin-top: -1rem;
    margin-bottom: -1rem;
    margin-inline-end: -1rem
}
.input-xs[type="number"]::-webkit-inner-spin-button {
    margin-top: -0.25rem;
    margin-bottom: -0.25rem;
    margin-inline-end: -0px
}
.input-sm[type="number"]::-webkit-inner-spin-button {
    margin-top: 0px;
    margin-bottom: 0px;
    margin-inline-end: -0px
}
.input-lg[type="number"]::-webkit-inner-spin-button {
    margin-top: -1.5rem;
    margin-bottom: -1.5rem;
    margin-inline-end: -1.5rem
}
.join {
    display: inline-flex;
    align-items: stretch;
}
  .join :where(.join-item) {
    border-start-end-radius: 0;
    border-end-end-radius: 0;
    border-end-start-radius: 0;
    border-start-start-radius: 0;
  }
  .join .join-item:not(:first-child):not(:last-child),
  .join *:not(:first-child):not(:last-child) .join-item {
    border-start-end-radius: 0;
    border-end-end-radius: 0;
    border-end-start-radius: 0;
    border-start-start-radius: 0;
  }
  .join .join-item:first-child:not(:last-child),
  .join *:first-child:not(:last-child) .join-item {
    border-start-end-radius: 0;
    border-end-end-radius: 0;
  }
  .join .dropdown .join-item:first-child:not(:last-child),
  .join *:first-child:not(:last-child) .dropdown .join-item {
    border-start-end-radius: inherit;
    border-end-end-radius: inherit;
  }
  .join :where(.join-item:first-child:not(:last-child)),
  .join :where(*:first-child:not(:last-child) .join-item) {
    border-end-start-radius: inherit;
    border-start-start-radius: inherit;
  }
  .join .join-item:last-child:not(:first-child),
  .join *:last-child:not(:first-child) .join-item {
    border-end-start-radius: 0;
    border-start-start-radius: 0;
  }
  .join :where(.join-item:last-child:not(:first-child)),
  .join :where(*:last-child:not(:first-child) .join-item) {
    border-start-end-radius: inherit;
    border-end-end-radius: inherit;
  }

@supports not selector(:has(*)) {
  :where(.join *) {
        border-radius: inherit;
    }
}

@supports selector(:has(*)) {
  :where(.join *:has(.join-item)) {
        border-radius: inherit;
    }
}
.kbd {
    display: inline-flex;
    align-items: center;
    justify-content: center
}
.link {
    cursor: pointer;
    text-decoration-line: underline
}
  .link-hover {
    text-decoration-line: none
}
  @media(hover:hover) {
    .link-hover:hover {
        text-decoration-line: underline
    }
}
.mask {
  mask-size: contain;
  mask-repeat: no-repeat;
  mask-position: center;
}
.mask-half-1 {
  mask-size: 200%;
  mask-position: left;
}
.mask-half-1:where([dir="rtl"], [dir="rtl"] *) {
  mask-position: right;
}
.mask-half-2 {
  mask-size: 200%;
  mask-position: right;
}
.mask-half-2:where([dir="rtl"], [dir="rtl"] *) {
  mask-position: left;
}
.menu {
    display: flex;
    flex-direction: column;
    flex-wrap: wrap;
    font-size: 0.875rem;
    line-height: 1.25rem
}
  .menu :where(li ul) {
    position: relative;
    white-space: nowrap
}
  .menu :where(li:not(.menu-title) > *:not(ul, details, .menu-title, .btn)), .menu :where(li:not(.menu-title) > details > summary:not(.menu-title)) {
    display: grid;
    grid-auto-flow: column;
    align-content: flex-start;
    align-items: center;
    gap: 0.5rem;
    grid-auto-columns: minmax(auto, max-content) auto max-content;
    user-select: none
}
  .menu li.disabled {
    cursor: not-allowed;
    user-select: none
}
  .menu :where(li > .menu-dropdown:not(.menu-dropdown-show)) {
    display: none
}
:where(.menu li) {
    position: relative;
    display: flex;
    flex-shrink: 0;
    flex-direction: column;
    flex-wrap: wrap;
    align-items: stretch
}
:where(.menu li) .badge {
    justify-self: end
}
.mockup-code {
        position: relative;
        overflow: hidden;
        overflow-x: auto;
}
    .mockup-code pre[data-prefix]:before {
        content: attr(data-prefix);
        display: inline-block;
        text-align: right;
      }
  .mockup-window {
        position: relative;
        overflow: hidden;
        overflow-x: auto;
}
  .mockup-window pre[data-prefix]:before {
        content: attr(data-prefix);
        display: inline-block;
        text-align: right;
      }
  .mockup-browser {
        position: relative;
        overflow: hidden;
        overflow-x: auto;
}
  .mockup-browser pre[data-prefix]:before {
        content: attr(data-prefix);
        display: inline-block;
        text-align: right;
      }
.modal {
  /* @apply pointer-events-none invisible fixed inset-0 flex justify-center opacity-0; */
  pointer-events: none;
  position: fixed;
  inset: 0px;
  margin: 0px;
  display: grid;
  height: 100%;
  max-height: none;
  width: 100%;
  max-width: none;
  justify-items: center;
  padding: 0px;
  opacity: 0;
  overscroll-behavior: contain;
  z-index: 999;
}
.modal-scroll {
  overscroll-behavior: auto;
}
:where(.modal) {
  align-items: center;
}
.modal-box {
  max-height: calc(100vh - 5em);
}
.modal-open,
.modal:target,
.modal-toggle:checked + .modal,
.modal[open] {
  pointer-events: auto;
  visibility: visible;
  opacity: 1;
}
.modal-action {
  display: flex;
}
.modal-toggle {
  position: fixed;
  height: 0px;
  width: 0px;
  appearance: none;
  opacity: 0;
}
:root:has(:is(.modal-open, .modal:target, .modal-toggle:checked + .modal, .modal[open])) {
  overflow: hidden;
  scrollbar-gutter: stable;
}
.navbar {
  display: flex;
  align-items: center;
}
:where(.navbar > *:not(script, style)) {
  display: inline-flex;
  align-items: center;
}
.navbar-start {
  width: 50%;
  justify-content: flex-start;
}
.navbar-center {
  flex-shrink: 0;
}
.navbar-end {
  width: 50%;
  justify-content: flex-end;
}
.progress {
    position: relative;
    width: 100%;
    appearance: none;
    overflow: hidden
}
.radial-progress {
  position: relative;
  display: inline-grid;
  height: var(--size);
  width: var(--size);
  place-content: center;
  border-radius: 9999px;
  background-color: transparent;
  vertical-align: middle;
  box-sizing: content-box;
}
.radial-progress::-moz-progress-bar {
  appearance: none;
  background-color: transparent;
}
.radial-progress::-webkit-progress-value {
  appearance: none;
  background-color: transparent;
}
.radial-progress::-webkit-progress-bar {
  appearance: none;
  background-color: transparent;
}
.radial-progress:before,
.radial-progress:after {
  position: absolute;
  border-radius: 9999px;
  content: "";
}
.radial-progress:before {
  inset: 0px;
  background:
    radial-gradient(farthest-side, currentColor 98%, #0000) top/var(--thickness) var(--thickness)
      no-repeat,
    conic-gradient(currentColor calc(var(--value) * 1%), #0000 0);
  -webkit-mask: radial-gradient(
    farthest-side,
    #0000 calc(99% - var(--thickness)),
    #000 calc(100% - var(--thickness))
  );
  mask: radial-gradient(
    farthest-side,
    #0000 calc(99% - var(--thickness)),
    #000 calc(100% - var(--thickness))
  );
}
.radial-progress:after {
  inset: calc(50% - var(--thickness) / 2);
  transform: rotate(calc(var(--value) * 3.6deg - 90deg)) translate(calc(var(--size) / 2 - 50%));
}
.radio {
    flex-shrink: 0
}
.range {
    height: 1.5rem;
    width: 100%;
    cursor: pointer;
}
  .range:focus {
    outline: none;
  }
.rating {
    position: relative;
    display: inline-flex
}
  .rating :where(input) {
    cursor: pointer;
    border-radius: 0px
}
.select {
    display: inline-flex;
    cursor: pointer;
    user-select: none;
    appearance: none;
    height: 3rem;
    min-height: 3rem;
    padding-inline-start: 1rem;
    padding-inline-end: 2.5rem;
    font-size: 0.875rem;
    line-height: 1.25rem;
    line-height: 2
}

  /* disabled */
  /* &-disabled,
  &[disabled] {
    @apply pointer-events-none;
  } */
  /* multiple */
  .select[multiple] {
    height: auto
}
.stack {
    display: inline-grid;
}
  .stack > * {
    grid-column-start: 1;
    grid-row-start: 1;
    transform: translateY(10%) scale(0.9);
    z-index: 1;
}
  .stack > *:nth-child(2) {
    transform: translateY(5%) scale(0.95);
    z-index: 2;
  }
  .stack > *:nth-child(1) {
    transform: translateY(0) scale(1);
    z-index: 3;
  }
.stats {
  display: inline-grid
}
:where(.stats) {
  grid-auto-flow: column
}
.stat {
  display: inline-grid;
  width: 100%;
  grid-template-columns: repeat(1, 1fr)
}
.stat-figure {
  grid-column-start: 2;
  grid-row: span 3 / span 3;
  grid-row-start: 1;
  place-self: center;
  justify-self: end
}
.stat-title {
  grid-column-start: 1;
  white-space: nowrap
}
.stat-value {
  grid-column-start: 1;
  white-space: nowrap
}
.stat-desc {
  grid-column-start: 1;
  white-space: nowrap
}
.stat-actions {
  grid-column-start: 1;
  white-space: nowrap
}
/* .stats.grid-flow-row {
  @apply auto-rows-fr;
} */
.steps {
  display: inline-grid;
  grid-auto-flow: column;
  overflow: hidden;
  overflow-x: auto;
  counter-reset: step;
  grid-auto-columns: 1fr
}
  .steps .step {
  display: grid;
  grid-template-columns: repeat(1, minmax(0, 1fr));
  grid-template-rows: repeat(2, minmax(0, 1fr));
  place-items: center;
  text-align: center
}
.swap {

    position: relative;

    display: inline-grid;

    user-select: none;

    place-content: center
}

.swap > * {

    grid-column-start: 1;

    grid-row-start: 1
}

.swap input {

    appearance: none
}

.swap .swap-on,
.swap .swap-indeterminate,
.swap input:indeterminate ~ .swap-on {

    opacity: 0
}

.swap input:checked ~ .swap-off,
.swap-active .swap-off,
.swap input:indeterminate ~ .swap-off {

    opacity: 0
}

.swap input:checked ~ .swap-on,
.swap-active .swap-on,
.swap input:indeterminate ~ .swap-indeterminate {

    opacity: 1
}
.tabs {
  display: grid;
  align-items: flex-end;
}
.tabs-lifted:has(.tab-content[class^="rounded-"])
    .tab:first-child:not(:is(.tab-active, [aria-selected="true"])), .tabs-lifted:has(.tab-content[class*=" rounded-"])
    .tab:first-child:not(:is(.tab-active, [aria-selected="true"])) {
  border-bottom-color: transparent;
}
.tab {
  position: relative;
  grid-row-start: 1;
  display: inline-flex;
  height: 2rem;
  cursor: pointer;
  user-select: none;
  appearance: none;
  flex-wrap: wrap;
  align-items: center;
  justify-content: center;
  text-align: center;
  font-size: 0.875rem;
  line-height: 1.25rem;
  line-height: 2;
  --tab-padding: 1rem;
}
.tab:is(input[type="radio"]) {
  width: auto;
  border-bottom-right-radius: 0px;
  border-bottom-left-radius: 0px;
}
.tab:is(input[type="radio"]):after {
      --tw-content: attr(aria-label);
      content: var(--tw-content);
    }
.tab:not(input):empty {
  cursor: default;
    grid-column-start: span 9999;
}
.tab-content {
  grid-column-start: 1;
  grid-column-end: span 9999;
  grid-row-start: 2;
  margin-top: calc(var(--tab-border) * -1);
  display: none;
  border-color: transparent;
  border-width: var(--tab-border, 0);
}
:checked + .tab-content:nth-child(2),
  :is(.tab-active, [aria-selected="true"]) + .tab-content:nth-child(2) {
  border-start-start-radius: 0px;
}
input.tab:checked + .tab-content,
:is(.tab-active, [aria-selected="true"]) + .tab-content {
  display: block;
}
.table {
    position: relative;
    width: 100%
}
  .table :where(.table-pin-rows thead tr) {
    position: sticky;
    top: 0px;
    z-index: 1;
    --tw-bg-opacity: 1;
    background-color: var(--fallback-b1,oklch(var(--b1)/var(--tw-bg-opacity)))
}
  .table :where(.table-pin-rows tfoot tr) {
    position: sticky;
    bottom: 0px;
    z-index: 1;
    --tw-bg-opacity: 1;
    background-color: var(--fallback-b1,oklch(var(--b1)/var(--tw-bg-opacity)))
}
  .table :where(.table-pin-cols tr th) {
    position: sticky;
    left: 0px;
    right: 0px;
    --tw-bg-opacity: 1;
    background-color: var(--fallback-b1,oklch(var(--b1)/var(--tw-bg-opacity)))
}
  .table-zebra tbody tr:nth-child(even) :where(.table-pin-cols tr th) {
    --tw-bg-opacity: 1;
    background-color: var(--fallback-b2,oklch(var(--b2)/var(--tw-bg-opacity)))
}
.textarea {
    min-height: 3rem;
    flex-shrink: 1;
    padding-left: 1rem;
    padding-right: 1rem;
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
    font-size: 0.875rem;
    line-height: 1.25rem;
    line-height: 2
}
.timeline {
  position: relative;
  display: flex
}
:where(.timeline > li) {
  position: relative;
  display: grid;
  flex-shrink: 0;
  align-items: center;
  grid-template-rows: var(--timeline-row-start, minmax(0, 1fr)) auto var(
      --timeline-row-end,
      minmax(0, 1fr)
    );
  grid-template-columns: var(--timeline-col-start, minmax(0, 1fr)) auto var(
      --timeline-col-end,
      minmax(0, 1fr)
    )
}
.timeline > li > hr {
  width: 100%;
  border-width: 0px
}
:where(.timeline > li > hr):first-child {
  grid-column-start: 1;
  grid-row-start: 2
}
:where(.timeline > li > hr):last-child {
  grid-column-start: 3;
  grid-column-end: none;
  grid-row-start: 2;
  grid-row-end: auto
}
.timeline-start {
  grid-column-start: 1;
  grid-column-end: 4;
  grid-row-start: 1;
  grid-row-end: 2;
  margin: 0.25rem;
  align-self: flex-end;
  justify-self: center
}
.timeline-middle {
  grid-column-start: 2;
  grid-row-start: 2
}
.timeline-end {
  grid-column-start: 1;
  grid-column-end: 4;
  grid-row-start: 3;
  grid-row-end: 4;
  margin: 0.25rem;
  align-self: flex-start;
  justify-self: center
}
.toast {
    position: fixed;
    display: flex;
    min-width: fit-content;
    flex-direction: column;
    white-space: nowrap
}
.toggle {
    flex-shrink: 0
}
.alert {
  border-radius: var(--rounded-box, 1rem);
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: var(--fallback-b2,oklch(var(--b2)/var(--tw-border-opacity)));
  padding: 1rem;
  --tw-text-opacity: 1;
  color: var(--fallback-bc,oklch(var(--bc)/var(--tw-text-opacity)));
  --alert-bg: var(--fallback-b2,oklch(var(--b2)/1));
  --alert-bg-mix: var(--fallback-b1,oklch(var(--b1)/1));
  background-color: var(--alert-bg)
}
  .alert-info {
  border-color: var(--fallback-in,oklch(var(--in)/0.2));
  --tw-text-opacity: 1;
  color: var(--fallback-inc,oklch(var(--inc)/var(--tw-text-opacity)));
    --alert-bg: var(--fallback-in,oklch(var(--in)/1));
    --alert-bg-mix: var(--fallback-b1,oklch(var(--b1)/1))
}
  .alert-success {
  border-color: var(--fallback-su,oklch(var(--su)/0.2));
  --tw-text-opacity: 1;
  color: var(--fallback-suc,oklch(var(--suc)/var(--tw-text-opacity)));
    --alert-bg: var(--fallback-su,oklch(var(--su)/1));
    --alert-bg-mix: var(--fallback-b1,oklch(var(--b1)/1))
}
  .alert-warning {
  border-color: var(--fallback-wa,oklch(var(--wa)/0.2));
  --tw-text-opacity: 1;
  color: var(--fallback-wac,oklch(var(--wac)/var(--tw-text-opacity)));
    --alert-bg: var(--fallback-wa,oklch(var(--wa)/1));
    --alert-bg-mix: var(--fallback-b1,oklch(var(--b1)/1))
}
  .alert-error {
  border-color: var(--fallback-er,oklch(var(--er)/0.2));
  --tw-text-opacity: 1;
  color: var(--fallback-erc,oklch(var(--erc)/var(--tw-text-opacity)));
    --alert-bg: var(--fallback-er,oklch(var(--er)/1));
    --alert-bg-mix: var(--fallback-b1,oklch(var(--b1)/1))
}
.avatar-group {
    display: flex;
    overflow: hidden
}
  .avatar-group :where(.avatar) {
    overflow: hidden;
    border-radius: 9999px;
    border-width: 4px;
    --tw-border-opacity: 1;
    border-color: var(--fallback-b1,oklch(var(--b1)/var(--tw-border-opacity)))
}
.badge {
    border-radius: var(--rounded-badge, 1.9rem);
    border-width: 1px;
    --tw-border-opacity: 1;
    border-color: var(--fallback-b2,oklch(var(--b2)/var(--tw-border-opacity)));
    --tw-bg-opacity: 1;
    background-color: var(--fallback-b1,oklch(var(--b1)/var(--tw-bg-opacity)));
    --tw-text-opacity: 1;
    color: var(--fallback-bc,oklch(var(--bc)/var(--tw-text-opacity)))
}
  .badge-neutral {
    --tw-border-opacity: 1;
    border-color: var(--fallback-n,oklch(var(--n)/var(--tw-border-opacity)));
    --tw-bg-opacity: 1;
    background-color: var(--fallback-n,oklch(var(--n)/var(--tw-bg-opacity)));
    --tw-text-opacity: 1;
    color: var(--fallback-nc,oklch(var(--nc)/var(--tw-text-opacity)))
}
  .badge-primary {
    --tw-border-opacity: 1;
    border-color: var(--fallback-p,oklch(var(--p)/var(--tw-border-opacity)));
    --tw-bg-opacity: 1;
    background-color: var(--fallback-p,oklch(var(--p)/var(--tw-bg-opacity)));
    --tw-text-opacity: 1;
    color: var(--fallback-pc,oklch(var(--pc)/var(--tw-text-opacity)))
}
  .badge-secondary {
    --tw-border-opacity: 1;
    border-color: var(--fallback-s,oklch(var(--s)/var(--tw-border-opacity)));
    --tw-bg-opacity: 1;
    background-color: var(--fallback-s,oklch(var(--s)/var(--tw-bg-opacity)));
    --tw-text-opacity: 1;
    color: var(--fallback-sc,oklch(var(--sc)/var(--tw-text-opacity)))
}
  .badge-accent {
    --tw-border-opacity: 1;
    border-color: var(--fallback-a,oklch(var(--a)/var(--tw-border-opacity)));
    --tw-bg-opacity: 1;
    background-color: var(--fallback-a,oklch(var(--a)/var(--tw-bg-opacity)));
    --tw-text-opacity: 1;
    color: var(--fallback-ac,oklch(var(--ac)/var(--tw-text-opacity)))
}
  .badge-info {
    border-color: transparent;
    --tw-bg-opacity: 1;
    background-color: var(--fallback-in,oklch(var(--in)/var(--tw-bg-opacity)));
    --tw-text-opacity: 1;
    color: var(--fallback-inc,oklch(var(--inc)/var(--tw-text-opacity)))
}
  .badge-success {
    border-color: transparent;
    --tw-bg-opacity: 1;
    background-color: var(--fallback-su,oklch(var(--su)/var(--tw-bg-opacity)));
    --tw-text-opacity: 1;
    color: var(--fallback-suc,oklch(var(--suc)/var(--tw-text-opacity)))
}
  .badge-warning {
    border-color: transparent;
    --tw-bg-opacity: 1;
    background-color: var(--fallback-wa,oklch(var(--wa)/var(--tw-bg-opacity)));
    --tw-text-opacity: 1;
    color: var(--fallback-wac,oklch(var(--wac)/var(--tw-text-opacity)))
}
  .badge-error {
    border-color: transparent;
    --tw-bg-opacity: 1;
    background-color: var(--fallback-er,oklch(var(--er)/var(--tw-bg-opacity)));
    --tw-text-opacity: 1;
    color: var(--fallback-erc,oklch(var(--erc)/var(--tw-text-opacity)))
}
  .badge-ghost {
    --tw-border-opacity: 1;
    border-color: var(--fallback-b2,oklch(var(--b2)/var(--tw-border-opacity)));
    --tw-bg-opacity: 1;
    background-color: var(--fallback-b2,oklch(var(--b2)/var(--tw-bg-opacity)));
    --tw-text-opacity: 1;
    color: var(--fallback-bc,oklch(var(--bc)/var(--tw-text-opacity)))
}
  .badge-outline {
    border-color: currentColor;
    --tw-border-opacity: 0.5;
    background-color: transparent;
    color: currentColor
}
  .badge-outline.badge-neutral {
    --tw-text-opacity: 1;
    color: var(--fallback-n,oklch(var(--n)/var(--tw-text-opacity)))
}
  .badge-outline.badge-primary {
    --tw-text-opacity: 1;
    color: var(--fallback-p,oklch(var(--p)/var(--tw-text-opacity)))
}
  .badge-outline.badge-secondary {
    --tw-text-opacity: 1;
    color: var(--fallback-s,oklch(var(--s)/var(--tw-text-opacity)))
}
  .badge-outline.badge-accent {
    --tw-text-opacity: 1;
    color: var(--fallback-a,oklch(var(--a)/var(--tw-text-opacity)))
}
  .badge-outline.badge-info {
    --tw-text-opacity: 1;
    color: var(--fallback-in,oklch(var(--in)/var(--tw-text-opacity)))
}
  .badge-outline.badge-success {
    --tw-text-opacity: 1;
    color: var(--fallback-su,oklch(var(--su)/var(--tw-text-opacity)))
}
  .badge-outline.badge-warning {
    --tw-text-opacity: 1;
    color: var(--fallback-wa,oklch(var(--wa)/var(--tw-text-opacity)))
}
  .badge-outline.badge-error {
    --tw-text-opacity: 1;
    color: var(--fallback-er,oklch(var(--er)/var(--tw-text-opacity)))
}
.btm-nav {
      height: 4rem;
      --tw-bg-opacity: 1;
      background-color: var(--fallback-b1,oklch(var(--b1)/var(--tw-bg-opacity)));
      color: currentColor
}
  .btm-nav > * {
      border-color: currentColor
}
  .btm-nav > *:not(.active) {
      padding-top: 0.125rem
}
  /* active */
  .btm-nav > *:where(.active) {
      border-top-width: 2px;
      --tw-bg-opacity: 1;
      background-color: var(--fallback-b1,oklch(var(--b1)/var(--tw-bg-opacity)))
}
  /* disabled */
  .btm-nav > *.disabled,
    .btm-nav > *[disabled] {
      pointer-events: none;
      --tw-border-opacity: 0;
      background-color: var(--fallback-n,oklch(var(--n)/var(--tw-bg-opacity)));
      --tw-bg-opacity: 0.1;
      color: var(--fallback-bc,oklch(var(--bc)/var(--tw-text-opacity)));
      --tw-text-opacity: 0.2
}
  @media (hover: hover) {
      .btm-nav > *.disabled:hover,
      .btm-nav > *[disabled]:hover {
            pointer-events: none;
            --tw-border-opacity: 0;
            background-color: var(--fallback-n,oklch(var(--n)/var(--tw-bg-opacity)));
            --tw-bg-opacity: 0.1;
            color: var(--fallback-bc,oklch(var(--bc)/var(--tw-text-opacity)));
            --tw-text-opacity: 0.2
      }
    }
  .btm-nav > * .label {
      font-size: 1rem;
      line-height: 1.5rem
}
.breadcrumbs {
          padding-top: 0.5rem;
          padding-bottom: 0.5rem;
}
  .breadcrumbs > ul > li > a:focus, .breadcrumbs > ol > li > a:focus {
          outline: 2px solid transparent;
          outline-offset: 2px;
}
  .breadcrumbs > ul > li > a:focus-visible, .breadcrumbs > ol > li > a:focus-visible {
          outline: 2px solid currentColor;
          outline-offset: 2px;
        }
  .breadcrumbs > ul > li + *:before, .breadcrumbs > ol > li + *:before {
        content: "";
        margin-left: 0.5rem;
        margin-right: 0.75rem;
        display: block;
        height: 0.375rem;
        width: 0.375rem;
        --tw-rotate: 45deg;
        transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
        opacity: 0.4;
        border-top: 1px solid;
        border-right: 1px solid;
        background-color: transparent;
      }

[dir="rtl"] .breadcrumbs > ul > li + *:before,
[dir="rtl"] .breadcrumbs > ol > li + *:before {
  --tw-rotate: -135deg;
}
.btn {
  gap: 0.5rem;
  font-weight: 600;
  text-decoration-line: none;
  transition-duration: 200ms;
  transition-timing-function: cubic-bezier(0, 0, 0.2, 1);
  border-width: var(--border-btn, 1px);
  transition-property: color, background-color, border-color, opacity, box-shadow, transform
}
  @media (prefers-reduced-motion: no-preference) {.btn {
    animation: button-pop var(--animation-btn, 0.25s) ease-out
}
  }
  .btn:active:hover,
  .btn:active:focus {
    animation: button-pop 0s ease-out;
    transform: scale(var(--btn-focus-scale, 0.97));
  }
  /* default btn */
  .btn {
  --tw-text-opacity: 1;
  color: var(--fallback-bc,oklch(var(--bc)/var(--tw-text-opacity)));
  text-decoration-line: none;
  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
  outline-color: var(--fallback-bc,oklch(var(--bc)/1));
    background-color: oklch(var(--btn-color, var(--b2)) / var(--tw-bg-opacity));
    --tw-bg-opacity: 1;
    border-color: oklch(var(--btn-color, var(--b2)) / var(--tw-border-opacity));
    --tw-border-opacity: 1
}
  @supports not (color: oklch(0% 0 0)) {
  .btn {
      background-color: var(--btn-color, var(--fallback-b2));
      border-color: var(--btn-color, var(--fallback-b2))
  }
    }
  @media (hover: hover) {
      .btn:hover {
    --tw-border-opacity: 1;
    border-color: var(--fallback-b3,oklch(var(--b3)/var(--tw-border-opacity)));
    --tw-bg-opacity: 1;
    background-color: var(--fallback-b3,oklch(var(--b3)/var(--tw-bg-opacity)))
  }
        @supports (color: color-mix(in oklab, black, black)) {
      .btn:hover {
          background-color: color-mix(
            in oklab,
            oklch(var(--btn-color, var(--b2)) / var(--tw-bg-opacity, 1)) 90%,
            black
          );
          border-color: color-mix(
            in oklab,
            oklch(var(--btn-color, var(--b2)) / var(--tw-border-opacity, 1)) 90%,
            black
          )
      }
        }
        @supports not (color: oklch(0% 0 0)) {
      .btn:hover {
          background-color: var(--btn-color, var(--fallback-b2));
          border-color: var(--btn-color, var(--fallback-b2))
      }
        }
    }
  @supports (color: color-mix(in oklab, black, black)) {
  .btn-active {
        background-color: color-mix(
          in oklab,
          oklch(var(--btn-color, var(--b3)) / var(--tw-bg-opacity, 1)) 90%,
          black
        );
        border-color: color-mix(
          in oklab,
          oklch(var(--btn-color, var(--b3)) / var(--tw-border-opacity, 1)) 90%,
          black
        )
    }
      }
  .btn:focus-visible {
  outline-style: solid;
  outline-width: 2px;
  outline-offset: 2px
}
  /* brand colors */
  .btn-primary {
  --tw-text-opacity: 1;
  color: var(--fallback-pc,oklch(var(--pc)/var(--tw-text-opacity)));
  outline-color: var(--fallback-p,oklch(var(--p)/1))
}
  @supports (color: oklch(0% 0 0)) {
  .btn-primary {
      --btn-color: var(--p)
  }
    }
  @supports not (color: oklch(0% 0 0)) {
  .btn-primary {
      --btn-color: var(--fallback-p)
  }
    }
  .btn-secondary {
  --tw-text-opacity: 1;
  color: var(--fallback-sc,oklch(var(--sc)/var(--tw-text-opacity)));
  outline-color: var(--fallback-s,oklch(var(--s)/1))
}
  @supports (color: oklch(0% 0 0)) {
  .btn-secondary {
      --btn-color: var(--s)
  }
    }
  @supports not (color: oklch(0% 0 0)) {
  .btn-secondary {
      --btn-color: var(--fallback-s)
  }
    }
  .btn-accent {
  --tw-text-opacity: 1;
  color: var(--fallback-ac,oklch(var(--ac)/var(--tw-text-opacity)));
  outline-color: var(--fallback-a,oklch(var(--a)/1))
}
  @supports (color: oklch(0% 0 0)) {
  .btn-accent {
      --btn-color: var(--a)
  }
    }
  @supports not (color: oklch(0% 0 0)) {
  .btn-accent {
      --btn-color: var(--fallback-a)
  }
    }
  .btn-neutral {
  --tw-text-opacity: 1;
  color: var(--fallback-nc,oklch(var(--nc)/var(--tw-text-opacity)));
  outline-color: var(--fallback-n,oklch(var(--n)/1))
}
  @supports (color: oklch(0% 0 0)) {
  .btn-neutral {
      --btn-color: var(--n)
  }
    }
  @supports not (color: oklch(0% 0 0)) {
  .btn-neutral {
      --btn-color: var(--fallback-n)
  }
    }
  /* btn with state colors */
  .btn-info {
  --tw-text-opacity: 1;
  color: var(--fallback-inc,oklch(var(--inc)/var(--tw-text-opacity)));
  outline-color: var(--fallback-in,oklch(var(--in)/1))
}
  @supports (color: oklch(0% 0 0)) {
  .btn-info {
      --btn-color: var(--in)
  }
    }
  @supports not (color: oklch(0% 0 0)) {
  .btn-info {
      --btn-color: var(--fallback-in)
  }
    }
  .btn-success {
  --tw-text-opacity: 1;
  color: var(--fallback-suc,oklch(var(--suc)/var(--tw-text-opacity)));
  outline-color: var(--fallback-su,oklch(var(--su)/1))
}
  @supports (color: oklch(0% 0 0)) {
  .btn-success {
      --btn-color: var(--su)
  }
    }
  @supports not (color: oklch(0% 0 0)) {
  .btn-success {
      --btn-color: var(--fallback-su)
  }
    }
  .btn-warning {
  --tw-text-opacity: 1;
  color: var(--fallback-wac,oklch(var(--wac)/var(--tw-text-opacity)));
  outline-color: var(--fallback-wa,oklch(var(--wa)/1))
}
  @supports (color: oklch(0% 0 0)) {
  .btn-warning {
      --btn-color: var(--wa)
  }
    }
  @supports not (color: oklch(0% 0 0)) {
  .btn-warning {
      --btn-color: var(--fallback-wa)
  }
    }
  .btn-error {
  --tw-text-opacity: 1;
  color: var(--fallback-erc,oklch(var(--erc)/var(--tw-text-opacity)));
  outline-color: var(--fallback-er,oklch(var(--er)/1))
}
  @supports (color: oklch(0% 0 0)) {
  .btn-error {
      --btn-color: var(--er)
  }
    }
  @supports not (color: oklch(0% 0 0)) {
  .btn-error {
      --btn-color: var(--fallback-er)
  }
    }
  /* glass */
  .btn.glass {
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
  outline-color: currentColor
}
  @media (hover: hover) {
      .btn.glass:hover {
        --glass-opacity: 25%;
        --glass-border-opacity: 15%;
      }
    }
  .btn.glass.btn-active {
      --glass-opacity: 25%;
      --glass-border-opacity: 15%;
    }
  /* btn variants */
  .btn-ghost {
  border-width: 1px;
  border-color: transparent;
  background-color: transparent;
  color: currentColor;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
  outline-color: currentColor
}
  @media (hover: hover) {
      .btn-ghost:hover {
    border-color: transparent
  }
        @supports (color: oklch(0% 0 0)) {
      .btn-ghost:hover {
      background-color: var(--fallback-bc,oklch(var(--bc)/0.2))
    }
        }
    }
  .btn-ghost.btn-active {
  border-color: transparent;
  background-color: var(--fallback-bc,oklch(var(--bc)/0.2))
}
  .btn-link {
  border-color: transparent;
  background-color: transparent;
  --tw-text-opacity: 1;
  color: var(--fallback-p,oklch(var(--p)/var(--tw-text-opacity)));
  text-decoration-line: underline;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
  outline-color: currentColor
}
  @media (hover: hover) {
      .btn-link:hover {
    border-color: transparent;
    background-color: transparent;
    text-decoration-line: underline
  }
    }
  .btn-link.btn-active {
  border-color: transparent;
  background-color: transparent;
  text-decoration-line: underline
}
  /* outline */
  .btn-outline {
  border-color: currentColor;
  background-color: transparent;
  --tw-text-opacity: 1;
  color: var(--fallback-bc,oklch(var(--bc)/var(--tw-text-opacity)));
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow)
}
  @media (hover: hover) {
      .btn-outline:hover {
    --tw-border-opacity: 1;
    border-color: var(--fallback-bc,oklch(var(--bc)/var(--tw-border-opacity)));
    --tw-bg-opacity: 1;
    background-color: var(--fallback-bc,oklch(var(--bc)/var(--tw-bg-opacity)));
    --tw-text-opacity: 1;
    color: var(--fallback-b1,oklch(var(--b1)/var(--tw-text-opacity)))
  }
    }
  .btn-outline.btn-active {
  --tw-border-opacity: 1;
  border-color: var(--fallback-bc,oklch(var(--bc)/var(--tw-border-opacity)));
  --tw-bg-opacity: 1;
  background-color: var(--fallback-bc,oklch(var(--bc)/var(--tw-bg-opacity)));
  --tw-text-opacity: 1;
  color: var(--fallback-b1,oklch(var(--b1)/var(--tw-text-opacity)))
}
  .btn-outline.btn-primary {
  --tw-text-opacity: 1;
  color: var(--fallback-p,oklch(var(--p)/var(--tw-text-opacity)))
}
  @media (hover: hover) {
        .btn-outline.btn-primary:hover {
    --tw-text-opacity: 1;
    color: var(--fallback-pc,oklch(var(--pc)/var(--tw-text-opacity)))
  }
          @supports (color: color-mix(in oklab, black, black)) {
        .btn-outline.btn-primary:hover {
            background-color: color-mix(in oklab, var(--fallback-p,oklch(var(--p)/1)) 90%, black);
            border-color: color-mix(in oklab, var(--fallback-p,oklch(var(--p)/1)) 90%, black)
        }
          }
      }
  .btn-outline.btn-primary.btn-active {
  --tw-text-opacity: 1;
  color: var(--fallback-pc,oklch(var(--pc)/var(--tw-text-opacity)))
}
  @supports (color: color-mix(in oklab, black, black)) {
  .btn-outline.btn-primary.btn-active {
          background-color: color-mix(in oklab, var(--fallback-p,oklch(var(--p)/1)) 90%, black);
          border-color: color-mix(in oklab, var(--fallback-p,oklch(var(--p)/1)) 90%, black)
      }
        }
  .btn-outline.btn-secondary {
  --tw-text-opacity: 1;
  color: var(--fallback-s,oklch(var(--s)/var(--tw-text-opacity)))
}
  @media (hover: hover) {
        .btn-outline.btn-secondary:hover {
    --tw-text-opacity: 1;
    color: var(--fallback-sc,oklch(var(--sc)/var(--tw-text-opacity)))
  }
          @supports (color: color-mix(in oklab, black, black)) {
        .btn-outline.btn-secondary:hover {
            background-color: color-mix(in oklab, var(--fallback-s,oklch(var(--s)/1)) 90%, black);
            border-color: color-mix(in oklab, var(--fallback-s,oklch(var(--s)/1)) 90%, black)
        }
          }
      }
  .btn-outline.btn-secondary.btn-active {
  --tw-text-opacity: 1;
  color: var(--fallback-sc,oklch(var(--sc)/var(--tw-text-opacity)))
}
  @supports (color: color-mix(in oklab, black, black)) {
  .btn-outline.btn-secondary.btn-active {
          background-color: color-mix(in oklab, var(--fallback-s,oklch(var(--s)/1)) 90%, black);
          border-color: color-mix(in oklab, var(--fallback-s,oklch(var(--s)/1)) 90%, black)
      }
        }
  .btn-outline.btn-accent {
  --tw-text-opacity: 1;
  color: var(--fallback-a,oklch(var(--a)/var(--tw-text-opacity)))
}
  @media (hover: hover) {
        .btn-outline.btn-accent:hover {
    --tw-text-opacity: 1;
    color: var(--fallback-ac,oklch(var(--ac)/var(--tw-text-opacity)))
  }
          @supports (color: color-mix(in oklab, black, black)) {
        .btn-outline.btn-accent:hover {
            background-color: color-mix(in oklab, var(--fallback-a,oklch(var(--a)/1)) 90%, black);
            border-color: color-mix(in oklab, var(--fallback-a,oklch(var(--a)/1)) 90%, black)
        }
          }
      }
  .btn-outline.btn-accent.btn-active {
  --tw-text-opacity: 1;
  color: var(--fallback-ac,oklch(var(--ac)/var(--tw-text-opacity)))
}
  @supports (color: color-mix(in oklab, black, black)) {
  .btn-outline.btn-accent.btn-active {
          background-color: color-mix(in oklab, var(--fallback-a,oklch(var(--a)/1)) 90%, black);
          border-color: color-mix(in oklab, var(--fallback-a,oklch(var(--a)/1)) 90%, black)
      }
        }
  .btn-outline.btn-success {
  --tw-text-opacity: 1;
  color: var(--fallback-su,oklch(var(--su)/var(--tw-text-opacity)))
}
  @media (hover: hover) {
        .btn-outline.btn-success:hover {
    --tw-text-opacity: 1;
    color: var(--fallback-suc,oklch(var(--suc)/var(--tw-text-opacity)))
  }
          @supports (color: color-mix(in oklab, black, black)) {
        .btn-outline.btn-success:hover {
            background-color: color-mix(in oklab, var(--fallback-su,oklch(var(--su)/1)) 90%, black);
            border-color: color-mix(in oklab, var(--fallback-su,oklch(var(--su)/1)) 90%, black)
        }
          }
      }
  .btn-outline.btn-success.btn-active {
  --tw-text-opacity: 1;
  color: var(--fallback-suc,oklch(var(--suc)/var(--tw-text-opacity)))
}
  @supports (color: color-mix(in oklab, black, black)) {
  .btn-outline.btn-success.btn-active {
          background-color: color-mix(in oklab, var(--fallback-su,oklch(var(--su)/1)) 90%, black);
          border-color: color-mix(in oklab, var(--fallback-su,oklch(var(--su)/1)) 90%, black)
      }
        }
  .btn-outline.btn-info {
  --tw-text-opacity: 1;
  color: var(--fallback-in,oklch(var(--in)/var(--tw-text-opacity)))
}
  @media (hover: hover) {
        .btn-outline.btn-info:hover {
    --tw-text-opacity: 1;
    color: var(--fallback-inc,oklch(var(--inc)/var(--tw-text-opacity)))
  }
          @supports (color: color-mix(in oklab, black, black)) {
        .btn-outline.btn-info:hover {
            background-color: color-mix(in oklab, var(--fallback-in,oklch(var(--in)/1)) 90%, black);
            border-color: color-mix(in oklab, var(--fallback-in,oklch(var(--in)/1)) 90%, black)
        }
          }
      }
  .btn-outline.btn-info.btn-active {
  --tw-text-opacity: 1;
  color: var(--fallback-inc,oklch(var(--inc)/var(--tw-text-opacity)))
}
  @supports (color: color-mix(in oklab, black, black)) {
  .btn-outline.btn-info.btn-active {
          background-color: color-mix(in oklab, var(--fallback-in,oklch(var(--in)/1)) 90%, black);
          border-color: color-mix(in oklab, var(--fallback-in,oklch(var(--in)/1)) 90%, black)
      }
        }
  .btn-outline.btn-warning {
  --tw-text-opacity: 1;
  color: var(--fallback-wa,oklch(var(--wa)/var(--tw-text-opacity)))
}
  @media (hover: hover) {
        .btn-outline.btn-warning:hover {
    --tw-text-opacity: 1;
    color: var(--fallback-wac,oklch(var(--wac)/var(--tw-text-opacity)))
  }
          @supports (color: color-mix(in oklab, black, black)) {
        .btn-outline.btn-warning:hover {
            background-color: color-mix(in oklab, var(--fallback-wa,oklch(var(--wa)/1)) 90%, black);
            border-color: color-mix(in oklab, var(--fallback-wa,oklch(var(--wa)/1)) 90%, black)
        }
          }
      }
  .btn-outline.btn-warning.btn-active {
  --tw-text-opacity: 1;
  color: var(--fallback-wac,oklch(var(--wac)/var(--tw-text-opacity)))
}
  @supports (color: color-mix(in oklab, black, black)) {
  .btn-outline.btn-warning.btn-active {
          background-color: color-mix(in oklab, var(--fallback-wa,oklch(var(--wa)/1)) 90%, black);
          border-color: color-mix(in oklab, var(--fallback-wa,oklch(var(--wa)/1)) 90%, black)
      }
        }
  .btn-outline.btn-error {
  --tw-text-opacity: 1;
  color: var(--fallback-er,oklch(var(--er)/var(--tw-text-opacity)))
}
  @media (hover: hover) {
        .btn-outline.btn-error:hover {
    --tw-text-opacity: 1;
    color: var(--fallback-erc,oklch(var(--erc)/var(--tw-text-opacity)))
  }
          @supports (color: color-mix(in oklab, black, black)) {
        .btn-outline.btn-error:hover {
            background-color: color-mix(in oklab, var(--fallback-er,oklch(var(--er)/1)) 90%, black);
            border-color: color-mix(in oklab, var(--fallback-er,oklch(var(--er)/1)) 90%, black)
        }
          }
      }
  .btn-outline.btn-error.btn-active {
  --tw-text-opacity: 1;
  color: var(--fallback-erc,oklch(var(--erc)/var(--tw-text-opacity)))
}
  @supports (color: color-mix(in oklab, black, black)) {
  .btn-outline.btn-error.btn-active {
          background-color: color-mix(in oklab, var(--fallback-er,oklch(var(--er)/1)) 90%, black);
          border-color: color-mix(in oklab, var(--fallback-er,oklch(var(--er)/1)) 90%, black)
      }
        }
  /* disabled */
  .btn.btn-disabled,
  .btn[disabled],
  .btn:disabled {
  --tw-border-opacity: 0;
  background-color: var(--fallback-n,oklch(var(--n)/var(--tw-bg-opacity)));
  --tw-bg-opacity: 0.2;
  color: var(--fallback-bc,oklch(var(--bc)/var(--tw-text-opacity)));
  --tw-text-opacity: 0.2
}
  @media (hover: hover) {
    .btn-disabled:hover,
    .btn[disabled]:hover,
    .btn:disabled:hover {
    --tw-border-opacity: 0;
    background-color: var(--fallback-n,oklch(var(--n)/var(--tw-bg-opacity)));
    --tw-bg-opacity: 0.2;
    color: var(--fallback-bc,oklch(var(--bc)/var(--tw-text-opacity)));
    --tw-text-opacity: 0.2
  }
  }

/* radio input and checkbox as button */

.btn:is(input[type="checkbox"]:checked),
.btn:is(input[type="radio"]:checked) {
  --tw-border-opacity: 1;
  border-color: var(--fallback-p,oklch(var(--p)/var(--tw-border-opacity)));
  --tw-bg-opacity: 1;
  background-color: var(--fallback-p,oklch(var(--p)/var(--tw-bg-opacity)));
  --tw-text-opacity: 1;
  color: var(--fallback-pc,oklch(var(--pc)/var(--tw-text-opacity)))
}

@media (hover: hover) {
      @supports (color: color-mix(in oklab, black, black)) {
    .btn:is(input[type="checkbox"]:checked):hover, .btn:is(input[type="radio"]:checked):hover {
        background-color: color-mix(in oklab, var(--fallback-p,oklch(var(--p)/1)) 90%, black);
        border-color: color-mix(in oklab, var(--fallback-p,oklch(var(--p)/1)) 90%, black)
    }
      }
  }

.btn:is(input[type="checkbox"]:checked):focus-visible, .btn:is(input[type="radio"]:checked):focus-visible {
  outline-color: var(--fallback-p,oklch(var(--p)/1))
}

@keyframes button-pop {
  0% {
    transform: scale(var(--btn-focus-scale, 0.98));
  }
  40% {
    transform: scale(1.02);
  }
  100% {
    transform: scale(1);
  }
}
.card {
    border-radius: var(--rounded-box, 1rem);
}
  .card :where(figure:first-child) {
    overflow: hidden;
    border-start-start-radius: inherit;
    border-start-end-radius: inherit;
    border-end-start-radius: unset;
    border-end-end-radius: unset;
}
  .card :where(figure:last-child) {
    overflow: hidden;
    border-start-start-radius: unset;
    border-start-end-radius: unset;
    border-end-start-radius: inherit;
    border-end-end-radius: inherit;
}
  .card:focus-visible {
    outline: 2px solid currentColor;
    outline-offset: 2px;
  }
  .card.bordered {
    border-width: 1px;
    --tw-border-opacity: 1;
    border-color: var(--fallback-b2,oklch(var(--b2)/var(--tw-border-opacity)));
}
  .card-bordered {
    border-width: 1px;
    --tw-border-opacity: 1;
    border-color: var(--fallback-b2,oklch(var(--b2)/var(--tw-border-opacity)));
}
  .card.compact .card-body {
    padding: 1rem;
    font-size: 0.875rem;
    line-height: 1.25rem;
}
  .card-body {
    padding: var(--padding-card, 2rem);
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }
  .card-title {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1.25rem;
    line-height: 1.75rem;
    font-weight: 600;
}
  .card.image-full:before {
    z-index: 10;
    border-radius: var(--rounded-box, 1rem);
    --tw-bg-opacity: 1;
    background-color: var(--fallback-n,oklch(var(--n)/var(--tw-bg-opacity)));
    opacity: 0.75;
}
  .card.image-full > .card-body {
    z-index: 20;
    --tw-text-opacity: 1;
    color: var(--fallback-nc,oklch(var(--nc)/var(--tw-text-opacity)));
}
  .card.image-full :where(figure) {
    overflow: hidden;
      border-radius: inherit;
}
.carousel {
  -ms-overflow-style: none;
  scrollbar-width: none;
}
  .carousel::-webkit-scrollbar {
  display: none;
}
.chat-bubble {
    border-radius: var(--rounded-box, 1rem);
    min-height: 2.75rem;
    min-width: 2.75rem;
}

    /* default */
    .chat-bubble {
    --tw-bg-opacity: 1;
    background-color: var(--fallback-n,oklch(var(--n)/var(--tw-bg-opacity)));
    --tw-text-opacity: 1;
    color: var(--fallback-nc,oklch(var(--nc)/var(--tw-text-opacity)));
}
    /* brand colors */
    .chat-bubble-primary {
    --tw-bg-opacity: 1;
    background-color: var(--fallback-p,oklch(var(--p)/var(--tw-bg-opacity)));
    --tw-text-opacity: 1;
    color: var(--fallback-pc,oklch(var(--pc)/var(--tw-text-opacity)));
}
    .chat-bubble-secondary {
    --tw-bg-opacity: 1;
    background-color: var(--fallback-s,oklch(var(--s)/var(--tw-bg-opacity)));
    --tw-text-opacity: 1;
    color: var(--fallback-sc,oklch(var(--sc)/var(--tw-text-opacity)));
}
    .chat-bubble-accent {
    --tw-bg-opacity: 1;
    background-color: var(--fallback-a,oklch(var(--a)/var(--tw-bg-opacity)));
    --tw-text-opacity: 1;
    color: var(--fallback-ac,oklch(var(--ac)/var(--tw-text-opacity)));
}
    /* state colors */
    .chat-bubble-info {
    --tw-bg-opacity: 1;
    background-color: var(--fallback-in,oklch(var(--in)/var(--tw-bg-opacity)));
    --tw-text-opacity: 1;
    color: var(--fallback-inc,oklch(var(--inc)/var(--tw-text-opacity)));
}
    .chat-bubble-success {
    --tw-bg-opacity: 1;
    background-color: var(--fallback-su,oklch(var(--su)/var(--tw-bg-opacity)));
    --tw-text-opacity: 1;
    color: var(--fallback-suc,oklch(var(--suc)/var(--tw-text-opacity)));
}
    .chat-bubble-warning {
    --tw-bg-opacity: 1;
    background-color: var(--fallback-wa,oklch(var(--wa)/var(--tw-bg-opacity)));
    --tw-text-opacity: 1;
    color: var(--fallback-wac,oklch(var(--wac)/var(--tw-text-opacity)));
}
    .chat-bubble-error {
    --tw-bg-opacity: 1;
    background-color: var(--fallback-er,oklch(var(--er)/var(--tw-bg-opacity)));
    --tw-text-opacity: 1;
    color: var(--fallback-erc,oklch(var(--erc)/var(--tw-text-opacity)));
}
  .chat-start .chat-bubble {
    border-end-start-radius: 0px;
}
  .chat-start .chat-bubble:before {
      inset-inline-start: -0.749rem;
    }
  .chat-end .chat-bubble {
    border-end-end-radius: 0px;
}
  .chat-end .chat-bubble:before {
      inset-inline-start: 99.9%;
    }
.checkbox {
  --chkbg: var(--fallback-bc,oklch(var(--bc)/1));
  --chkfg: var(--fallback-b1,oklch(var(--b1)/1));
  height: 1.5rem;
  width: 1.5rem;
  cursor: pointer;
  appearance: none;
  border-radius: var(--rounded-btn, 0.5rem);
  border-width: 1px;
  border-color: var(--fallback-bc,oklch(var(--bc)/var(--tw-border-opacity)));
  --tw-border-opacity: 0.2;
}
  .checkbox:focus {
    box-shadow: none;
  }
  .checkbox:focus-visible {
  outline-style: solid;
  outline-width: 2px;
  outline-offset: 2px;
  outline-color: var(--fallback-bc,oklch(var(--bc)/1));
}
  .checkbox:disabled {
  border-width: 0px;
}
  .checkbox:checked,
  .checkbox[aria-checked="true"] {
  background-repeat: no-repeat;
    animation: checkmark var(--animation-input, 0.2s) ease-out;
    background-color: var(--chkbg);
    background-image: linear-gradient(-45deg, transparent 65%, var(--chkbg) 65.99%),
      linear-gradient(45deg, transparent 75%, var(--chkbg) 75.99%),
      linear-gradient(-45deg, var(--chkbg) 40%, transparent 40.99%),
      linear-gradient(
        45deg,
        var(--chkbg) 30%,
        var(--chkfg) 30.99%,
        var(--chkfg) 40%,
        transparent 40.99%
      ),
      linear-gradient(-45deg, var(--chkfg) 50%, var(--chkbg) 50.99%);
}
  .checkbox:indeterminate {
  --tw-bg-opacity: 1;
  background-color: var(--fallback-bc,oklch(var(--bc)/var(--tw-bg-opacity)));
  background-repeat: no-repeat;
    animation: checkmark var(--animation-input, 0.2s) ease-out;
    background-image: linear-gradient(90deg, transparent 80%, var(--chkbg) 80%),
      linear-gradient(-90deg, transparent 80%, var(--chkbg) 80%),
      linear-gradient(0deg, var(--chkbg) 43%, var(--chkfg) 43%, var(--chkfg) 57%, var(--chkbg) 57%);
}
  .checkbox-primary {
    --chkbg: var(--fallback-p,oklch(var(--p)/1));
    --chkfg: var(--fallback-pc,oklch(var(--pc)/1));
    --tw-border-opacity: 1;
    border-color: var(--fallback-p,oklch(var(--p)/var(--tw-border-opacity)));
  }
  @media(hover:hover) {
  .checkbox-primary:hover {
    --tw-border-opacity: 1;
    border-color: var(--fallback-p,oklch(var(--p)/var(--tw-border-opacity)));
  }
}
  .checkbox-primary:focus-visible {
  outline-color: var(--fallback-p,oklch(var(--p)/1));
}
  .checkbox-primary:checked,
    .checkbox-primary[aria-checked="true"] {
  --tw-border-opacity: 1;
  border-color: var(--fallback-p,oklch(var(--p)/var(--tw-border-opacity)));
  --tw-bg-opacity: 1;
  background-color: var(--fallback-p,oklch(var(--p)/var(--tw-bg-opacity)));
  --tw-text-opacity: 1;
  color: var(--fallback-pc,oklch(var(--pc)/var(--tw-text-opacity)));
}
  .checkbox-secondary {
    --chkbg: var(--fallback-s,oklch(var(--s)/1));
    --chkfg: var(--fallback-sc,oklch(var(--sc)/1));
    --tw-border-opacity: 1;
    border-color: var(--fallback-s,oklch(var(--s)/var(--tw-border-opacity)));
  }
  @media(hover:hover) {
  .checkbox-secondary:hover {
    --tw-border-opacity: 1;
    border-color: var(--fallback-s,oklch(var(--s)/var(--tw-border-opacity)));
  }
}
  .checkbox-secondary:focus-visible {
  outline-color: var(--fallback-s,oklch(var(--s)/1));
}
  .checkbox-secondary:checked,
    .checkbox-secondary[aria-checked="true"] {
  --tw-border-opacity: 1;
  border-color: var(--fallback-s,oklch(var(--s)/var(--tw-border-opacity)));
  --tw-bg-opacity: 1;
  background-color: var(--fallback-s,oklch(var(--s)/var(--tw-bg-opacity)));
  --tw-text-opacity: 1;
  color: var(--fallback-sc,oklch(var(--sc)/var(--tw-text-opacity)));
}
  .checkbox-accent {
    --chkbg: var(--fallback-a,oklch(var(--a)/1));
    --chkfg: var(--fallback-ac,oklch(var(--ac)/1));
    --tw-border-opacity: 1;
    border-color: var(--fallback-a,oklch(var(--a)/var(--tw-border-opacity)));
  }
  @media(hover:hover) {
  .checkbox-accent:hover {
    --tw-border-opacity: 1;
    border-color: var(--fallback-a,oklch(var(--a)/var(--tw-border-opacity)));
  }
}
  .checkbox-accent:focus-visible {
  outline-color: var(--fallback-a,oklch(var(--a)/1));
}
  .checkbox-accent:checked,
    .checkbox-accent[aria-checked="true"] {
  --tw-border-opacity: 1;
  border-color: var(--fallback-a,oklch(var(--a)/var(--tw-border-opacity)));
  --tw-bg-opacity: 1;
  background-color: var(--fallback-a,oklch(var(--a)/var(--tw-bg-opacity)));
  --tw-text-opacity: 1;
  color: var(--fallback-ac,oklch(var(--ac)/var(--tw-text-opacity)));
}
  .checkbox-success {
    --chkbg: var(--fallback-su,oklch(var(--su)/1));
    --chkfg: var(--fallback-suc,oklch(var(--suc)/1));
    --tw-border-opacity: 1;
    border-color: var(--fallback-su,oklch(var(--su)/var(--tw-border-opacity)));
  }
  @media(hover:hover) {
  .checkbox-success:hover {
    --tw-border-opacity: 1;
    border-color: var(--fallback-su,oklch(var(--su)/var(--tw-border-opacity)));
  }
}
  .checkbox-success:focus-visible {
  outline-color: var(--fallback-su,oklch(var(--su)/1));
}
  .checkbox-success:checked,
    .checkbox-success[aria-checked="true"] {
  --tw-border-opacity: 1;
  border-color: var(--fallback-su,oklch(var(--su)/var(--tw-border-opacity)));
  --tw-bg-opacity: 1;
  background-color: var(--fallback-su,oklch(var(--su)/var(--tw-bg-opacity)));
  --tw-text-opacity: 1;
  color: var(--fallback-suc,oklch(var(--suc)/var(--tw-text-opacity)));
}
  .checkbox-warning {
    --chkbg: var(--fallback-wa,oklch(var(--wa)/1));
    --chkfg: var(--fallback-wac,oklch(var(--wac)/1));
    --tw-border-opacity: 1;
    border-color: var(--fallback-wa,oklch(var(--wa)/var(--tw-border-opacity)));
  }
  @media(hover:hover) {
  .checkbox-warning:hover {
    --tw-border-opacity: 1;
    border-color: var(--fallback-wa,oklch(var(--wa)/var(--tw-border-opacity)));
  }
}
  .checkbox-warning:focus-visible {
  outline-color: var(--fallback-wa,oklch(var(--wa)/1));
}
  .checkbox-warning:checked,
    .checkbox-warning[aria-checked="true"] {
  --tw-border-opacity: 1;
  border-color: var(--fallback-wa,oklch(var(--wa)/var(--tw-border-opacity)));
  --tw-bg-opacity: 1;
  background-color: var(--fallback-wa,oklch(var(--wa)/var(--tw-bg-opacity)));
  --tw-text-opacity: 1;
  color: var(--fallback-wac,oklch(var(--wac)/var(--tw-text-opacity)));
}
  .checkbox-info {
    --chkbg: var(--fallback-in,oklch(var(--in)/1));
    --chkfg: var(--fallback-inc,oklch(var(--inc)/1));
    --tw-border-opacity: 1;
    border-color: var(--fallback-in,oklch(var(--in)/var(--tw-border-opacity)));
  }
  @media(hover:hover) {
  .checkbox-info:hover {
    --tw-border-opacity: 1;
    border-color: var(--fallback-in,oklch(var(--in)/var(--tw-border-opacity)));
  }
}
  .checkbox-info:focus-visible {
  outline-color: var(--fallback-in,oklch(var(--in)/1));
}
  .checkbox-info:checked,
    .checkbox-info[aria-checked="true"] {
  --tw-border-opacity: 1;
  border-color: var(--fallback-in,oklch(var(--in)/var(--tw-border-opacity)));
  --tw-bg-opacity: 1;
  background-color: var(--fallback-in,oklch(var(--in)/var(--tw-bg-opacity)));
  --tw-text-opacity: 1;
  color: var(--fallback-inc,oklch(var(--inc)/var(--tw-text-opacity)));
}
  .checkbox-error {
    --chkbg: var(--fallback-er,oklch(var(--er)/1));
    --chkfg: var(--fallback-erc,oklch(var(--erc)/1));
    --tw-border-opacity: 1;
    border-color: var(--fallback-er,oklch(var(--er)/var(--tw-border-opacity)));
  }
  @media(hover:hover) {
  .checkbox-error:hover {
    --tw-border-opacity: 1;
    border-color: var(--fallback-er,oklch(var(--er)/var(--tw-border-opacity)));
  }
}
  .checkbox-error:focus-visible {
  outline-color: var(--fallback-er,oklch(var(--er)/1));
}
  .checkbox-error:checked,
    .checkbox-error[aria-checked="true"] {
  --tw-border-opacity: 1;
  border-color: var(--fallback-er,oklch(var(--er)/var(--tw-border-opacity)));
  --tw-bg-opacity: 1;
  background-color: var(--fallback-er,oklch(var(--er)/var(--tw-bg-opacity)));
  --tw-text-opacity: 1;
  color: var(--fallback-erc,oklch(var(--erc)/var(--tw-text-opacity)));
}
  .checkbox:disabled {
  cursor: not-allowed;
  border-color: transparent;
  --tw-bg-opacity: 1;
  background-color: var(--fallback-bc,oklch(var(--bc)/var(--tw-bg-opacity)));
  opacity: 0.2;
}

@keyframes checkmark {
  0% {
    background-position-y: 5px;
  }
  50% {
    background-position-y: -2px;
  }
  100% {
    background-position-y: 0;
  }
}

/* backward compatibility */

.checkbox-mark {
  display: none;
}
.collapse {
  width: 100%;
  border-radius: var(--rounded-box, 1rem);
}
details.collapse {
  width: 100%;
}
details.collapse summary {
  position: relative;
  display: block;
}
details.collapse summary::-webkit-details-marker {
  display: none;
}
.collapse:focus-visible {
  outline-style: solid;
  outline-width: 2px;
  outline-offset: 2px;
  outline-color: var(--fallback-bc,oklch(var(--bc)/1));
}
details.collapse summary {
  outline: 2px solid transparent;
  outline-offset: 2px;
}
.collapse:has(.collapse-title:focus-visible),
.collapse:has(> input[type="checkbox"]:focus-visible),
.collapse:has(> input[type="radio"]:focus-visible) {
  outline-style: solid;
  outline-width: 2px;
  outline-offset: 2px;
  outline-color: var(--fallback-bc,oklch(var(--bc)/1));
}
.collapse-arrow > .collapse-title:after {
  position: absolute;
  display: block;
  height: 0.5rem;
  width: 0.5rem;
  --tw-translate-y: -100%;
  --tw-rotate: 45deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
  transition-timing-function: cubic-bezier(0, 0, 0.2, 1);
  transition-duration: 0.2s;
  top: 1.9rem;
  inset-inline-end: 1.4rem;
  content: "";
  transform-origin: 75% 75%;
  box-shadow: 2px 2px;
  pointer-events: none;
}
.collapse-plus > .collapse-title:after {
  position: absolute;
  display: block;
  height: 0.5rem;
  width: 0.5rem;
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
  transition-timing-function: cubic-bezier(0, 0, 0.2, 1);
  top: 0.9rem;
  inset-inline-end: 1.4rem;
  content: "+";
  pointer-events: none;
}
.collapse:not(.collapse-open):not(.collapse-close) > input[type="checkbox"],
.collapse:not(.collapse-open):not(.collapse-close) > input[type="radio"]:not(:checked),
.collapse:not(.collapse-open):not(.collapse-close) > .collapse-title {
  cursor: pointer;
}
.collapse:focus:not(.collapse-open):not(.collapse-close):not(.collapse[open]) > .collapse-title {
  cursor: unset;
}
.collapse-title {
  position: relative;
}
:where(.collapse > input[type="checkbox"]),
:where(.collapse > input[type="radio"]) {
  z-index: 1;
}
.collapse-title,
:where(.collapse > input[type="checkbox"]),
:where(.collapse > input[type="radio"]) {
  width: 100%;
  padding: 1rem;
  padding-inline-end: 3rem;
  min-height: 3.75rem;
  transition: background-color 0.2s ease-out;
}
.collapse-content {
  padding-left: 1rem;
  padding-right: 1rem;
  cursor: unset;
  transition:
    padding 0.2s ease-out,
    background-color 0.2s ease-out;
}
.collapse[open] > :where(.collapse-content),
.collapse-open > :where(.collapse-content),
.collapse:focus:not(.collapse-close) > :where(.collapse-content),
.collapse:not(.collapse-close) > :where(input[type="checkbox"]:checked ~ .collapse-content),
.collapse:not(.collapse-close) > :where(input[type="radio"]:checked ~ .collapse-content) {
  padding-bottom: 1rem;
  transition:
    padding 0.2s ease-out,
    background-color 0.2s ease-out;
}
.collapse[open].collapse-arrow > .collapse-title:after,
.collapse-open.collapse-arrow > .collapse-title:after,
.collapse-arrow:focus:not(.collapse-close) > .collapse-title:after,
.collapse-arrow:not(.collapse-close) > input[type="checkbox"]:checked ~ .collapse-title:after,
.collapse-arrow:not(.collapse-close) > input[type="radio"]:checked ~ .collapse-title:after {
  --tw-translate-y: -50%;
  --tw-rotate: 225deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.collapse[open].collapse-plus > .collapse-title:after,
.collapse-open.collapse-plus > .collapse-title:after,
.collapse-plus:focus:not(.collapse-close) > .collapse-title:after,
.collapse-plus:not(.collapse-close) > input[type="checkbox"]:checked ~ .collapse-title:after,
.collapse-plus:not(.collapse-close) > input[type="radio"]:checked ~ .collapse-title:after {
  content: "−";
}
.countdown > *:before {
      text-align: center;
      transition: all 1s cubic-bezier(1, 0, 0, 1);
    }
.diff-item-1:after {
  border-radius: 9999px;
  border-width: 2px;
  --tw-border-opacity: 1;
  border-color: var(--fallback-b1,oklch(var(--b1)/var(--tw-border-opacity)));
  background-color: var(--fallback-b1,oklch(var(--b1)/0.5));
  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
  outline-style: solid;
  outline-offset: -3px;
  outline-color: var(--fallback-bc,oklch(var(--bc)/0.05));
  --tw-backdrop-blur: blur(8px);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  translate: 50% -50%
}
.diff-item-2 {
  border-right-width: 2px;
  --tw-border-opacity: 1;
  border-color: var(--fallback-b1,oklch(var(--b1)/var(--tw-border-opacity)))
}
.divider {
    margin-top: 1rem;
    margin-bottom: 1rem;
    height: 1rem;
    white-space: nowrap
}
  .divider:before,
  .divider:after {
    background-color: var(--fallback-bc,oklch(var(--bc)/0.1))
}
  .divider:not(:empty) {
    gap: 1rem
}
  .divider-neutral:before,
  .divider-neutral:after {
    --tw-bg-opacity: 1;
    background-color: var(--fallback-n,oklch(var(--n)/var(--tw-bg-opacity)))
}
  .divider-primary:before,
  .divider-primary:after {
    --tw-bg-opacity: 1;
    background-color: var(--fallback-p,oklch(var(--p)/var(--tw-bg-opacity)))
}
  .divider-secondary:before,
  .divider-secondary:after {
    --tw-bg-opacity: 1;
    background-color: var(--fallback-s,oklch(var(--s)/var(--tw-bg-opacity)))
}
  .divider-accent:before,
  .divider-accent:after {
    --tw-bg-opacity: 1;
    background-color: var(--fallback-a,oklch(var(--a)/var(--tw-bg-opacity)))
}
  .divider-success:before,
  .divider-success:after {
    --tw-bg-opacity: 1;
    background-color: var(--fallback-su,oklch(var(--su)/var(--tw-bg-opacity)))
}
  .divider-warning:before,
  .divider-warning:after {
    --tw-bg-opacity: 1;
    background-color: var(--fallback-wa,oklch(var(--wa)/var(--tw-bg-opacity)))
}
  .divider-info:before,
  .divider-info:after {
    --tw-bg-opacity: 1;
    background-color: var(--fallback-in,oklch(var(--in)/var(--tw-bg-opacity)))
}
  .divider-error:before,
  .divider-error:after {
    --tw-bg-opacity: 1;
    background-color: var(--fallback-er,oklch(var(--er)/var(--tw-bg-opacity)))
}
.drawer {
  width: 100%;
}
  .drawer-side > .drawer-overlay {
  cursor: pointer;
  background-color: transparent;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 200ms;
  transition-timing-function: cubic-bezier(0, 0, 0.2, 1);
}
  .drawer-toggle:checked ~ .drawer-side > .drawer-overlay {
        background-color: #0006;
      }
  .drawer-toggle:focus-visible ~ .drawer-content label.drawer-button {
  outline-style: solid;
  outline-width: 2px;
  outline-offset: 2px;
}
.dropdown:is(:not(details)) .dropdown-content {
  transform-origin: top;
  --tw-scale-x: .95;
  --tw-scale-y: .95;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 200ms;
  transition-timing-function: cubic-bezier(0, 0, 0.2, 1)
}
.dropdown-bottom .dropdown-content {
  transform-origin: top
}
.dropdown-top .dropdown-content {
  transform-origin: bottom
}
.dropdown-left .dropdown-content {
  transform-origin: right
}
.dropdown-right .dropdown-content {
  transform-origin: left
}
.dropdown.dropdown-open .dropdown-content,
.dropdown:focus .dropdown-content,
.dropdown:focus-within .dropdown-content {
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))
}
@media (hover: hover) {
  .dropdown.dropdown-hover:hover .dropdown-content {
    --tw-scale-x: 1;
    --tw-scale-y: 1;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))
  }
}
.file-input {
    overflow: hidden;
    border-radius: var(--rounded-btn, 0.5rem);
    border-width: 1px;
    border-color: var(--fallback-bc,oklch(var(--bc)/var(--tw-border-opacity)));
    --tw-border-opacity: 0;
    --tw-bg-opacity: 1;
    background-color: var(--fallback-b1,oklch(var(--b1)/var(--tw-bg-opacity)));
    font-size: 1rem;
    line-height: 1.5rem;
}
  .file-input::file-selector-button {
    border-style: solid;
    --tw-border-opacity: 1;
    border-color: var(--fallback-n,oklch(var(--n)/var(--tw-border-opacity)));
    --tw-bg-opacity: 1;
    background-color: var(--fallback-n,oklch(var(--n)/var(--tw-bg-opacity)));
    font-weight: 600;
    text-transform: uppercase;
    --tw-text-opacity: 1;
    color: var(--fallback-nc,oklch(var(--nc)/var(--tw-text-opacity)));
    text-decoration-line: none;
    border-width: var(--border-btn, 1px);
    animation: button-pop var(--animation-btn, 0.25s) ease-out;
  }
  .file-input-bordered {
    --tw-border-opacity: 0.2;
}
  .file-input:focus {
    outline-style: solid;
    outline-width: 2px;
    outline-offset: 2px;
    outline-color: var(--fallback-bc,oklch(var(--bc)/0.2));
}
  .file-input-ghost {
    --tw-bg-opacity: 0.05;
}
  .file-input-ghost:focus {
    --tw-bg-opacity: 1;
    --tw-text-opacity: 1;
    color: var(--fallback-bc,oklch(var(--bc)/var(--tw-text-opacity)));
      box-shadow: none;
}
  .file-input-ghost::file-selector-button {
    border-width: 1px;
    border-color: transparent;
    background-color: transparent;
    color: currentColor;
}
  .file-input-primary {
    --tw-border-opacity: 1;
    border-color: var(--fallback-p,oklch(var(--p)/var(--tw-border-opacity)));
}
  .file-input-primary:focus {
    outline-color: var(--fallback-p,oklch(var(--p)/1));
}
  .file-input-primary::file-selector-button {
    --tw-border-opacity: 1;
    border-color: var(--fallback-p,oklch(var(--p)/var(--tw-border-opacity)));
    --tw-bg-opacity: 1;
    background-color: var(--fallback-p,oklch(var(--p)/var(--tw-bg-opacity)));
    --tw-text-opacity: 1;
    color: var(--fallback-pc,oklch(var(--pc)/var(--tw-text-opacity)));
}
  .file-input-secondary {
    --tw-border-opacity: 1;
    border-color: var(--fallback-s,oklch(var(--s)/var(--tw-border-opacity)));
}
  .file-input-secondary:focus {
    outline-color: var(--fallback-s,oklch(var(--s)/1));
}
  .file-input-secondary::file-selector-button {
    --tw-border-opacity: 1;
    border-color: var(--fallback-s,oklch(var(--s)/var(--tw-border-opacity)));
    --tw-bg-opacity: 1;
    background-color: var(--fallback-s,oklch(var(--s)/var(--tw-bg-opacity)));
    --tw-text-opacity: 1;
    color: var(--fallback-sc,oklch(var(--sc)/var(--tw-text-opacity)));
}
  .file-input-accent {
    --tw-border-opacity: 1;
    border-color: var(--fallback-a,oklch(var(--a)/var(--tw-border-opacity)));
}
  .file-input-accent:focus {
    outline-color: var(--fallback-a,oklch(var(--a)/1));
}
  .file-input-accent::file-selector-button {
    --tw-border-opacity: 1;
    border-color: var(--fallback-a,oklch(var(--a)/var(--tw-border-opacity)));
    --tw-bg-opacity: 1;
    background-color: var(--fallback-a,oklch(var(--a)/var(--tw-bg-opacity)));
    --tw-text-opacity: 1;
    color: var(--fallback-ac,oklch(var(--ac)/var(--tw-text-opacity)));
}
  .file-input-info {
    --tw-border-opacity: 1;
    border-color: var(--fallback-in,oklch(var(--in)/var(--tw-border-opacity)));
}
  .file-input-info:focus {
    outline-color: var(--fallback-in,oklch(var(--in)/1));
}
  .file-input-info::file-selector-button {
    --tw-border-opacity: 1;
    border-color: var(--fallback-in,oklch(var(--in)/var(--tw-border-opacity)));
    --tw-bg-opacity: 1;
    background-color: var(--fallback-in,oklch(var(--in)/var(--tw-bg-opacity)));
    --tw-text-opacity: 1;
    color: var(--fallback-inc,oklch(var(--inc)/var(--tw-text-opacity)));
}
  .file-input-success {
    --tw-border-opacity: 1;
    border-color: var(--fallback-su,oklch(var(--su)/var(--tw-border-opacity)));
}
  .file-input-success:focus {
    outline-color: var(--fallback-su,oklch(var(--su)/1));
}
  .file-input-success::file-selector-button {
    --tw-border-opacity: 1;
    border-color: var(--fallback-su,oklch(var(--su)/var(--tw-border-opacity)));
    --tw-bg-opacity: 1;
    background-color: var(--fallback-su,oklch(var(--su)/var(--tw-bg-opacity)));
    --tw-text-opacity: 1;
    color: var(--fallback-suc,oklch(var(--suc)/var(--tw-text-opacity)));
}
  .file-input-warning {
    --tw-border-opacity: 1;
    border-color: var(--fallback-wa,oklch(var(--wa)/var(--tw-border-opacity)));
}
  .file-input-warning:focus {
    outline-color: var(--fallback-wa,oklch(var(--wa)/1));
}
  .file-input-warning::file-selector-button {
    --tw-border-opacity: 1;
    border-color: var(--fallback-wa,oklch(var(--wa)/var(--tw-border-opacity)));
    --tw-bg-opacity: 1;
    background-color: var(--fallback-wa,oklch(var(--wa)/var(--tw-bg-opacity)));
    --tw-text-opacity: 1;
    color: var(--fallback-wac,oklch(var(--wac)/var(--tw-text-opacity)));
}
  .file-input-error {
    --tw-border-opacity: 1;
    border-color: var(--fallback-er,oklch(var(--er)/var(--tw-border-opacity)));
}
  .file-input-error:focus {
    outline-color: var(--fallback-er,oklch(var(--er)/1));
}
  .file-input-error::file-selector-button {
    --tw-border-opacity: 1;
    border-color: var(--fallback-er,oklch(var(--er)/var(--tw-border-opacity)));
    --tw-bg-opacity: 1;
    background-color: var(--fallback-er,oklch(var(--er)/var(--tw-bg-opacity)));
    --tw-text-opacity: 1;
    color: var(--fallback-erc,oklch(var(--erc)/var(--tw-text-opacity)));
}
  .file-input-disabled,
  .file-input[disabled] {
    cursor: not-allowed;
    --tw-border-opacity: 1;
    border-color: var(--fallback-b2,oklch(var(--b2)/var(--tw-border-opacity)));
    --tw-bg-opacity: 1;
    background-color: var(--fallback-b2,oklch(var(--b2)/var(--tw-bg-opacity)));
    --tw-text-opacity: 0.2;
}
  .file-input-disabled::placeholder,
  .file-input[disabled]::placeholder {
    color: var(--fallback-bc,oklch(var(--bc)/var(--tw-placeholder-opacity)));
    --tw-placeholder-opacity: 0.2;
}
  .file-input-disabled::file-selector-button, .file-input[disabled]::file-selector-button {
    --tw-border-opacity: 0;
    background-color: var(--fallback-n,oklch(var(--n)/var(--tw-bg-opacity)));
    --tw-bg-opacity: 0.2;
    color: var(--fallback-bc,oklch(var(--bc)/var(--tw-text-opacity)));
    --tw-text-opacity: 0.2;
}
.footer {
    column-gap: 1rem;
    row-gap: 2.5rem;
    font-size: 0.875rem;
    line-height: 1.25rem
}
  .footer > * {
    gap: 0.5rem
}
  .footer-title {
    margin-bottom: 0.5rem;
    font-weight: 700;
    text-transform: uppercase;
    opacity: 0.6
}
.label {
    padding-left: 0.25rem;
    padding-right: 0.25rem;
    padding-top: 0.5rem;
    padding-bottom: 0.5rem
}
  .label-text {
    font-size: 0.875rem;
    line-height: 1.25rem;
    --tw-text-opacity: 1;
    color: var(--fallback-bc,oklch(var(--bc)/var(--tw-text-opacity)))
}
  .label-text-alt {
    font-size: 0.75rem;
    line-height: 1rem;
    --tw-text-opacity: 1;
    color: var(--fallback-bc,oklch(var(--bc)/var(--tw-text-opacity)))
}
  @media(hover:hover) {
    .label a:hover {
        --tw-text-opacity: 1;
        color: var(--fallback-bc,oklch(var(--bc)/var(--tw-text-opacity)))
    }
}
.hero-overlay {
    background-color: var(--fallback-n,oklch(var(--n)/var(--tw-bg-opacity)));
    --tw-bg-opacity: 0.5
}
  .hero-content {
    max-width: 80rem;
    gap: 1rem;
    padding: 1rem
}
.input {
    border-radius: var(--rounded-btn, 0.5rem);
    border-width: 1px;
    border-color: transparent;
    --tw-bg-opacity: 1;
    background-color: var(--fallback-b1,oklch(var(--b1)/var(--tw-bg-opacity)));
    font-size: 1rem;
    line-height: 1.5rem;
}
  .input input {
    --tw-bg-opacity: 1;
    background-color: var(--fallback-p,oklch(var(--p)/var(--tw-bg-opacity)));
    background-color: transparent;
}
  .input input:focus {
    outline: 2px solid transparent;
    outline-offset: 2px;
}
  .input[list]::-webkit-calendar-picker-indicator {
    line-height: 1em;
  }
  .input-bordered {
    border-color: var(--fallback-bc,oklch(var(--bc)/0.2));
}
  .input:focus,
  .input:focus-within {
    box-shadow: none;
    border-color: var(--fallback-bc,oklch(var(--bc)/0.2));
    outline-style: solid;
    outline-width: 2px;
    outline-offset: 2px;
    outline-color: var(--fallback-bc,oklch(var(--bc)/0.2));
  }
  .input-ghost {
    --tw-bg-opacity: 0.05;
}
  .input-ghost:focus,
    .input-ghost:focus-within {
    --tw-bg-opacity: 1;
    --tw-text-opacity: 1;
    color: var(--fallback-bc,oklch(var(--bc)/var(--tw-text-opacity)));
      box-shadow: none;
}
  .input-primary {
    --tw-border-opacity: 1;
    border-color: var(--fallback-p,oklch(var(--p)/var(--tw-border-opacity)));
}
  .input-primary:focus,
    .input-primary:focus-within {
    --tw-border-opacity: 1;
    border-color: var(--fallback-p,oklch(var(--p)/var(--tw-border-opacity)));
    outline-color: var(--fallback-p,oklch(var(--p)/1));
}
  .input-secondary {
    --tw-border-opacity: 1;
    border-color: var(--fallback-s,oklch(var(--s)/var(--tw-border-opacity)));
}
  .input-secondary:focus,
    .input-secondary:focus-within {
    --tw-border-opacity: 1;
    border-color: var(--fallback-s,oklch(var(--s)/var(--tw-border-opacity)));
    outline-color: var(--fallback-s,oklch(var(--s)/1));
}
  .input-accent {
    --tw-border-opacity: 1;
    border-color: var(--fallback-a,oklch(var(--a)/var(--tw-border-opacity)));
}
  .input-accent:focus,
    .input-accent:focus-within {
    --tw-border-opacity: 1;
    border-color: var(--fallback-a,oklch(var(--a)/var(--tw-border-opacity)));
    outline-color: var(--fallback-a,oklch(var(--a)/1));
}
  .input-info {
    --tw-border-opacity: 1;
    border-color: var(--fallback-in,oklch(var(--in)/var(--tw-border-opacity)));
}
  .input-info:focus,
    .input-info:focus-within {
    --tw-border-opacity: 1;
    border-color: var(--fallback-in,oklch(var(--in)/var(--tw-border-opacity)));
    outline-color: var(--fallback-in,oklch(var(--in)/1));
}
  .input-success {
    --tw-border-opacity: 1;
    border-color: var(--fallback-su,oklch(var(--su)/var(--tw-border-opacity)));
}
  .input-success:focus,
    .input-success:focus-within {
    --tw-border-opacity: 1;
    border-color: var(--fallback-su,oklch(var(--su)/var(--tw-border-opacity)));
    outline-color: var(--fallback-su,oklch(var(--su)/1));
}
  .input-warning {
    --tw-border-opacity: 1;
    border-color: var(--fallback-wa,oklch(var(--wa)/var(--tw-border-opacity)));
}
  .input-warning:focus,
    .input-warning:focus-within {
    --tw-border-opacity: 1;
    border-color: var(--fallback-wa,oklch(var(--wa)/var(--tw-border-opacity)));
    outline-color: var(--fallback-wa,oklch(var(--wa)/1));
}
  .input-error {
    --tw-border-opacity: 1;
    border-color: var(--fallback-er,oklch(var(--er)/var(--tw-border-opacity)));
}
  .input-error:focus,
    .input-error:focus-within {
    --tw-border-opacity: 1;
    border-color: var(--fallback-er,oklch(var(--er)/var(--tw-border-opacity)));
    outline-color: var(--fallback-er,oklch(var(--er)/1));
}
  .input:has(> input[disabled]),
  .input-disabled,
  .input:disabled,
  .input[disabled] {
    cursor: not-allowed;
    --tw-border-opacity: 1;
    border-color: var(--fallback-b2,oklch(var(--b2)/var(--tw-border-opacity)));
    --tw-bg-opacity: 1;
    background-color: var(--fallback-b2,oklch(var(--b2)/var(--tw-bg-opacity)));
    color: var(--fallback-bc,oklch(var(--bc)/0.4));
}
  .input:has(> input[disabled])::placeholder,
  .input-disabled::placeholder,
  .input:disabled::placeholder,
  .input[disabled]::placeholder {
    color: var(--fallback-bc,oklch(var(--bc)/var(--tw-placeholder-opacity)));
    --tw-placeholder-opacity: 0.2;
}
  .input:has(> input[disabled]) > input[disabled] {
    cursor: not-allowed;
}
  /* &::-webkit-calendar-picker-indicator {
    display: none;
  } */
  .input::-webkit-date-and-time-value {
    text-align: inherit;
  }
.join {
    border-radius: var(--rounded-btn, 0.5rem)
}
  .join > :where(*:not(:first-child)) {
    margin-top: 0px;
    margin-bottom: 0px;
    margin-inline-start: -1px
}
  .join > :where(*:not(:first-child)):is(.btn) {
    margin-inline-start: calc(var(--border-btn) * -1)
}
  .join-item:focus {
    isolation: isolate
}
.kbd {
  border-radius: var(--rounded-btn, 0.5rem);
  border-width: 1px;
  border-color: var(--fallback-bc,oklch(var(--bc)/var(--tw-border-opacity)));
  --tw-border-opacity: 0.2;
  --tw-bg-opacity: 1;
  background-color: var(--fallback-b2,oklch(var(--b2)/var(--tw-bg-opacity)));
  padding-left: 0.5rem;
  padding-right: 0.5rem;
  border-bottom-width: 2px;
  min-height: 2.2em;
  min-width: 2.2em
}
.link-primary {
    --tw-text-opacity: 1;
    color: var(--fallback-p,oklch(var(--p)/var(--tw-text-opacity)));
}
@supports(color:color-mix(in oklab,black,black)) {
    @media(hover:hover) {
        .link-primary:hover {
            color: color-mix(in oklab,var(--fallback-p,oklch(var(--p)/1)) 80%,black);
        }
    }
}
  .link-secondary {
    --tw-text-opacity: 1;
    color: var(--fallback-s,oklch(var(--s)/var(--tw-text-opacity)));
}
  @supports(color:color-mix(in oklab,black,black)) {
    @media(hover:hover) {
        .link-secondary:hover {
            color: color-mix(in oklab,var(--fallback-s,oklch(var(--s)/1)) 80%,black);
        }
    }
}
  .link-accent {
    --tw-text-opacity: 1;
    color: var(--fallback-a,oklch(var(--a)/var(--tw-text-opacity)));
}
  @supports(color:color-mix(in oklab,black,black)) {
    @media(hover:hover) {
        .link-accent:hover {
            color: color-mix(in oklab,var(--fallback-a,oklch(var(--a)/1)) 80%,black);
        }
    }
}
  .link-neutral {
    --tw-text-opacity: 1;
    color: var(--fallback-n,oklch(var(--n)/var(--tw-text-opacity)));
}
  @supports(color:color-mix(in oklab,black,black)) {
    @media(hover:hover) {
        .link-neutral:hover {
            color: color-mix(in oklab,var(--fallback-n,oklch(var(--n)/1)) 80%,black);
        }
    }
}
  .link-success {
    --tw-text-opacity: 1;
    color: var(--fallback-su,oklch(var(--su)/var(--tw-text-opacity)));
}
  @supports(color:color-mix(in oklab,black,black)) {
    @media(hover:hover) {
        .link-success:hover {
            color: color-mix(in oklab,var(--fallback-su,oklch(var(--su)/1)) 80%,black);
        }
    }
}
  .link-info {
    --tw-text-opacity: 1;
    color: var(--fallback-in,oklch(var(--in)/var(--tw-text-opacity)));
}
  @supports(color:color-mix(in oklab,black,black)) {
    @media(hover:hover) {
        .link-info:hover {
            color: color-mix(in oklab,var(--fallback-in,oklch(var(--in)/1)) 80%,black);
        }
    }
}
  .link-warning {
    --tw-text-opacity: 1;
    color: var(--fallback-wa,oklch(var(--wa)/var(--tw-text-opacity)));
}
  @supports(color:color-mix(in oklab,black,black)) {
    @media(hover:hover) {
        .link-warning:hover {
            color: color-mix(in oklab,var(--fallback-wa,oklch(var(--wa)/1)) 80%,black);
        }
    }
}
  .link-error {
    --tw-text-opacity: 1;
    color: var(--fallback-er,oklch(var(--er)/var(--tw-text-opacity)));
}
  @supports(color:color-mix(in oklab,black,black)) {
    @media(hover:hover) {
        .link-error:hover {
            color: color-mix(in oklab,var(--fallback-er,oklch(var(--er)/1)) 80%,black);
        }
    }
}
  .link:focus {
    outline: 2px solid transparent;
    outline-offset: 2px;
}
  .link:focus-visible {
    outline: 2px solid currentColor;
    outline-offset: 2px;
  }
.loading {
  pointer-events: none;
  display: inline-block;
  aspect-ratio: 1 / 1;
  width: 1.5rem;
  background-color: currentColor;
  mask-size: 100%;
  mask-repeat: no-repeat;
  mask-position: center;
  mask-image: url("data:image/svg+xml,%3Csvg width='24' height='24' stroke='%23000' viewBox='0 0 24 24' xmlns='http://www.w3.org/2000/svg'%3E%3Cstyle%3E.spinner_V8m1%7Btransform-origin:center;animation:spinner_zKoa 2s linear infinite%7D.spinner_V8m1 circle%7Bstroke-linecap:round;animation:spinner_YpZS 1.5s ease-out infinite%7D%40keyframes spinner_zKoa%7B100%25%7Btransform:rotate(360deg)%7D%7D%40keyframes spinner_YpZS%7B0%25%7Bstroke-dasharray:0 150;stroke-dashoffset:0%7D47.5%25%7Bstroke-dasharray:42 150;stroke-dashoffset:-16%7D95%25%2C100%25%7Bstroke-dasharray:42 150;stroke-dashoffset:-59%7D%7D%3C%2Fstyle%3E%3Cg class='spinner_V8m1'%3E%3Ccircle cx='12' cy='12' r='9.5' fill='none' stroke-width='3'%3E%3C%2Fcircle%3E%3C%2Fg%3E%3C%2Fsvg%3E");
}
.loading-spinner {
  mask-image: url("data:image/svg+xml,%3Csvg width='24' height='24' stroke='%23000' viewBox='0 0 24 24' xmlns='http://www.w3.org/2000/svg'%3E%3Cstyle%3E.spinner_V8m1%7Btransform-origin:center;animation:spinner_zKoa 2s linear infinite%7D.spinner_V8m1 circle%7Bstroke-linecap:round;animation:spinner_YpZS 1.5s ease-out infinite%7D%40keyframes spinner_zKoa%7B100%25%7Btransform:rotate(360deg)%7D%7D%40keyframes spinner_YpZS%7B0%25%7Bstroke-dasharray:0 150;stroke-dashoffset:0%7D47.5%25%7Bstroke-dasharray:42 150;stroke-dashoffset:-16%7D95%25%2C100%25%7Bstroke-dasharray:42 150;stroke-dashoffset:-59%7D%7D%3C%2Fstyle%3E%3Cg class='spinner_V8m1'%3E%3Ccircle cx='12' cy='12' r='9.5' fill='none' stroke-width='3'%3E%3C%2Fcircle%3E%3C%2Fg%3E%3C%2Fsvg%3E");
}
.loading-dots {
  mask-image: url("data:image/svg+xml,%3Csvg width='24' height='24' viewBox='0 0 24 24' xmlns='http://www.w3.org/2000/svg'%3E%3Cstyle%3E.spinner_qM83%7Banimation:spinner_8HQG 1.05s infinite%7D.spinner_oXPr%7Banimation-delay:.1s%7D.spinner_ZTLf%7Banimation-delay:.2s%7D@keyframes spinner_8HQG%7B0%25,57.14%25%7Banimation-timing-function:cubic-bezier(0.33,.66,.66,1);transform:translate(0)%7D28.57%25%7Banimation-timing-function:cubic-bezier(0.33,0,.66,.33);transform:translateY(-6px)%7D100%25%7Btransform:translate(0)%7D%7D%3C/style%3E%3Ccircle class='spinner_qM83' cx='4' cy='12' r='3'/%3E%3Ccircle class='spinner_qM83 spinner_oXPr' cx='12' cy='12' r='3'/%3E%3Ccircle class='spinner_qM83 spinner_ZTLf' cx='20' cy='12' r='3'/%3E%3C/svg%3E");
}
.loading-ring {
  mask-image: url("data:image/svg+xml,%3Csvg width='44' height='44' viewBox='0 0 44 44' xmlns='http://www.w3.org/2000/svg' stroke='%23fff'%3E%3Cg fill='none' fill-rule='evenodd' stroke-width='2'%3E%3Ccircle cx='22' cy='22' r='1'%3E%3Canimate attributeName='r' begin='0s' dur='1.8s' values='1; 20' calcMode='spline' keyTimes='0; 1' keySplines='0.165, 0.84, 0.44, 1' repeatCount='indefinite' /%3E%3Canimate attributeName='stroke-opacity' begin='0s' dur='1.8s' values='1; 0' calcMode='spline' keyTimes='0; 1' keySplines='0.3, 0.61, 0.355, 1' repeatCount='indefinite' /%3E%3C/circle%3E%3Ccircle cx='22' cy='22' r='1'%3E%3Canimate attributeName='r' begin='-0.9s' dur='1.8s' values='1; 20' calcMode='spline' keyTimes='0; 1' keySplines='0.165, 0.84, 0.44, 1' repeatCount='indefinite' /%3E%3Canimate attributeName='stroke-opacity' begin='-0.9s' dur='1.8s' values='1; 0' calcMode='spline' keyTimes='0; 1' keySplines='0.3, 0.61, 0.355, 1' repeatCount='indefinite' /%3E%3C/circle%3E%3C/g%3E%3C/svg%3E");
}
.loading-ball {
  mask-image: url("data:image/svg+xml,%0A%3Csvg width='24' height='24' viewBox='0 0 24 24' xmlns='http://www.w3.org/2000/svg'%3E%3Cstyle%3E.spinner_rXNP%7Banimation:spinner_YeBj .8s infinite%7D@keyframes spinner_YeBj%7B0%25%7Banimation-timing-function:cubic-bezier(0.33,0,.66,.33);cy:5px%7D46.875%25%7Bcy:20px;rx:4px;ry:4px%7D50%25%7Banimation-timing-function:cubic-bezier(0.33,.66,.66,1);cy:20.5px;rx:4.8px;ry:3px%7D53.125%25%7Brx:4px;ry:4px%7D100%25%7Bcy:5px%7D%7D%3C/style%3E%3Cellipse class='spinner_rXNP' cx='12' cy='5' rx='4' ry='4'/%3E%3C/svg%3E");
}
.loading-bars {
  mask-image: url("data:image/svg+xml,%0A%3Csvg width='24' height='24' viewBox='0 0 24 24' xmlns='http://www.w3.org/2000/svg'%3E%3Cstyle%3E.spinner_hzlK%7Banimation:spinner_vc4H .8s linear infinite;animation-delay:-.8s%7D.spinner_koGT%7Banimation-delay:-.65s%7D.spinner_YF1u%7Banimation-delay:-.5s%7D@keyframes spinner_vc4H%7B0%25%7By:1px;height:22px%7D93.75%25%7By:5px;height:14px;opacity:.2%7D%7D%3C/style%3E%3Crect class='spinner_hzlK' x='1' y='1' width='6' height='22'/%3E%3Crect class='spinner_hzlK spinner_koGT' x='9' y='1' width='6' height='22'/%3E%3Crect class='spinner_hzlK spinner_YF1u' x='17' y='1' width='6' height='22'/%3E%3C/svg%3E");
}
.loading-infinity {
  mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' style='shape-rendering: auto;' width='200px' height='200px' viewBox='0 0 100 100' preserveAspectRatio='xMidYMid'%3E%3Cpath fill='none' stroke='%230a0a0a' stroke-width='10' stroke-dasharray='205.271142578125 51.317785644531256' d='M24.3 30C11.4 30 5 43.3 5 50s6.4 20 19.3 20c19.3 0 32.1-40 51.4-40 C88.6 30 95 43.3 95 50s-6.4 20-19.3 20C56.4 70 43.6 30 24.3 30z' stroke-linecap='round' style='transform:scale(0.8);transform-origin:50px 50px'%3E%3Canimate attributeName='stroke-dashoffset' repeatCount='indefinite' dur='2s' keyTimes='0;1' values='0;256.58892822265625'%3E%3C/animate%3E%3C/path%3E%3C/svg%3E");
}
.loading-xs {
  width: 1rem;
}
.loading-sm {
  width: 1.25rem;
}
.loading-md {
  width: 1.5rem;
}
.loading-lg {
  width: 2.5rem;
}
.mask-squircle {
    mask-image: url("data:image/svg+xml,%3csvg width='200' height='200' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='M100 0C20 0 0 20 0 100s20 100 100 100 100-20 100-100S180 0 100 0Z'/%3e%3c/svg%3e");
  }
  .mask-decagon {
    mask-image: url("data:image/svg+xml,%3csvg width='192' height='200' xmlns='http://www.w3.org/2000/svg'%3e%3cpath fill='black' d='m96 0 58.779 19.098 36.327 50v61.804l-36.327 50L96 200l-58.779-19.098-36.327-50V69.098l36.327-50z' fill-rule='evenodd'/%3e%3c/svg%3e");
  }
  .mask-diamond {
    mask-image: url("data:image/svg+xml,%3csvg width='200' height='200' xmlns='http://www.w3.org/2000/svg'%3e%3cpath fill='black' d='m100 0 100 100-100 100L0 100z' fill-rule='evenodd'/%3e%3c/svg%3e");
  }
  .mask-heart {
    mask-image: url("data:image/svg+xml,%3csvg width='200' height='185' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='M100 184.606a15.384 15.384 0 0 1-8.653-2.678C53.565 156.28 37.205 138.695 28.182 127.7 8.952 104.264-.254 80.202.005 54.146.308 24.287 24.264 0 53.406 0c21.192 0 35.869 11.937 44.416 21.879a2.884 2.884 0 0 0 4.356 0C110.725 11.927 125.402 0 146.594 0c29.142 0 53.098 24.287 53.4 54.151.26 26.061-8.956 50.122-28.176 73.554-9.023 10.994-25.383 28.58-63.165 54.228a15.384 15.384 0 0 1-8.653 2.673Z' fill='black' fill-rule='nonzero'/%3e%3c/svg%3e");
  }
  .mask-hexagon {
    mask-image: url("data:image/svg+xml,%3csvg width='182' height='201' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='M.3 65.486c0-9.196 6.687-20.063 14.211-25.078l61.86-35.946c8.36-5.016 20.899-5.016 29.258 0l61.86 35.946c8.36 5.015 14.211 15.882 14.211 25.078v71.055c0 9.196-6.687 20.063-14.211 25.079l-61.86 35.945c-8.36 4.18-20.899 4.18-29.258 0L14.51 161.62C6.151 157.44.3 145.737.3 136.54V65.486Z' fill='black' fill-rule='nonzero'/%3e%3c/svg%3e");
  }
  .mask-hexagon-2 {
    mask-image: url("data:image/svg+xml,%3csvg width='200' height='182' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='M64.786 181.4c-9.196 0-20.063-6.687-25.079-14.21L3.762 105.33c-5.016-8.36-5.016-20.9 0-29.259l35.945-61.86C44.723 5.851 55.59 0 64.786 0h71.055c9.196 0 20.063 6.688 25.079 14.211l35.945 61.86c4.18 8.36 4.18 20.899 0 29.258l-35.945 61.86c-4.18 8.36-15.883 14.211-25.079 14.211H64.786Z' fill='black' fill-rule='nonzero'/%3e%3c/svg%3e");
  }
  .mask-circle {
    mask-image: url("data:image/svg+xml,%3csvg width='200' height='200' xmlns='http://www.w3.org/2000/svg'%3e%3ccircle fill='black' cx='100' cy='100' r='100' fill-rule='evenodd'/%3e%3c/svg%3e");
  }
  .mask-parallelogram {
    mask-image: url("data:image/svg+xml,%3csvg width='200' height='154' xmlns='http://www.w3.org/2000/svg'%3e%3cpath fill='black' d='M46.154 0H200l-46.154 153.846H0z' fill-rule='evenodd'/%3e%3c/svg%3e");
  }
  .mask-parallelogram-2 {
    mask-image: url("data:image/svg+xml,%3csvg width='200' height='154' xmlns='http://www.w3.org/2000/svg'%3e%3cpath fill='black' d='M153.846 0H0l46.154 153.846H200z' fill-rule='evenodd'/%3e%3c/svg%3e");
  }
  .mask-parallelogram-3 {
    mask-image: url("data:image/svg+xml,%3csvg width='154' height='201' xmlns='http://www.w3.org/2000/svg'%3e%3cpath fill='black' d='M.077 47.077v153.846l153.846-46.154V.923z' fill-rule='evenodd'/%3e%3c/svg%3e");
  }
  .mask-parallelogram-4 {
    mask-image: url("data:image/svg+xml,%3csvg width='154' height='201' xmlns='http://www.w3.org/2000/svg'%3e%3cpath fill='black' d='M153.923 47.077v153.846L.077 154.77V.923z' fill-rule='evenodd'/%3e%3c/svg%3e");
  }
  .mask-pentagon {
    mask-image: url("data:image/svg+xml,%3csvg width='192' height='181' xmlns='http://www.w3.org/2000/svg'%3e%3cpath fill='black' d='m96 0 95.106 69.098-36.327 111.804H37.22L.894 69.098z' fill-rule='evenodd'/%3e%3c/svg%3e");
  }
  .mask-square {
    mask-image: url("data:image/svg+xml,%3csvg width='200' height='200' xmlns='http://www.w3.org/2000/svg'%3e%3cpath fill='black' d='M0 0h200v200H0z' fill-rule='evenodd'/%3e%3c/svg%3e");
  }
  .mask-star {
    mask-image: url("data:image/svg+xml,%3csvg width='192' height='180' xmlns='http://www.w3.org/2000/svg'%3e%3cpath fill='black' d='m96 137.263-58.779 42.024 22.163-68.389L.894 68.481l72.476-.243L96 0l22.63 68.238 72.476.243-58.49 42.417 22.163 68.389z' fill-rule='evenodd'/%3e%3c/svg%3e");
  }
  .mask-star-2 {
    mask-image: url("data:image/svg+xml,%3csvg width='192' height='180' xmlns='http://www.w3.org/2000/svg'%3e%3cpath fill='black' d='m96 153.044-58.779 26.243 7.02-63.513L.894 68.481l63.117-13.01L96 0l31.989 55.472 63.117 13.01-43.347 47.292 7.02 63.513z' fill-rule='evenodd'/%3e%3c/svg%3e");
  }
  .mask-triangle {
    mask-image: url("data:image/svg+xml,%3csvg width='174' height='149' xmlns='http://www.w3.org/2000/svg'%3e%3cpath fill='black' d='m87 148.476-86.603.185L43.86 74.423 87 0l43.14 74.423 43.463 74.238z' fill-rule='evenodd'/%3e%3c/svg%3e");
  }
  .mask-triangle-2 {
    mask-image: url("data:image/svg+xml,%3csvg width='174' height='150' xmlns='http://www.w3.org/2000/svg'%3e%3cpath fill='black' d='m87 .738 86.603-.184-43.463 74.238L87 149.214 43.86 74.792.397.554z' fill-rule='evenodd'/%3e%3c/svg%3e");
  }
  .mask-triangle-3 {
    mask-image: url("data:image/svg+xml,%3csvg width='150' height='174' xmlns='http://www.w3.org/2000/svg'%3e%3cpath fill='black' d='m149.369 87.107.185 86.603-74.239-43.463L.893 87.107l74.422-43.14L149.554.505z' fill-rule='evenodd'/%3e%3c/svg%3e");
  }
  .mask-triangle-4 {
    mask-image: url("data:image/svg+xml,%3csvg width='150' height='174' xmlns='http://www.w3.org/2000/svg'%3e%3cpath fill='black' d='M.631 87.107.446.505l74.239 43.462 74.422 43.14-74.422 43.14L.446 173.71z' fill-rule='evenodd'/%3e%3c/svg%3e");
  }
.menu {
  padding: 0.5rem;
}
:where(.menu li:empty) {
  --tw-bg-opacity: 1;
  background-color: var(--fallback-bc,oklch(var(--bc)/var(--tw-bg-opacity)));
  opacity: 0.1;
  margin: 0.5rem 1rem;
  height: 1px;
}
.menu :where(li ul) {
  margin-inline-start: 1rem;
  padding-inline-start: 0.5rem;
}
.menu :where(li ul):before {
  position: absolute;
  bottom: 0.75rem;
  inset-inline-start: 0px;
  top: 0.75rem;
  width: 1px;
  --tw-bg-opacity: 1;
  background-color: var(--fallback-bc,oklch(var(--bc)/var(--tw-bg-opacity)));
  opacity: 0.1;
    content: "";
}
.menu :where(li:not(.menu-title) > *:not(ul, details, .menu-title, .btn)),
.menu :where(li:not(.menu-title) > details > summary:not(.menu-title)) {
  border-radius: var(--rounded-btn, 0.5rem);
  padding-left: 1rem;
  padding-right: 1rem;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  text-align: start;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 200ms;
  transition-timing-function: cubic-bezier(0, 0, 0.2, 1);
  text-wrap: balance;
}
:where(.menu li:not(.menu-title, .disabled) > *:not(ul, details, .menu-title)):not(summary, .active, .btn).focus, :where(.menu li:not(.menu-title, .disabled) > *:not(ul, details, .menu-title)):not(summary, .active, .btn):focus, :where(.menu li:not(.menu-title, .disabled) > *:not(ul, details, .menu-title)):is(summary):not(.active, .btn):focus-visible, :where(.menu li:not(.menu-title, .disabled) > details > summary:not(.menu-title)):not(summary, .active, .btn).focus, :where(.menu li:not(.menu-title, .disabled) > details > summary:not(.menu-title)):not(summary, .active, .btn):focus, :where(.menu li:not(.menu-title, .disabled) > details > summary:not(.menu-title)):is(summary):not(.active, .btn):focus-visible {
  cursor: pointer;
  background-color: var(--fallback-bc,oklch(var(--bc)/0.1));
  --tw-text-opacity: 1;
  color: var(--fallback-bc,oklch(var(--bc)/var(--tw-text-opacity)));
  outline: 2px solid transparent;
  outline-offset: 2px;
}
@media (hover: hover) {
    :where(.menu li:not(.menu-title, .disabled) > *:not(ul, details, .menu-title)):not(.active, .btn):hover, :where(.menu li:not(.menu-title, .disabled) > details > summary:not(.menu-title)):not(.active, .btn):hover {
    cursor: pointer;
    outline: 2px solid transparent;
    outline-offset: 2px;
  }
      @supports (color: oklch(0% 0 0)) {
    :where(.menu li:not(.menu-title, .disabled) > *:not(ul, details, .menu-title)):not(.active, .btn):hover, :where(.menu li:not(.menu-title, .disabled) > details > summary:not(.menu-title)):not(.active, .btn):hover {
      background-color: var(--fallback-bc,oklch(var(--bc)/0.1));
    }
      }
  }
.menu li > *:not(ul, .menu-title, details, .btn):active,
.menu li > *:not(ul, .menu-title, details, .btn).active,
.menu li > details > summary:active {
  --tw-bg-opacity: 1;
  background-color: var(--fallback-n,oklch(var(--n)/var(--tw-bg-opacity)));
  --tw-text-opacity: 1;
  color: var(--fallback-nc,oklch(var(--nc)/var(--tw-text-opacity)));
}
@media(hover:hover) {
  .menu li > *:not(ul, .menu-title, details, .btn):active,
.menu li > *:not(ul, .menu-title, details, .btn).active,
.menu li > details > summary:active {
    --tw-bg-opacity: 1;
    background-color: var(--fallback-n,oklch(var(--n)/var(--tw-bg-opacity)));
    --tw-text-opacity: 1;
    color: var(--fallback-nc,oklch(var(--nc)/var(--tw-text-opacity)));
  }
}
.menu li.disabled {
  color: var(--fallback-bc,oklch(var(--bc)/0.3));
}
.menu :where(li > details > summary)::-webkit-details-marker {
  display: none;
}
.menu :where(li > details > summary):after,
.menu :where(li > .menu-dropdown-toggle):after {
  justify-self: end;
  display: block;
  margin-top: -0.5rem;
  height: 0.5rem;
  width: 0.5rem;
  transform: rotate(45deg);
  transition-property: transform, margin-top;
  transition-duration: 0.3s;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  content: "";
  transform-origin: 75% 75%;
  box-shadow: 2px 2px;
  pointer-events: none;
}
.menu :where(li > details[open] > summary):after,
.menu :where(li > .menu-dropdown-toggle.menu-dropdown-show):after {
  transform: rotate(225deg);
  margin-top: 0;
}
.menu-title {
  padding-left: 1rem;
  padding-right: 1rem;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
  font-weight: 700;
  color: var(--fallback-bc,oklch(var(--bc)/0.4));
}
.mockup-code {
    min-width: 18rem;
    border-radius: var(--rounded-box, 1rem);
    --tw-bg-opacity: 1;
    background-color: var(--fallback-n,oklch(var(--n)/var(--tw-bg-opacity)));
    padding-top: 1.25rem;
    padding-bottom: 1.25rem;
    --tw-text-opacity: 1;
    color: var(--fallback-nc,oklch(var(--nc)/var(--tw-text-opacity)));
    direction: ltr;
}
    .mockup-code:before {
      content: "";
      margin-bottom: 1rem;
      display: block;
      height: 0.75rem;
      width: 0.75rem;
      border-radius: 9999px;
      opacity: 0.3;
      box-shadow:
        1.4em 0,
        2.8em 0,
        4.2em 0;
    }
    .mockup-code pre {
    padding-right: 1.25rem;
}
    .mockup-code pre:before {
        content: "";
        margin-right: 2ch;
      }
    .mockup-code pre[data-prefix]:before {
        content: attr(data-prefix);
        width: 2rem;
        opacity: 0.5;
      }
  .mockup-window {
    display: flex;
    flex-direction: column;
    border-radius: var(--rounded-box, 1rem);
    padding-top: 1.25rem;
}
  .mockup-window:before {
      content: "";
      margin-bottom: 1rem;
      display: block;
      aspect-ratio: 1 / 1;
      height: 0.75rem;
      flex-shrink: 0;
      align-self: flex-start;
      border-radius: 9999px;
      opacity: 0.3;
    }
  .mockup-window:where([dir="rtl"], [dir="rtl"] *):before {
    align-self: flex-end;
}
  .mockup-window:before {
      box-shadow:
        1.4em 0,
        2.8em 0,
        4.2em 0;
    }
  .mockup-phone {
    display: inline-block;
    border: 4px solid #444;
    border-radius: 50px;
    background-color: #000;
    padding: 10px;
    margin: 0 auto;
    overflow: hidden;
  }
  .mockup-phone .camera {
      position: relative;
      top: 0px;
      left: 0px;
      background: #000;
      height: 25px;
      width: 150px;
      margin: 0 auto;
      border-bottom-left-radius: 17px;
      border-bottom-right-radius: 17px;
      z-index: 11;
    }
  .mockup-phone .camera:before {
        content: "";
        position: absolute;
        top: 35%;
        left: 50%;
        width: 50px;
        height: 4px;
        border-radius: 5px;
        background-color: #0c0b0e;
        transform: translate(-50%, -50%);
      }
  .mockup-phone .camera:after {
        content: "";
        position: absolute;
        top: 20%;
        left: 70%;
        width: 8px;
        height: 8px;
        border-radius: 5px;
        background-color: #0f0b25;
      }
  .mockup-phone .display {
      overflow: hidden;
      border-radius: 40px;
      margin-top: -25px;
    }
  .mockup-browser {
    border-radius: var(--rounded-box, 1rem);
}
  .mockup-browser .mockup-browser-toolbar {
    margin-top: 0.75rem;
    margin-bottom: 0.75rem;
    display: inline-flex;
    width: 100%;
    align-items: center;
    padding-right: 1.4em;
}
  .mockup-browser .mockup-browser-toolbar:where([dir="rtl"], [dir="rtl"] *) {
    flex-direction: row-reverse;
}
  .mockup-browser .mockup-browser-toolbar:before {
        content: "";
        margin-right: 4.8rem;
        display: inline-block;
        aspect-ratio: 1 / 1;
        height: 0.75rem;
        border-radius: 9999px;
        opacity: 0.3;
        box-shadow:
          1.4em 0,
          2.8em 0,
          4.2em 0;
      }
  .mockup-browser .mockup-browser-toolbar .input {
    position: relative;
    margin-left: auto;
    margin-right: auto;
    display: block;
    height: 1.75rem;
    width: 24rem;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    --tw-bg-opacity: 1;
    background-color: var(--fallback-b2,oklch(var(--b2)/var(--tw-bg-opacity)));
    padding-left: 2rem;
        direction: ltr;
}
  .mockup-browser .mockup-browser-toolbar .input:before {
          content: "";
          position: absolute;
          left: 0.5rem;
          top: 50%;
          aspect-ratio: 1 / 1;
          height: 0.75rem;
          --tw-translate-y: -50%;
          transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
          border-radius: 9999px;
          border-width: 2px;
          border-color: currentColor;
          opacity: 0.6;
        }
  .mockup-browser .mockup-browser-toolbar .input:after {
          content: "";
          position: absolute;
          left: 1.25rem;
          top: 50%;
          height: 0.5rem;
          --tw-translate-y: 25%;
          --tw-rotate: -45deg;
          transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
          border-radius: 9999px;
          border-width: 1px;
          border-color: currentColor;
          opacity: 0.6;
        }
.modal {
  background-color: transparent;
  color: inherit;
  transition-duration: 200ms;
  transition-timing-function: cubic-bezier(0, 0, 0.2, 1);
  transition-property: transform, opacity, visibility;
  overflow-y: hidden;
  overscroll-behavior: contain;
}
  .modal:not(dialog:not(.modal-open)),
  .modal::backdrop {
    background-color: #0006;
    animation: modal-pop 0.2s ease-out;
  }
.modal-backdrop {
  z-index: -1;
  grid-column-start: 1;
  grid-row-start: 1;
  display: grid;
  align-self: stretch;
  justify-self: stretch;
  color: transparent;
}
.modal-box {
  grid-column-start: 1;
  grid-row-start: 1;
  width: 91.666667%;
  max-width: 32rem;
  --tw-scale-x: .9;
  --tw-scale-y: .9;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  border-bottom-right-radius: var(--rounded-box, 1rem);
  border-bottom-left-radius: var(--rounded-box, 1rem);
  border-top-left-radius: var(--rounded-box, 1rem);
  border-top-right-radius: var(--rounded-box, 1rem);
  --tw-bg-opacity: 1;
  background-color: var(--fallback-b1,oklch(var(--b1)/var(--tw-bg-opacity)));
  padding: 1.5rem;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 200ms;
  transition-timing-function: cubic-bezier(0, 0, 0.2, 1);
  box-shadow: rgba(0, 0, 0, 0.25) 0px 25px 50px -12px;
  overflow-y: auto;
  overscroll-behavior: contain;
}
.modal-open .modal-box,
.modal-toggle:checked + .modal .modal-box,
.modal:target .modal-box,
.modal[open] .modal-box {
  --tw-translate-y: 0px;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.modal-action {
  margin-top: 1.5rem;
  justify-content: flex-end;
}
.modal-action > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(0.5rem * var(--tw-space-x-reverse));
  margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));
}
@keyframes modal-pop {
  0% {
    opacity: 0;
  }
}
.navbar {
  padding: var(--navbar-padding, 0.5rem);
  min-height: 4rem;
  width: 100%;
}
.progress {
    height: 0.5rem;
    border-radius: var(--rounded-box, 1rem);
    background-color: var(--fallback-bc,oklch(var(--bc)/0.2));
}
  .progress::-moz-progress-bar {
    border-radius: var(--rounded-box, 1rem);
    --tw-bg-opacity: 1;
    background-color: var(--fallback-bc,oklch(var(--bc)/var(--tw-bg-opacity)));
}
  .progress-primary::-moz-progress-bar {
    border-radius: var(--rounded-box, 1rem);
    --tw-bg-opacity: 1;
    background-color: var(--fallback-p,oklch(var(--p)/var(--tw-bg-opacity)));
}
  .progress-secondary::-moz-progress-bar {
    border-radius: var(--rounded-box, 1rem);
    --tw-bg-opacity: 1;
    background-color: var(--fallback-s,oklch(var(--s)/var(--tw-bg-opacity)));
}
  .progress-accent::-moz-progress-bar {
    border-radius: var(--rounded-box, 1rem);
    --tw-bg-opacity: 1;
    background-color: var(--fallback-a,oklch(var(--a)/var(--tw-bg-opacity)));
}
  .progress-info::-moz-progress-bar {
    border-radius: var(--rounded-box, 1rem);
    --tw-bg-opacity: 1;
    background-color: var(--fallback-in,oklch(var(--in)/var(--tw-bg-opacity)));
}
  .progress-success::-moz-progress-bar {
    border-radius: var(--rounded-box, 1rem);
    --tw-bg-opacity: 1;
    background-color: var(--fallback-su,oklch(var(--su)/var(--tw-bg-opacity)));
}
  .progress-warning::-moz-progress-bar {
    border-radius: var(--rounded-box, 1rem);
    --tw-bg-opacity: 1;
    background-color: var(--fallback-wa,oklch(var(--wa)/var(--tw-bg-opacity)));
}
  .progress-error::-moz-progress-bar {
    border-radius: var(--rounded-box, 1rem);
    --tw-bg-opacity: 1;
    background-color: var(--fallback-er,oklch(var(--er)/var(--tw-bg-opacity)));
}
  .progress:indeterminate {
    --progress-color: var(--fallback-bc,oklch(var(--bc)/1));
  }
  .progress-primary:indeterminate {
    --progress-color: var(--fallback-p,oklch(var(--p)/1));
  }
  .progress-secondary:indeterminate {
    --progress-color: var(--fallback-s,oklch(var(--s)/1));
  }
  .progress-accent:indeterminate {
    --progress-color: var(--fallback-a,oklch(var(--a)/1));
  }
  .progress-info:indeterminate {
    --progress-color: var(--fallback-in,oklch(var(--in)/1));
  }
  .progress-success:indeterminate {
    --progress-color: var(--fallback-su,oklch(var(--su)/1));
  }
  .progress-warning:indeterminate {
    --progress-color: var(--fallback-wa,oklch(var(--wa)/1));
  }
  .progress-error:indeterminate {
    --progress-color: var(--fallback-er,oklch(var(--er)/1));
  }
  .progress::-webkit-progress-bar {
    border-radius: var(--rounded-box, 1rem);
    background-color: transparent;
}
  .progress::-webkit-progress-value {
    border-radius: var(--rounded-box, 1rem);
    --tw-bg-opacity: 1;
    background-color: var(--fallback-bc,oklch(var(--bc)/var(--tw-bg-opacity)));
}
  .progress-primary::-webkit-progress-value {
    --tw-bg-opacity: 1;
    background-color: var(--fallback-p,oklch(var(--p)/var(--tw-bg-opacity)));
}
  .progress-secondary::-webkit-progress-value {
    --tw-bg-opacity: 1;
    background-color: var(--fallback-s,oklch(var(--s)/var(--tw-bg-opacity)));
}
  .progress-accent::-webkit-progress-value {
    --tw-bg-opacity: 1;
    background-color: var(--fallback-a,oklch(var(--a)/var(--tw-bg-opacity)));
}
  .progress-info::-webkit-progress-value {
    --tw-bg-opacity: 1;
    background-color: var(--fallback-in,oklch(var(--in)/var(--tw-bg-opacity)));
}
  .progress-success::-webkit-progress-value {
    --tw-bg-opacity: 1;
    background-color: var(--fallback-su,oklch(var(--su)/var(--tw-bg-opacity)));
}
  .progress-warning::-webkit-progress-value {
    --tw-bg-opacity: 1;
    background-color: var(--fallback-wa,oklch(var(--wa)/var(--tw-bg-opacity)));
}
  .progress-error::-webkit-progress-value {
    --tw-bg-opacity: 1;
    background-color: var(--fallback-er,oklch(var(--er)/var(--tw-bg-opacity)));
}

.progress:indeterminate {
  background-image: repeating-linear-gradient(
    90deg,
    var(--progress-color) -1%,
    var(--progress-color) 10%,
    transparent 10%,
    transparent 90%
  );
  background-size: 200%;
  background-position-x: 15%;
  animation: progress-loading 5s ease-in-out infinite;
}

.progress:indeterminate::-moz-progress-bar {
    background-color: transparent;
  background-image: repeating-linear-gradient(
    90deg,
    var(--progress-color) -1%,
    var(--progress-color) 10%,
    transparent 10%,
    transparent 90%
  );
  background-size: 200%;
  background-position-x: 15%;
  animation: progress-loading 5s ease-in-out infinite;
}

@keyframes progress-loading {
  50% {
    background-position-x: -115%;
  }
}
.radial-progress {
  --value: 0;
  --size: 5rem;
  --thickness: calc(var(--size) / 10);
}
.radial-progress:after {
  background-color: currentColor;
}
.radio {
  --chkbg: var(--bc);
  height: 1.5rem;
  width: 1.5rem;
  cursor: pointer;
  appearance: none;
  border-radius: 9999px;
  border-width: 1px;
  border-color: var(--fallback-bc,oklch(var(--bc)/var(--tw-border-opacity)));
  --tw-border-opacity: 0.2;
}
  .radio:focus {
    box-shadow: none;
  }
  .radio:focus-visible {
  outline-style: solid;
  outline-width: 2px;
  outline-offset: 2px;
  outline-color: var(--fallback-bc,oklch(var(--bc)/1));
}
  .radio:checked,
  .radio[aria-checked="true"] {
  --tw-bg-opacity: 1;
  background-color: var(--fallback-bc,oklch(var(--bc)/var(--tw-bg-opacity)));
    background-image: none;
    animation: radiomark var(--animation-input, 0.2s) ease-out;
    box-shadow:
      0 0 0 4px var(--fallback-b1,oklch(var(--b1)/1)) inset,
      0 0 0 4px var(--fallback-b1,oklch(var(--b1)/1)) inset;
}
  .radio-primary {
    --chkbg: var(--p);
    --tw-border-opacity: 1;
    border-color: var(--fallback-p,oklch(var(--p)/var(--tw-border-opacity)));
  }
  @media(hover:hover) {
  .radio-primary:hover {
    --tw-border-opacity: 1;
    border-color: var(--fallback-p,oklch(var(--p)/var(--tw-border-opacity)));
  }
}
  .radio-primary:focus-visible {
  outline-color: var(--fallback-p,oklch(var(--p)/1));
}
  .radio-primary:checked,
    .radio-primary[aria-checked="true"] {
  --tw-border-opacity: 1;
  border-color: var(--fallback-p,oklch(var(--p)/var(--tw-border-opacity)));
  --tw-bg-opacity: 1;
  background-color: var(--fallback-p,oklch(var(--p)/var(--tw-bg-opacity)));
  --tw-text-opacity: 1;
  color: var(--fallback-pc,oklch(var(--pc)/var(--tw-text-opacity)));
}
  .radio-secondary {
    --chkbg: var(--s);
    --tw-border-opacity: 1;
    border-color: var(--fallback-s,oklch(var(--s)/var(--tw-border-opacity)));
  }
  @media(hover:hover) {
  .radio-secondary:hover {
    --tw-border-opacity: 1;
    border-color: var(--fallback-s,oklch(var(--s)/var(--tw-border-opacity)));
  }
}
  .radio-secondary:focus-visible {
  outline-color: var(--fallback-s,oklch(var(--s)/1));
}
  .radio-secondary:checked,
    .radio-secondary[aria-checked="true"] {
  --tw-border-opacity: 1;
  border-color: var(--fallback-s,oklch(var(--s)/var(--tw-border-opacity)));
  --tw-bg-opacity: 1;
  background-color: var(--fallback-s,oklch(var(--s)/var(--tw-bg-opacity)));
  --tw-text-opacity: 1;
  color: var(--fallback-sc,oklch(var(--sc)/var(--tw-text-opacity)));
}
  .radio-accent {
    --chkbg: var(--a);
    --tw-border-opacity: 1;
    border-color: var(--fallback-a,oklch(var(--a)/var(--tw-border-opacity)));
  }
  @media(hover:hover) {
  .radio-accent:hover {
    --tw-border-opacity: 1;
    border-color: var(--fallback-a,oklch(var(--a)/var(--tw-border-opacity)));
  }
}
  .radio-accent:focus-visible {
  outline-color: var(--fallback-a,oklch(var(--a)/1));
}
  .radio-accent:checked,
    .radio-accent[aria-checked="true"] {
  --tw-border-opacity: 1;
  border-color: var(--fallback-a,oklch(var(--a)/var(--tw-border-opacity)));
  --tw-bg-opacity: 1;
  background-color: var(--fallback-a,oklch(var(--a)/var(--tw-bg-opacity)));
  --tw-text-opacity: 1;
  color: var(--fallback-ac,oklch(var(--ac)/var(--tw-text-opacity)));
}
  .radio-success {
    --chkbg: var(--su);
    --tw-border-opacity: 1;
    border-color: var(--fallback-su,oklch(var(--su)/var(--tw-border-opacity)));
  }
  @media(hover:hover) {
  .radio-success:hover {
    --tw-border-opacity: 1;
    border-color: var(--fallback-su,oklch(var(--su)/var(--tw-border-opacity)));
  }
}
  .radio-success:focus-visible {
  outline-color: var(--fallback-su,oklch(var(--su)/1));
}
  .radio-success:checked,
    .radio-success[aria-checked="true"] {
  --tw-border-opacity: 1;
  border-color: var(--fallback-su,oklch(var(--su)/var(--tw-border-opacity)));
  --tw-bg-opacity: 1;
  background-color: var(--fallback-su,oklch(var(--su)/var(--tw-bg-opacity)));
  --tw-text-opacity: 1;
  color: var(--fallback-suc,oklch(var(--suc)/var(--tw-text-opacity)));
}
  .radio-warning {
    --chkbg: var(--wa);
    --tw-border-opacity: 1;
    border-color: var(--fallback-wa,oklch(var(--wa)/var(--tw-border-opacity)));
  }
  @media(hover:hover) {
  .radio-warning:hover {
    --tw-border-opacity: 1;
    border-color: var(--fallback-wa,oklch(var(--wa)/var(--tw-border-opacity)));
  }
}
  .radio-warning:focus-visible {
  outline-color: var(--fallback-wa,oklch(var(--wa)/1));
}
  .radio-warning:checked,
    .radio-warning[aria-checked="true"] {
  --tw-border-opacity: 1;
  border-color: var(--fallback-wa,oklch(var(--wa)/var(--tw-border-opacity)));
  --tw-bg-opacity: 1;
  background-color: var(--fallback-wa,oklch(var(--wa)/var(--tw-bg-opacity)));
  --tw-text-opacity: 1;
  color: var(--fallback-wac,oklch(var(--wac)/var(--tw-text-opacity)));
}
  .radio-info {
    --chkbg: var(--in);
    --tw-border-opacity: 1;
    border-color: var(--fallback-in,oklch(var(--in)/var(--tw-border-opacity)));
  }
  @media(hover:hover) {
  .radio-info:hover {
    --tw-border-opacity: 1;
    border-color: var(--fallback-in,oklch(var(--in)/var(--tw-border-opacity)));
  }
}
  .radio-info:focus-visible {
  outline-color: var(--fallback-in,oklch(var(--in)/1));
}
  .radio-info:checked,
    .radio-info[aria-checked="true"] {
  --tw-border-opacity: 1;
  border-color: var(--fallback-in,oklch(var(--in)/var(--tw-border-opacity)));
  --tw-bg-opacity: 1;
  background-color: var(--fallback-in,oklch(var(--in)/var(--tw-bg-opacity)));
  --tw-text-opacity: 1;
  color: var(--fallback-inc,oklch(var(--inc)/var(--tw-text-opacity)));
}
  .radio-error {
    --chkbg: var(--er);
    --tw-border-opacity: 1;
    border-color: var(--fallback-er,oklch(var(--er)/var(--tw-border-opacity)));
  }
  @media(hover:hover) {
  .radio-error:hover {
    --tw-border-opacity: 1;
    border-color: var(--fallback-er,oklch(var(--er)/var(--tw-border-opacity)));
  }
}
  .radio-error:focus-visible {
  outline-color: var(--fallback-er,oklch(var(--er)/1));
}
  .radio-error:checked,
    .radio-error[aria-checked="true"] {
  --tw-border-opacity: 1;
  border-color: var(--fallback-er,oklch(var(--er)/var(--tw-border-opacity)));
  --tw-bg-opacity: 1;
  background-color: var(--fallback-er,oklch(var(--er)/var(--tw-bg-opacity)));
  --tw-text-opacity: 1;
  color: var(--fallback-erc,oklch(var(--erc)/var(--tw-text-opacity)));
}
  .radio:disabled {
  cursor: not-allowed;
  opacity: 0.2;
}

@keyframes radiomark {
  0% {
    box-shadow:
      0 0 0 12px var(--fallback-b1,oklch(var(--b1)/1)) inset,
      0 0 0 12px var(--fallback-b1,oklch(var(--b1)/1)) inset;
  }
  50% {
    box-shadow:
      0 0 0 3px var(--fallback-b1,oklch(var(--b1)/1)) inset,
      0 0 0 3px var(--fallback-b1,oklch(var(--b1)/1)) inset;
  }
  100% {
    box-shadow:
      0 0 0 4px var(--fallback-b1,oklch(var(--b1)/1)) inset,
      0 0 0 4px var(--fallback-b1,oklch(var(--b1)/1)) inset;
  }
}

/* backward compatibility */

.radio-mark {
  display: none;
}
.range {
  appearance: none;
  -webkit-appearance: none;
  --range-shdw: var(--fallback-bc,oklch(var(--bc)/1));
  overflow: hidden;
  border-radius: var(--rounded-box, 1rem);
  background-color: transparent;
}
  .range:focus-visible::-webkit-slider-thumb {
    --focus-shadow: 0 0 0 6px var(--fallback-b1,oklch(var(--b1)/1)) inset, 0 0 0 2rem var(--range-shdw) inset;
  }
  .range:focus-visible::-moz-range-thumb {
    --focus-shadow: 0 0 0 6px var(--fallback-b1,oklch(var(--b1)/1)) inset, 0 0 0 2rem var(--range-shdw) inset;
  }
  .range::-webkit-slider-runnable-track {
  height: 0.5rem;
  width: 100%;
  border-radius: var(--rounded-box, 1rem);
  background-color: var(--fallback-bc,oklch(var(--bc)/0.1));
}
  .range::-moz-range-track {
  height: 0.5rem;
  width: 100%;
  border-radius: var(--rounded-box, 1rem);
  background-color: var(--fallback-bc,oklch(var(--bc)/0.1));
}
  .range::-webkit-slider-thumb {
  position: relative;
  height: 1.5rem;
  width: 1.5rem;
  border-radius: var(--rounded-box, 1rem);
  border-style: none;
  --tw-bg-opacity: 1;
  background-color: var(--fallback-b1,oklch(var(--b1)/var(--tw-bg-opacity)));
    appearance: none;
    -webkit-appearance: none;
    top: 50%;
    color: var(--range-shdw);
    transform: translateY(-50%);
    --filler-size: 100rem;
    --filler-offset: 0.6rem;
    box-shadow:
      0 0 0 3px var(--range-shdw) inset,
      var(--focus-shadow, 0 0),
      calc(var(--filler-size) * -1 - var(--filler-offset)) 0 0 var(--filler-size);
}
  .range::-moz-range-thumb {
  position: relative;
  height: 1.5rem;
  width: 1.5rem;
  border-radius: var(--rounded-box, 1rem);
  border-style: none;
  --tw-bg-opacity: 1;
  background-color: var(--fallback-b1,oklch(var(--b1)/var(--tw-bg-opacity)));
    top: 50%;
    color: var(--range-shdw);
    --filler-size: 100rem;
    --filler-offset: 0.5rem;
    box-shadow:
      0 0 0 3px var(--range-shdw) inset,
      var(--focus-shadow, 0 0),
      calc(var(--filler-size) * -1 - var(--filler-offset)) 0 0 var(--filler-size);
}
  .range-primary {
    --range-shdw: var(--fallback-p,oklch(var(--p)/1));
  }
  .range-secondary {
    --range-shdw: var(--fallback-s,oklch(var(--s)/1));
  }
  .range-accent {
    --range-shdw: var(--fallback-a,oklch(var(--a)/1));
  }
  .range-success {
    --range-shdw: var(--fallback-su,oklch(var(--su)/1));
  }
  .range-warning {
    --range-shdw: var(--fallback-wa,oklch(var(--wa)/1));
  }
  .range-info {
    --range-shdw: var(--fallback-in,oklch(var(--in)/1));
  }
  .range-error {
    --range-shdw: var(--fallback-er,oklch(var(--er)/1));
  }
.rating input {
    appearance: none;
    -webkit-appearance: none;
}
  .rating :where(input) {
    animation: rating-pop var(--animation-input, 0.25s) ease-out;
    height: 1.5rem;
    width: 1.5rem;
    background-color: var(--fallback-bc,oklch(var(--bc)/var(--tw-bg-opacity)));
    --tw-bg-opacity: 1;
  }
  .rating .rating-hidden {
    width: 0.5rem;
    background-color: transparent;
}
  .rating input[type="radio"]:checked {
    background-image: none;
  }
  .rating input:checked ~ input,
  .rating input[aria-checked="true"] ~ input {
    --tw-bg-opacity: 0.2;
}
  .rating input:focus-visible {
    transition-property: transform;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 300ms;
    transition-timing-function: cubic-bezier(0, 0, 0.2, 1);
    transform: translateY(-0.125em);
}
  .rating input:active:focus {
    animation: none;
    transform: translateY(-0.125em);
  }
.rating-half :where(input:not(.rating-hidden)) {
    width: 0.75rem;
}
@keyframes rating-pop {
  0% {
    transform: translateY(-0.125em);
  }
  40% {
    transform: translateY(-0.125em);
  }
  100% {
    transform: translateY(0);
  }
}
.select {
  border-radius: var(--rounded-btn, 0.5rem);
  border-width: 1px;
  border-color: transparent;
  --tw-bg-opacity: 1;
  background-color: var(--fallback-b1,oklch(var(--b1)/var(--tw-bg-opacity)));
  padding-inline-end: 2.5rem;
}
  .select-bordered {
  border-color: var(--fallback-bc,oklch(var(--bc)/0.2));
}
  .select {
  background-image: linear-gradient(45deg, transparent 50%, currentColor 50%),
    linear-gradient(135deg, currentColor 50%, transparent 50%);
  background-position:
    calc(100% - 20px) calc(1px + 50%),
    calc(100% - 16.1px) calc(1px + 50%);
  background-size:
    4px 4px,
    4px 4px;
  background-repeat: no-repeat;
}
  .select:focus {
    box-shadow: none;
    border-color: var(--fallback-bc,oklch(var(--bc)/0.2));
    outline-style: solid;
    outline-width: 2px;
    outline-offset: 2px;
    outline-color: var(--fallback-bc,oklch(var(--bc)/0.2));
  }
  .select-ghost {
  --tw-bg-opacity: 0.05;
}
  .select-ghost:focus {
  --tw-bg-opacity: 1;
  --tw-text-opacity: 1;
  color: var(--fallback-bc,oklch(var(--bc)/var(--tw-text-opacity)));
}
  .select-primary {
  --tw-border-opacity: 1;
  border-color: var(--fallback-p,oklch(var(--p)/var(--tw-border-opacity)));
}
  .select-primary:focus {
  --tw-border-opacity: 1;
  border-color: var(--fallback-p,oklch(var(--p)/var(--tw-border-opacity)));
  outline-color: var(--fallback-p,oklch(var(--p)/1));
}
  .select-secondary {
  --tw-border-opacity: 1;
  border-color: var(--fallback-s,oklch(var(--s)/var(--tw-border-opacity)));
}
  .select-secondary:focus {
  --tw-border-opacity: 1;
  border-color: var(--fallback-s,oklch(var(--s)/var(--tw-border-opacity)));
  outline-color: var(--fallback-s,oklch(var(--s)/1));
}
  .select-accent {
  --tw-border-opacity: 1;
  border-color: var(--fallback-a,oklch(var(--a)/var(--tw-border-opacity)));
}
  .select-accent:focus {
  --tw-border-opacity: 1;
  border-color: var(--fallback-a,oklch(var(--a)/var(--tw-border-opacity)));
  outline-color: var(--fallback-a,oklch(var(--a)/1));
}
  .select-info {
  --tw-border-opacity: 1;
  border-color: var(--fallback-in,oklch(var(--in)/var(--tw-border-opacity)));
}
  .select-info:focus {
  --tw-border-opacity: 1;
  border-color: var(--fallback-in,oklch(var(--in)/var(--tw-border-opacity)));
  outline-color: var(--fallback-in,oklch(var(--in)/1));
}
  .select-success {
  --tw-border-opacity: 1;
  border-color: var(--fallback-su,oklch(var(--su)/var(--tw-border-opacity)));
}
  .select-success:focus {
  --tw-border-opacity: 1;
  border-color: var(--fallback-su,oklch(var(--su)/var(--tw-border-opacity)));
  outline-color: var(--fallback-su,oklch(var(--su)/1));
}
  .select-warning {
  --tw-border-opacity: 1;
  border-color: var(--fallback-wa,oklch(var(--wa)/var(--tw-border-opacity)));
}
  .select-warning:focus {
  --tw-border-opacity: 1;
  border-color: var(--fallback-wa,oklch(var(--wa)/var(--tw-border-opacity)));
  outline-color: var(--fallback-wa,oklch(var(--wa)/1));
}
  .select-error {
  --tw-border-opacity: 1;
  border-color: var(--fallback-er,oklch(var(--er)/var(--tw-border-opacity)));
}
  .select-error:focus {
  --tw-border-opacity: 1;
  border-color: var(--fallback-er,oklch(var(--er)/var(--tw-border-opacity)));
  outline-color: var(--fallback-er,oklch(var(--er)/1));
}
  .select-disabled,
  .select:disabled,
  .select[disabled] {
  cursor: not-allowed;
  --tw-border-opacity: 1;
  border-color: var(--fallback-b2,oklch(var(--b2)/var(--tw-border-opacity)));
  --tw-bg-opacity: 1;
  background-color: var(--fallback-b2,oklch(var(--b2)/var(--tw-bg-opacity)));
  color: var(--fallback-bc,oklch(var(--bc)/0.4));
}
  .select-disabled::placeholder,
  .select:disabled::placeholder,
  .select[disabled]::placeholder {
  color: var(--fallback-bc,oklch(var(--bc)/var(--tw-placeholder-opacity)));
  --tw-placeholder-opacity: 0.2;
}
  .select-multiple,
  .select[multiple],
  .select[size].select:not([size="1"]) {
  background-image: none;
  padding-right: 1rem;
}

[dir="rtl"] .select {
  background-position:
    calc(0% + 12px) calc(1px + 50%),
    calc(0% + 16px) calc(1px + 50%);
}
.skeleton {
  border-radius: var(--rounded-box, 1rem);
  --tw-bg-opacity: 1;
  background-color: var(--fallback-b3,oklch(var(--b3)/var(--tw-bg-opacity)));
  will-change: background-position;
  animation: skeleton 1.8s ease-in-out infinite;
  background-image: linear-gradient(
    105deg,
    transparent 0%,
    transparent 40%,
    var(--fallback-b1,oklch(var(--b1)/1)) 50%,
    transparent 60%,
    transparent 100%
  );
  background-size: 200% auto;
  background-repeat: no-repeat;
  background-position-x: -50%;
}
@media (prefers-reduced-motion) {
  .skeleton {
    animation-duration: 15s;
  }
}
@keyframes skeleton {
  from {
    background-position: 150%;
  }
  to {
    background-position: -50%;
  }
}
.stack {
    place-items: center;
    align-items: flex-end
}
  .stack > * {
    width: 100%;
    opacity: 0.6
}
  .stack > *:nth-child(2) {
    opacity: 0.8
}
  .stack > *:nth-child(1) {
    opacity: 1
}
.stats {
  border-radius: var(--rounded-box, 1rem);
  --tw-bg-opacity: 1;
  background-color: var(--fallback-b1,oklch(var(--b1)/var(--tw-bg-opacity)));
  --tw-text-opacity: 1;
  color: var(--fallback-bc,oklch(var(--bc)/var(--tw-text-opacity)));
}
:where(.stats) > :not([hidden]) ~ :not([hidden]) {
  --tw-divide-x-reverse: 0;
  border-right-width: calc(1px * var(--tw-divide-x-reverse));
  border-left-width: calc(1px * calc(1 - var(--tw-divide-x-reverse)));
  --tw-divide-y-reverse: 0;
  border-top-width: calc(0px * calc(1 - var(--tw-divide-y-reverse)));
  border-bottom-width: calc(0px * var(--tw-divide-y-reverse));
}
:where(.stats) {
  overflow-x: auto;
}
:is([dir="rtl"] .stats > :not([hidden]) ~ :not([hidden])) {
  --tw-divide-x-reverse: 1;
}
.stat {
  column-gap: 1rem;
  border-color: var(--fallback-bc,oklch(var(--bc)/var(--tw-border-opacity)));
  --tw-border-opacity: 0.1;
  padding-left: 1.5rem;
  padding-right: 1.5rem;
  padding-top: 1rem;
  padding-bottom: 1rem;
}
.stat-title {
  color: var(--fallback-bc,oklch(var(--bc)/0.6));
}
.stat-value {
  font-size: 2.25rem;
  line-height: 2.5rem;
  font-weight: 800;
}
.stat-desc {
  font-size: 0.75rem;
  line-height: 1rem;
  color: var(--fallback-bc,oklch(var(--bc)/0.6));
}
.stat-actions {
  margin-top: 1rem;
}
/* .stat + .stat {
  @apply border-l border-base-content border-opacity-10;
}
.stats.grid-flow-row {
  .stat + .stat {
    @apply border-l-0 border-t;
  }
} */
.steps {
  /* &-vertical{
    .step {
      gap: .5rem;
      grid-template-columns: 40px 1fr;
      grid-template-rows: auto;
      min-height: 4rem;
      justify-items: start;
      &:before {
        @apply w-2 h-full top-0 transform -translate-y-1/2 -translate-x-1/2;
        margin-left: 50%;
      }
    }
  } */
}
  /* &,
  &-horizontal{ */
  .steps .step {
    grid-template-rows: 40px 1fr;
    grid-template-columns: auto;
    min-width: 4rem;
  }
  .steps .step:before {
  top: 0px;
  grid-column-start: 1;
  grid-row-start: 1;
  height: 0.5rem;
  width: 100%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  --tw-bg-opacity: 1;
  background-color: var(--fallback-b3,oklch(var(--b3)/var(--tw-bg-opacity)));
  --tw-text-opacity: 1;
  color: var(--fallback-bc,oklch(var(--bc)/var(--tw-text-opacity)));
      content: "";
      margin-inline-start: -100%;
}
  /* } */
  .steps .step:after {
      content: counter(step);
      counter-increment: step;
      z-index: 1;
      position: relative;
      grid-column-start: 1;
      grid-row-start: 1;
      display: grid;
      height: 2rem;
      width: 2rem;
      place-items: center;
      place-self: center;
      border-radius: 9999px;
      --tw-bg-opacity: 1;
      background-color: var(--fallback-b3,oklch(var(--b3)/var(--tw-bg-opacity)));
      --tw-text-opacity: 1;
      color: var(--fallback-bc,oklch(var(--bc)/var(--tw-text-opacity)));
    }
  .steps .step:first-child:before {
      content: none;
    }
  .steps .step[data-content]:after {
      content: attr(data-content);
    }
  .steps .step-neutral + .step-neutral:before,
  .steps .step-neutral:after {
  --tw-bg-opacity: 1;
  background-color: var(--fallback-n,oklch(var(--n)/var(--tw-bg-opacity)));
  --tw-text-opacity: 1;
  color: var(--fallback-nc,oklch(var(--nc)/var(--tw-text-opacity)));
}
  .steps .step-primary + .step-primary:before,
  .steps .step-primary:after {
  --tw-bg-opacity: 1;
  background-color: var(--fallback-p,oklch(var(--p)/var(--tw-bg-opacity)));
  --tw-text-opacity: 1;
  color: var(--fallback-pc,oklch(var(--pc)/var(--tw-text-opacity)));
}
  .steps .step-secondary + .step-secondary:before,
  .steps .step-secondary:after {
  --tw-bg-opacity: 1;
  background-color: var(--fallback-s,oklch(var(--s)/var(--tw-bg-opacity)));
  --tw-text-opacity: 1;
  color: var(--fallback-sc,oklch(var(--sc)/var(--tw-text-opacity)));
}
  .steps .step-accent + .step-accent:before,
  .steps .step-accent:after {
  --tw-bg-opacity: 1;
  background-color: var(--fallback-a,oklch(var(--a)/var(--tw-bg-opacity)));
  --tw-text-opacity: 1;
  color: var(--fallback-ac,oklch(var(--ac)/var(--tw-text-opacity)));
}
  .steps .step-info + .step-info:before {
  --tw-bg-opacity: 1;
  background-color: var(--fallback-in,oklch(var(--in)/var(--tw-bg-opacity)));
}
  .steps .step-info:after {
  --tw-bg-opacity: 1;
  background-color: var(--fallback-in,oklch(var(--in)/var(--tw-bg-opacity)));
  --tw-text-opacity: 1;
  color: var(--fallback-inc,oklch(var(--inc)/var(--tw-text-opacity)));
}
  .steps .step-success + .step-success:before {
  --tw-bg-opacity: 1;
  background-color: var(--fallback-su,oklch(var(--su)/var(--tw-bg-opacity)));
}
  .steps .step-success:after {
  --tw-bg-opacity: 1;
  background-color: var(--fallback-su,oklch(var(--su)/var(--tw-bg-opacity)));
  --tw-text-opacity: 1;
  color: var(--fallback-suc,oklch(var(--suc)/var(--tw-text-opacity)));
}
  .steps .step-warning + .step-warning:before {
  --tw-bg-opacity: 1;
  background-color: var(--fallback-wa,oklch(var(--wa)/var(--tw-bg-opacity)));
}
  .steps .step-warning:after {
  --tw-bg-opacity: 1;
  background-color: var(--fallback-wa,oklch(var(--wa)/var(--tw-bg-opacity)));
  --tw-text-opacity: 1;
  color: var(--fallback-wac,oklch(var(--wac)/var(--tw-text-opacity)));
}
  .steps .step-error + .step-error:before {
  --tw-bg-opacity: 1;
  background-color: var(--fallback-er,oklch(var(--er)/var(--tw-bg-opacity)));
}
  .steps .step-error:after {
  --tw-bg-opacity: 1;
  background-color: var(--fallback-er,oklch(var(--er)/var(--tw-bg-opacity)));
  --tw-text-opacity: 1;
  color: var(--fallback-erc,oklch(var(--erc)/var(--tw-text-opacity)));
}
.swap {
  cursor: pointer;
}

.swap > * {
  transition-duration: 300ms;
  transition-timing-function: cubic-bezier(0, 0, 0.2, 1);
  transition-property: transform, opacity;
}

.swap-rotate .swap-on,
.swap-rotate .swap-indeterminate,
.swap-rotate input:indeterminate ~ .swap-on {
  --tw-rotate: 45deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.swap-rotate input:checked ~ .swap-off,
.swap-active:where(.swap-rotate) .swap-off,
.swap-rotate input:indeterminate ~ .swap-off {
  --tw-rotate: -45deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.swap-rotate input:checked ~ .swap-on,
.swap-active:where(.swap-rotate) .swap-on,
.swap-rotate input:indeterminate ~ .swap-indeterminate {
  --tw-rotate: 0deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.swap-flip {
  transform-style: preserve-3d;
  perspective: 16em;
}

.swap-flip .swap-on,
.swap-flip .swap-indeterminate,
.swap-flip input:indeterminate ~ .swap-on {
  transform: rotateY(180deg);
  backface-visibility: hidden;
  opacity: 1;
}

.swap-flip input:checked ~ .swap-off,
.swap-active:where(.swap-flip) .swap-off,
.swap-flip input:indeterminate ~ .swap-off {
  transform: rotateY(-180deg);
  backface-visibility: hidden;
  opacity: 1;
}

.swap-flip input:checked ~ .swap-on,
.swap-active:where(.swap-flip) .swap-on,
.swap-flip input:indeterminate ~ .swap-indeterminate {
  transform: rotateY(0deg);
}
.tabs-lifted > .tab:focus-visible {
  border-end-end-radius: 0;
  border-end-start-radius: 0;
}
.tab {
  --tw-text-opacity: 0.5;
}
@media(hover:hover) {
  .tab:hover {
    --tw-text-opacity: 1;
  }
}
.tab {
  --tab-color: var(--fallback-bc,oklch(var(--bc)/1));
  --tab-bg: var(--fallback-b1,oklch(var(--b1)/1));
  --tab-border-color: var(--fallback-b3,oklch(var(--b3)/1));
  color: var(--tab-color);
  padding-inline-start: var(--tab-padding, 1rem);
  padding-inline-end: var(--tab-padding, 1rem);
}
.tab:is(.tab-active, [aria-selected="true"]):not(.tab-disabled):not([disabled]), .tab:is(input:checked) {
  border-color: var(--fallback-bc,oklch(var(--bc)/var(--tw-border-opacity)));
  --tw-border-opacity: 1;
  --tw-text-opacity: 1;
}
.tab:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
}
.tab:focus-visible {
    outline: 2px solid currentColor;
    outline-offset: -5px;
  }
/* disabled */
.tab-disabled,
  .tab[disabled] {
  cursor: not-allowed;
  color: var(--fallback-bc,oklch(var(--bc)/var(--tw-text-opacity)));
  --tw-text-opacity: 0.2;
}
@media (hover: hover) {
    .tab[disabled],
    .tab[disabled]:hover {
    cursor: not-allowed;
    color: var(--fallback-bc,oklch(var(--bc)/var(--tw-text-opacity)));
    --tw-text-opacity: 0.2;
  }
  }
.tabs-bordered > .tab {
  border-color: var(--fallback-bc,oklch(var(--bc)/var(--tw-border-opacity)));
  --tw-border-opacity: 0.2;
  border-style: solid;
  border-bottom-width: calc(var(--tab-border, 1px) + 1px);
}
.tabs-lifted > .tab {
  border: var(--tab-border, 1px) solid transparent;
  border-width: 0 0 var(--tab-border, 1px) 0;
  border-start-start-radius: var(--tab-radius, 0.5rem);
  border-start-end-radius: var(--tab-radius, 0.5rem);
  border-bottom-color: var(--tab-border-color);
  padding-inline-start: var(--tab-padding, 1rem);
  padding-inline-end: var(--tab-padding, 1rem);
  padding-top: var(--tab-border, 1px);
}
.tabs-lifted > .tab:is(.tab-active, [aria-selected="true"]):not(.tab-disabled):not([disabled]), .tabs-lifted > .tab:is(input:checked) {
    background-color: var(--tab-bg);
    border-width: var(--tab-border, 1px) var(--tab-border, 1px) 0 var(--tab-border, 1px);
    border-inline-start-color: var(--tab-border-color);
    border-inline-end-color: var(--tab-border-color);
    border-top-color: var(--tab-border-color);
    padding-inline-start: calc(var(--tab-padding, 1rem) - var(--tab-border, 1px));
    padding-inline-end: calc(var(--tab-padding, 1rem) - var(--tab-border, 1px));
    padding-bottom: var(--tab-border, 1px);
    padding-top: 0;
  }
.tabs-lifted > .tab:is(.tab-active, [aria-selected="true"]):not(.tab-disabled):not([disabled]):before, .tabs-lifted > .tab:is(input:checked):before {
      z-index: 1;
      content: "";
      display: block;
      position: absolute;
      width: calc(100% + var(--tab-radius, 0.5rem) * 2);
      height: var(--tab-radius, 0.5rem);
      bottom: 0;
      background-size: var(--tab-radius, 0.5rem);
      background-position:
        top left,
        top right;
      background-repeat: no-repeat;
      --tab-grad: calc(69% - var(--tab-border, 1px));
      --radius-start: radial-gradient(
        circle at top left,
        transparent var(--tab-grad),
        var(--tab-border-color) calc(var(--tab-grad) + 0.25px),
        var(--tab-border-color) calc(var(--tab-grad) + var(--tab-border, 1px)),
        var(--tab-bg) calc(var(--tab-grad) + var(--tab-border, 1px) + 0.25px)
      );
      --radius-end: radial-gradient(
        circle at top right,
        transparent var(--tab-grad),
        var(--tab-border-color) calc(var(--tab-grad) + 0.25px),
        var(--tab-border-color) calc(var(--tab-grad) + var(--tab-border, 1px)),
        var(--tab-bg) calc(var(--tab-grad) + var(--tab-border, 1px) + 0.25px)
      );
      background-image: var(--radius-start), var(--radius-end);
    }
.tabs-lifted > .tab:is(.tab-active, [aria-selected="true"]):not(.tab-disabled):not([disabled]):first-child:before, .tabs-lifted > .tab:is(input:checked):first-child:before {
      background-image: var(--radius-end);
      background-position: top right;
    }
[dir="rtl"] .tabs-lifted > .tab:is(.tab-active, [aria-selected="true"]):not(.tab-disabled):not([disabled]):first-child:before, [dir="rtl"] .tabs-lifted > .tab:is(input:checked):first-child:before {
        background-image: var(--radius-start);
        background-position: top left;
      }
.tabs-lifted > .tab:is(.tab-active, [aria-selected="true"]):not(.tab-disabled):not([disabled]):last-child:before, .tabs-lifted > .tab:is(input:checked):last-child:before {
      background-image: var(--radius-start);
      background-position: top left;
    }
[dir="rtl"] .tabs-lifted > .tab:is(.tab-active, [aria-selected="true"]):not(.tab-disabled):not([disabled]):last-child:before, [dir="rtl"] .tabs-lifted > .tab:is(input:checked):last-child:before {
        background-image: var(--radius-end);
        background-position: top right;
      }
.tabs-lifted
  > :is(.tab-active, [aria-selected="true"]):not(.tab-disabled):not([disabled])
  + .tabs-lifted
  :is(.tab-active, [aria-selected="true"]):not(.tab-disabled):not([disabled]):before, .tabs-lifted > .tab:is(input:checked) + .tabs-lifted .tab:is(input:checked):before {
    background-image: var(--radius-end);
    background-position: top right;
  }
.tabs-boxed {
  border-radius: var(--rounded-btn, 0.5rem);
  --tw-bg-opacity: 1;
  background-color: var(--fallback-b2,oklch(var(--b2)/var(--tw-bg-opacity)));
  padding: 0.25rem;
}
.tabs-boxed .tab {
  border-radius: var(--rounded-btn, 0.5rem);
}
.tabs-boxed :is(.tab-active, [aria-selected="true"]):not(.tab-disabled):not([disabled]), .tabs-boxed :is(input:checked) {
  --tw-bg-opacity: 1;
  background-color: var(--fallback-p,oklch(var(--p)/var(--tw-bg-opacity)));
  --tw-text-opacity: 1;
  color: var(--fallback-pc,oklch(var(--pc)/var(--tw-text-opacity)));
}
@media(hover:hover) {
  .tabs-boxed :is(.tab-active, [aria-selected="true"]):not(.tab-disabled):not([disabled]):hover, .tabs-boxed :is(input:checked):hover {
    --tw-text-opacity: 1;
    color: var(--fallback-pc,oklch(var(--pc)/var(--tw-text-opacity)));
  }
}
.table {
    border-radius: var(--rounded-box, 1rem);
    text-align: left;
    font-size: 0.875rem;
    line-height: 1.25rem
}
.table:where([dir="rtl"], [dir="rtl"] *) {
    text-align: right
}
  .table :where(th, td) {
    padding-left: 1rem;
    padding-right: 1rem;
    padding-top: 0.75rem;
    padding-bottom: 0.75rem;
    vertical-align: middle
}
  .table tr.active,
  .table tr.active:nth-child(even),
  .table-zebra tbody tr:nth-child(even) {
    --tw-bg-opacity: 1;
    background-color: var(--fallback-b2,oklch(var(--b2)/var(--tw-bg-opacity)))
}
  @media(hover:hover) {
    .table tr.hover:hover,
  .table tr.hover:nth-child(even):hover {
        --tw-bg-opacity: 1;
        background-color: var(--fallback-b2,oklch(var(--b2)/var(--tw-bg-opacity)))
    }
}
  .table-zebra tr.active,
    .table-zebra tr.active:nth-child(even),
    .table-zebra-zebra tbody tr:nth-child(even) {
    --tw-bg-opacity: 1;
    background-color: var(--fallback-b3,oklch(var(--b3)/var(--tw-bg-opacity)))
}
  @media(hover:hover) {
    .table-zebra tr.hover:hover,
  .table-zebra tr.hover:nth-child(even):hover {
        --tw-bg-opacity: 1;
        background-color: var(--fallback-b3,oklch(var(--b3)/var(--tw-bg-opacity)))
    }
}
  .table :where(thead tr, tbody tr:not(:last-child), tbody tr:first-child:last-child) {
    border-bottom-width: 1px;
    --tw-border-opacity: 1;
    border-bottom-color: var(--fallback-b2,oklch(var(--b2)/var(--tw-border-opacity)))
}
  .table :where(thead, tfoot) {
    white-space: nowrap;
    font-size: 0.75rem;
    line-height: 1rem;
    font-weight: 700;
    color: var(--fallback-bc,oklch(var(--bc)/0.6))
}
  .table :where(tfoot) {
    border-top-width: 1px;
    --tw-border-opacity: 1;
    border-top-color: var(--fallback-b2,oklch(var(--b2)/var(--tw-border-opacity)))
}
.textarea {
    border-radius: var(--rounded-btn, 0.5rem);
    border-width: 1px;
    border-color: transparent;
    --tw-bg-opacity: 1;
    background-color: var(--fallback-b1,oklch(var(--b1)/var(--tw-bg-opacity)));
}
  .textarea-bordered {
    border-color: var(--fallback-bc,oklch(var(--bc)/0.2));
}
  .textarea:focus {
    box-shadow: none;
    border-color: var(--fallback-bc,oklch(var(--bc)/0.2));
    outline-style: solid;
    outline-width: 2px;
    outline-offset: 2px;
    outline-color: var(--fallback-bc,oklch(var(--bc)/0.2));
  }
  .textarea-ghost {
    --tw-bg-opacity: 0.05;
}
  .textarea-ghost:focus {
    --tw-bg-opacity: 1;
    --tw-text-opacity: 1;
    color: var(--fallback-bc,oklch(var(--bc)/var(--tw-text-opacity)));
      box-shadow: none;
}
  .textarea-primary {
    --tw-border-opacity: 1;
    border-color: var(--fallback-p,oklch(var(--p)/var(--tw-border-opacity)));
}
  .textarea-primary:focus {
    --tw-border-opacity: 1;
    border-color: var(--fallback-p,oklch(var(--p)/var(--tw-border-opacity)));
    outline-color: var(--fallback-p,oklch(var(--p)/1));
}
  .textarea-secondary {
    --tw-border-opacity: 1;
    border-color: var(--fallback-s,oklch(var(--s)/var(--tw-border-opacity)));
}
  .textarea-secondary:focus {
    --tw-border-opacity: 1;
    border-color: var(--fallback-s,oklch(var(--s)/var(--tw-border-opacity)));
    outline-color: var(--fallback-s,oklch(var(--s)/1));
}
  .textarea-accent {
    --tw-border-opacity: 1;
    border-color: var(--fallback-a,oklch(var(--a)/var(--tw-border-opacity)));
}
  .textarea-accent:focus {
    --tw-border-opacity: 1;
    border-color: var(--fallback-a,oklch(var(--a)/var(--tw-border-opacity)));
    outline-color: var(--fallback-a,oklch(var(--a)/1));
}
  .textarea-info {
    --tw-border-opacity: 1;
    border-color: var(--fallback-in,oklch(var(--in)/var(--tw-border-opacity)));
}
  .textarea-info:focus {
    --tw-border-opacity: 1;
    border-color: var(--fallback-in,oklch(var(--in)/var(--tw-border-opacity)));
    outline-color: var(--fallback-in,oklch(var(--in)/1));
}
  .textarea-success {
    --tw-border-opacity: 1;
    border-color: var(--fallback-su,oklch(var(--su)/var(--tw-border-opacity)));
}
  .textarea-success:focus {
    --tw-border-opacity: 1;
    border-color: var(--fallback-su,oklch(var(--su)/var(--tw-border-opacity)));
    outline-color: var(--fallback-su,oklch(var(--su)/1));
}
  .textarea-warning {
    --tw-border-opacity: 1;
    border-color: var(--fallback-wa,oklch(var(--wa)/var(--tw-border-opacity)));
}
  .textarea-warning:focus {
    --tw-border-opacity: 1;
    border-color: var(--fallback-wa,oklch(var(--wa)/var(--tw-border-opacity)));
    outline-color: var(--fallback-wa,oklch(var(--wa)/1));
}
  .textarea-error {
    --tw-border-opacity: 1;
    border-color: var(--fallback-er,oklch(var(--er)/var(--tw-border-opacity)));
}
  .textarea-error:focus {
    --tw-border-opacity: 1;
    border-color: var(--fallback-er,oklch(var(--er)/var(--tw-border-opacity)));
    outline-color: var(--fallback-er,oklch(var(--er)/1));
}
  .textarea-disabled,
  .textarea:disabled,
  .textarea[disabled] {
    cursor: not-allowed;
    --tw-border-opacity: 1;
    border-color: var(--fallback-b2,oklch(var(--b2)/var(--tw-border-opacity)));
    --tw-bg-opacity: 1;
    background-color: var(--fallback-b2,oklch(var(--b2)/var(--tw-bg-opacity)));
    color: var(--fallback-bc,oklch(var(--bc)/0.4));
}
  .textarea-disabled::placeholder,
  .textarea:disabled::placeholder,
  .textarea[disabled]::placeholder {
    color: var(--fallback-bc,oklch(var(--bc)/var(--tw-placeholder-opacity)));
    --tw-placeholder-opacity: 0.2;
}
.timeline hr {
    height: 0.25rem
}
:where(.timeline hr) {
    --tw-bg-opacity: 1;
    background-color: var(--fallback-b3,oklch(var(--b3)/var(--tw-bg-opacity)))
}
:where(.timeline:has(.timeline-middle) hr):first-child {
    border-start-end-radius: var(--rounded-badge, 1.9rem);
    border-end-end-radius: var(--rounded-badge, 1.9rem);
    border-start-start-radius: 0px;
    border-end-start-radius: 0px
}
:where(.timeline:has(.timeline-middle) hr):last-child {
    border-start-start-radius: var(--rounded-badge, 1.9rem);
    border-end-start-radius: var(--rounded-badge, 1.9rem);
    border-start-end-radius: 0px;
    border-end-end-radius: 0px
}
:where(.timeline:not(:has(.timeline-middle)) :first-child hr:last-child) {
    border-start-start-radius: var(--rounded-badge, 1.9rem);
    border-end-start-radius: var(--rounded-badge, 1.9rem);
    border-start-end-radius: 0px;
    border-end-end-radius: 0px
}
:where(.timeline:not(:has(.timeline-middle)) :last-child hr:first-child) {
    border-start-end-radius: var(--rounded-badge, 1.9rem);
    border-end-end-radius: var(--rounded-badge, 1.9rem);
    border-start-start-radius: 0px;
    border-end-start-radius: 0px
}
.timeline-box {
    border-radius: var(--rounded-box, 1rem);
    border-width: 1px;
    --tw-border-opacity: 1;
    border-color: var(--fallback-b3,oklch(var(--b3)/var(--tw-border-opacity)));
    --tw-bg-opacity: 1;
    background-color: var(--fallback-b1,oklch(var(--b1)/var(--tw-bg-opacity)));
    padding-left: 1rem;
    padding-right: 1rem;
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
    --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow)
}
.toast {
    gap: 0.5rem;
    padding: 1rem;
}
  .toast > * {
    animation: toast-pop 0.25s ease-out;
  }

@keyframes toast-pop {
  0% {
    transform: scale(0.9);
    opacity: 0;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}
.toggle {
  --tglbg: var(--fallback-b1,oklch(var(--b1)/1));
  --handleoffset: 1.5rem;
  --handleoffsetcalculator: calc(var(--handleoffset) * -1);
  --togglehandleborder: 0 0;
  height: 1.5rem;
  width: 3rem;
  cursor: pointer;
  appearance: none;
  border-radius: var(--rounded-badge, 1.9rem);
  border-width: 1px;
  border-color: currentColor;
  background-color: currentColor;
  color: var(--fallback-bc,oklch(var(--bc)/0.5));
  transition:
    background,
    box-shadow var(--animation-input, 0.2s) ease-out;
  box-shadow:
    var(--handleoffsetcalculator) 0 0 2px var(--tglbg) inset,
    0 0 0 2px var(--tglbg) inset,
    var(--togglehandleborder);
}
  [dir="rtl"] .toggle {
    --handleoffsetcalculator: calc(var(--handleoffset) * 1);
  }
  .toggle:focus-visible {
  outline-style: solid;
  outline-width: 2px;
  outline-offset: 2px;
  outline-color: var(--fallback-bc,oklch(var(--bc)/0.2));
}
  .toggle:hover {
  background-color: currentColor;
}
  .toggle:checked,
  .toggle[aria-checked="true"] {
    background-image: none;
    --handleoffsetcalculator: var(--handleoffset);
    --tw-text-opacity: 1;
    color: var(--fallback-bc,oklch(var(--bc)/var(--tw-text-opacity)));
  }
  [dir="rtl"] .toggle:checked, [dir="rtl"] .toggle[aria-checked="true"] {
      --handleoffsetcalculator: calc(var(--handleoffset) * -1);
    }
  .toggle:indeterminate {
  --tw-text-opacity: 1;
  color: var(--fallback-bc,oklch(var(--bc)/var(--tw-text-opacity)));
    box-shadow:
      calc(var(--handleoffset) / 2) 0 0 2px var(--tglbg) inset,
      calc(var(--handleoffset) / -2) 0 0 2px var(--tglbg) inset,
      0 0 0 2px var(--tglbg) inset;
}
  [dir="rtl"] .toggle:indeterminate {
      box-shadow:
        calc(var(--handleoffset) / 2) 0 0 2px var(--tglbg) inset,
        calc(var(--handleoffset) / -2) 0 0 2px var(--tglbg) inset,
        0 0 0 2px var(--tglbg) inset;
    }
  .toggle-primary:focus-visible {
  outline-color: var(--fallback-p,oklch(var(--p)/1));
}
  .toggle-primary:checked,
    .toggle-primary[aria-checked="true"] {
  border-color: var(--fallback-p,oklch(var(--p)/var(--tw-border-opacity)));
  --tw-border-opacity: 0.1;
  --tw-bg-opacity: 1;
  background-color: var(--fallback-p,oklch(var(--p)/var(--tw-bg-opacity)));
  --tw-text-opacity: 1;
  color: var(--fallback-pc,oklch(var(--pc)/var(--tw-text-opacity)));
}
  .toggle-secondary:focus-visible {
  outline-color: var(--fallback-s,oklch(var(--s)/1));
}
  .toggle-secondary:checked,
    .toggle-secondary[aria-checked="true"] {
  border-color: var(--fallback-s,oklch(var(--s)/var(--tw-border-opacity)));
  --tw-border-opacity: 0.1;
  --tw-bg-opacity: 1;
  background-color: var(--fallback-s,oklch(var(--s)/var(--tw-bg-opacity)));
  --tw-text-opacity: 1;
  color: var(--fallback-sc,oklch(var(--sc)/var(--tw-text-opacity)));
}
  .toggle-accent:focus-visible {
  outline-color: var(--fallback-a,oklch(var(--a)/1));
}
  .toggle-accent:checked,
    .toggle-accent[aria-checked="true"] {
  border-color: var(--fallback-a,oklch(var(--a)/var(--tw-border-opacity)));
  --tw-border-opacity: 0.1;
  --tw-bg-opacity: 1;
  background-color: var(--fallback-a,oklch(var(--a)/var(--tw-bg-opacity)));
  --tw-text-opacity: 1;
  color: var(--fallback-ac,oklch(var(--ac)/var(--tw-text-opacity)));
}
  .toggle-success:focus-visible {
  outline-color: var(--fallback-su,oklch(var(--su)/1));
}
  .toggle-success:checked,
    .toggle-success[aria-checked="true"] {
  border-color: var(--fallback-su,oklch(var(--su)/var(--tw-border-opacity)));
  --tw-border-opacity: 0.1;
  --tw-bg-opacity: 1;
  background-color: var(--fallback-su,oklch(var(--su)/var(--tw-bg-opacity)));
  --tw-text-opacity: 1;
  color: var(--fallback-suc,oklch(var(--suc)/var(--tw-text-opacity)));
}
  .toggle-warning:focus-visible {
  outline-color: var(--fallback-wa,oklch(var(--wa)/1));
}
  .toggle-warning:checked,
    .toggle-warning[aria-checked="true"] {
  border-color: var(--fallback-wa,oklch(var(--wa)/var(--tw-border-opacity)));
  --tw-border-opacity: 0.1;
  --tw-bg-opacity: 1;
  background-color: var(--fallback-wa,oklch(var(--wa)/var(--tw-bg-opacity)));
  --tw-text-opacity: 1;
  color: var(--fallback-wac,oklch(var(--wac)/var(--tw-text-opacity)));
}
  .toggle-info:focus-visible {
  outline-color: var(--fallback-in,oklch(var(--in)/1));
}
  .toggle-info:checked,
    .toggle-info[aria-checked="true"] {
  border-color: var(--fallback-in,oklch(var(--in)/var(--tw-border-opacity)));
  --tw-border-opacity: 0.1;
  --tw-bg-opacity: 1;
  background-color: var(--fallback-in,oklch(var(--in)/var(--tw-bg-opacity)));
  --tw-text-opacity: 1;
  color: var(--fallback-inc,oklch(var(--inc)/var(--tw-text-opacity)));
}
  .toggle-error:focus-visible {
  outline-color: var(--fallback-er,oklch(var(--er)/1));
}
  .toggle-error:checked,
    .toggle-error[aria-checked="true"] {
  border-color: var(--fallback-er,oklch(var(--er)/var(--tw-border-opacity)));
  --tw-border-opacity: 0.1;
  --tw-bg-opacity: 1;
  background-color: var(--fallback-er,oklch(var(--er)/var(--tw-bg-opacity)));
  --tw-text-opacity: 1;
  color: var(--fallback-erc,oklch(var(--erc)/var(--tw-text-opacity)));
}
  .toggle:disabled {
  cursor: not-allowed;
  --tw-border-opacity: 1;
  border-color: var(--fallback-bc,oklch(var(--bc)/var(--tw-border-opacity)));
  background-color: transparent;
  opacity: 0.3;
    --togglehandleborder: 0 0 0 3px var(--fallback-bc,oklch(var(--bc)/1)) inset,
      var(--handleoffsetcalculator) 0 0 3px var(--fallback-bc,oklch(var(--bc)/1)) inset;
}

/* backward compatibility */

.toggle-mark {
  display: none;
}
:root .prose {
  --tw-prose-body: var(--fallback-bc,oklch(var(--bc)/0.8));
  --tw-prose-headings: var(--fallback-bc,oklch(var(--bc)/1));
  --tw-prose-lead: var(--fallback-bc,oklch(var(--bc)/1));
  --tw-prose-links: var(--fallback-bc,oklch(var(--bc)/1));
  --tw-prose-bold: var(--fallback-bc,oklch(var(--bc)/1));
  --tw-prose-counters: var(--fallback-bc,oklch(var(--bc)/1));
  --tw-prose-bullets: var(--fallback-bc,oklch(var(--bc)/0.5));
  --tw-prose-hr: var(--fallback-bc,oklch(var(--bc)/0.2));
  --tw-prose-quotes: var(--fallback-bc,oklch(var(--bc)/1));
  --tw-prose-quote-borders: var(--fallback-bc,oklch(var(--bc)/0.2));
  --tw-prose-captions: var(--fallback-bc,oklch(var(--bc)/0.5));
  --tw-prose-code: var(--fallback-bc,oklch(var(--bc)/1));
  --tw-prose-pre-code: var(--fallback-nc,oklch(var(--nc)/1));
  --tw-prose-pre-bg: var(--fallback-n,oklch(var(--n)/1));
  --tw-prose-th-borders: var(--fallback-bc,oklch(var(--bc)/0.5));
  --tw-prose-td-borders: var(--fallback-bc,oklch(var(--bc)/0.2));
}
.prose :where(code):not(:where([class~="not-prose"] *, pre *)) {
    padding: 1px 8px;
    border-radius: var(--rounded-badge);
    font-weight: initial;
    background-color: var(--fallback-bc,oklch(var(--bc)/0.1));
  }
@supports not (color: oklch(0% 0 0)) {
.prose :where(code):not(:where([class~="not-prose"] *, pre *)) {
      background-color: var(--fallback-b3,oklch(var(--b3)/1))
  }
    }
.prose :where(code):not(:where([class~="not-prose"], [class~="not-prose"] *))::before, .prose :where(code):not(:where([class~="not-prose"], [class~="not-prose"] *))::after {
    display: none;
  }
.prose pre code {
      border-radius: 0;
      padding: 0;
    }
.prose :where(tbody tr, thead):not(:where([class~="not-prose"] *)) {
    border-bottom-color: var(--fallback-bc,oklch(var(--bc)/0.2));
  }
