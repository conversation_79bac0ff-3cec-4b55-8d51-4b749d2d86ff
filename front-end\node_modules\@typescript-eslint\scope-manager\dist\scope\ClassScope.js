"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ClassScope = void 0;
const ScopeBase_1 = require("./ScopeBase");
const ScopeType_1 = require("./ScopeType");
class ClassScope extends ScopeBase_1.ScopeBase {
    constructor(scopeManager, upperScope, block) {
        super(scope<PERSON>anager, ScopeType_1.ScopeType.class, upperScope, block, false);
    }
}
exports.ClassScope = ClassScope;
//# sourceMappingURL=ClassScope.js.map