{"version": 3, "sources": ["../../src/ReactQueryDevtools.tsx"], "sourcesContent": ["'use client'\nimport * as React from 'react'\nimport { onlineManager, useQueryClient } from '@tanstack/react-query'\nimport { TanstackQueryDevtools } from '@tanstack/query-devtools'\nimport type {\n  DevtoolsButtonPosition,\n  DevtoolsErrorType,\n  DevtoolsPosition,\n} from '@tanstack/query-devtools'\nimport type { QueryClient } from '@tanstack/react-query'\n\nexport interface DevtoolsOptions {\n  /**\n   * Set this true if you want the dev tools to default to being open\n   */\n  initialIsOpen?: boolean\n  /**\n   * The position of the React Query logo to open and close the devtools panel.\n   * 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right'\n   * Defaults to 'bottom-right'.\n   */\n  buttonPosition?: DevtoolsButtonPosition\n  /**\n   * The position of the React Query devtools panel.\n   * 'top' | 'bottom' | 'left' | 'right'\n   * Defaults to 'bottom'.\n   */\n  position?: DevtoolsPosition\n  /**\n   * Custom instance of QueryClient\n   */\n  client?: QueryClient\n  /**\n   * Use this so you can define custom errors that can be shown in the devtools.\n   */\n  errorTypes?: Array<DevtoolsErrorType>\n  /**\n   * Use this to pass a nonce to the style tag that is added to the document head. This is useful if you are using a Content Security Policy (CSP) nonce to allow inline styles.\n   */\n  styleNonce?: string\n  /**\n   * Use this so you can attach the devtool's styles to specific element in the DOM.\n   */\n  shadowDOMTarget?: ShadowRoot\n}\n\nexport function ReactQueryDevtools(\n  props: DevtoolsOptions,\n): React.ReactElement | null {\n  const queryClient = useQueryClient(props.client)\n  const ref = React.useRef<HTMLDivElement>(null)\n  const {\n    buttonPosition,\n    position,\n    initialIsOpen,\n    errorTypes,\n    styleNonce,\n    shadowDOMTarget,\n  } = props\n  const [devtools] = React.useState(\n    new TanstackQueryDevtools({\n      client: queryClient,\n      queryFlavor: 'React Query',\n      version: '5',\n      onlineManager,\n      buttonPosition,\n      position,\n      initialIsOpen,\n      errorTypes,\n      styleNonce,\n      shadowDOMTarget,\n    }),\n  )\n\n  React.useEffect(() => {\n    devtools.setClient(queryClient)\n  }, [queryClient, devtools])\n\n  React.useEffect(() => {\n    if (buttonPosition) {\n      devtools.setButtonPosition(buttonPosition)\n    }\n  }, [buttonPosition, devtools])\n\n  React.useEffect(() => {\n    if (position) {\n      devtools.setPosition(position)\n    }\n  }, [position, devtools])\n\n  React.useEffect(() => {\n    devtools.setInitialIsOpen(initialIsOpen || false)\n  }, [initialIsOpen, devtools])\n\n  React.useEffect(() => {\n    devtools.setErrorTypes(errorTypes || [])\n  }, [errorTypes, devtools])\n\n  React.useEffect(() => {\n    if (ref.current) {\n      devtools.mount(ref.current)\n    }\n\n    return () => {\n      devtools.unmount()\n    }\n  }, [devtools])\n\n  return <div className=\"tsqd-parent-container\" ref={ref}></div>\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,YAAuB;AACvB,yBAA8C;AAC9C,4BAAsC;AAyG7B;AA9DF,SAAS,mBACd,OAC2B;AAC3B,QAAM,kBAAc,mCAAe,MAAM,MAAM;AAC/C,QAAM,MAAY,aAAuB,IAAI;AAC7C,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,CAAC,QAAQ,IAAU;AAAA,IACvB,IAAI,4CAAsB;AAAA,MACxB,QAAQ;AAAA,MACR,aAAa;AAAA,MACb,SAAS;AAAA,MACT;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC;AAAA,EACH;AAEA,EAAM,gBAAU,MAAM;AACpB,aAAS,UAAU,WAAW;AAAA,EAChC,GAAG,CAAC,aAAa,QAAQ,CAAC;AAE1B,EAAM,gBAAU,MAAM;AACpB,QAAI,gBAAgB;AAClB,eAAS,kBAAkB,cAAc;AAAA,IAC3C;AAAA,EACF,GAAG,CAAC,gBAAgB,QAAQ,CAAC;AAE7B,EAAM,gBAAU,MAAM;AACpB,QAAI,UAAU;AACZ,eAAS,YAAY,QAAQ;AAAA,IAC/B;AAAA,EACF,GAAG,CAAC,UAAU,QAAQ,CAAC;AAEvB,EAAM,gBAAU,MAAM;AACpB,aAAS,iBAAiB,iBAAiB,KAAK;AAAA,EAClD,GAAG,CAAC,eAAe,QAAQ,CAAC;AAE5B,EAAM,gBAAU,MAAM;AACpB,aAAS,cAAc,cAAc,CAAC,CAAC;AAAA,EACzC,GAAG,CAAC,YAAY,QAAQ,CAAC;AAEzB,EAAM,gBAAU,MAAM;AACpB,QAAI,IAAI,SAAS;AACf,eAAS,MAAM,IAAI,OAAO;AAAA,IAC5B;AAEA,WAAO,MAAM;AACX,eAAS,QAAQ;AAAA,IACnB;AAAA,EACF,GAAG,CAAC,QAAQ,CAAC;AAEb,SAAO,4CAAC,SAAI,WAAU,yBAAwB,KAAU;AAC1D;", "names": []}