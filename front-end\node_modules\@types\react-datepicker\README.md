# Installation
> `npm install --save @types/react-datepicker`

# Summary
This package contains type definitions for react-datepicker (https://github.com/Hacker0x01/react-datepicker).

# Details
Files were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/react-datepicker.

### Additional Details
 * Last updated: Mon, 11 Mar 2024 23:35:33 GMT
 * Dependencies: [@floating-ui/react](https://npmjs.com/package/@floating-ui/react), [@types/react](https://npmjs.com/package/@types/react), [date-fns](https://npmjs.com/package/date-fns)

# Credits
These definitions were written by [<PERSON><PERSON>](https://github.com/radziksh), [<PERSON>](https://github.com/smrq), [<PERSON>](https://github.com/royxue), [Koala Human](https://github.com/KoalaHuman), [<PERSON>](https://github.com/justingrant), [<PERSON><PERSON>](https://github.com/avik<PERSON>), [<PERSON><PERSON>](https://github.com/seckie), [<PERSON> Gougeon](https://github.com/kerry-g), [Shiftr Tech SAS](https://github.com/ShiftrTechSAS), [Pirasis Leelatanon](https://github.com/1pete), [Alexander Shipulin](https://github.com/y), and [Rafik Ogandzhanian](https://github.com/inomn).
