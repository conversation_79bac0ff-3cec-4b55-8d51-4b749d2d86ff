!function(e,n){"object"==typeof exports&&"undefined"!=typeof module?n(exports):"function"==typeof define&&define.amd?define(["exports"],n):n((e="undefined"!=typeof globalThis?globalThis:e||self).FloatingUIUtilsDOM={})}(this,(function(e){"use strict";function n(){return"undefined"!=typeof window}function t(e){return i(e)?(e.nodeName||"").toLowerCase():"#document"}function o(e){var n;return(null==e||null==(n=e.ownerDocument)?void 0:n.defaultView)||window}function r(e){var n;return null==(n=(i(e)?e.ownerDocument:e.document)||window.document)?void 0:n.documentElement}function i(e){return!!n()&&(e instanceof Node||e instanceof o(e).Node)}function l(e){return!!n()&&(e instanceof Element||e instanceof o(e).Element)}function c(e){return!!n()&&(e instanceof HTMLElement||e instanceof o(e).HTMLElement)}function u(e){return!(!n()||"undefined"==typeof ShadowRoot)&&(e instanceof ShadowRoot||e instanceof o(e).ShadowRoot)}function s(e){const{overflow:n,overflowX:t,overflowY:o,display:r}=p(e);return/auto|scroll|overlay|hidden|clip/.test(n+o+t)&&!["inline","contents"].includes(r)}function f(e){return[":popover-open",":modal"].some((n=>{try{return e.matches(n)}catch(e){return!1}}))}function a(e){const n=d(),t=l(e)?p(e):e;return["transform","translate","scale","rotate","perspective"].some((e=>!!t[e]&&"none"!==t[e]))||!!t.containerType&&"normal"!==t.containerType||!n&&!!t.backdropFilter&&"none"!==t.backdropFilter||!n&&!!t.filter&&"none"!==t.filter||["transform","translate","scale","rotate","perspective","filter"].some((e=>(t.willChange||"").includes(e)))||["paint","layout","strict","content"].some((e=>(t.contain||"").includes(e)))}function d(){return!("undefined"==typeof CSS||!CSS.supports)&&CSS.supports("-webkit-backdrop-filter","none")}function m(e){return["html","body","#document"].includes(t(e))}function p(e){return o(e).getComputedStyle(e)}function w(e){if("html"===t(e))return e;const n=e.assignedSlot||e.parentNode||u(e)&&e.host||r(e);return u(n)?n.host:n}function y(e){const n=w(e);return m(n)?e.ownerDocument?e.ownerDocument.body:e.body:c(n)&&s(n)?n:y(n)}function v(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}e.getComputedStyle=p,e.getContainingBlock=function(e){let n=w(e);for(;c(n)&&!m(n);){if(a(n))return n;if(f(n))return null;n=w(n)}return null},e.getDocumentElement=r,e.getFrameElement=v,e.getNearestOverflowAncestor=y,e.getNodeName=t,e.getNodeScroll=function(e){return l(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}},e.getOverflowAncestors=function e(n,t,r){var i;void 0===t&&(t=[]),void 0===r&&(r=!0);const l=y(n),c=l===(null==(i=n.ownerDocument)?void 0:i.body),u=o(l);if(c){const n=v(u);return t.concat(u,u.visualViewport||[],s(l)?l:[],n&&r?e(n):[])}return t.concat(l,e(l,[],r))},e.getParentNode=w,e.getWindow=o,e.isContainingBlock=a,e.isElement=l,e.isHTMLElement=c,e.isLastTraversableNode=m,e.isNode=i,e.isOverflowElement=s,e.isShadowRoot=u,e.isTableElement=function(e){return["table","td","th"].includes(t(e))},e.isTopLayer=f,e.isWebKit=d}));
