---
description: '<Description from rule metadata here>'
---

import Tabs from '@theme/Tabs';
import TabItem from '@theme/TabItem';

> 🛑 This file is source code, not the primary documentation location! 🛑
>
> See **https://typescript-eslint.io/rules/RULE_NAME_REPLACEME** for documentation.

## Examples

To fill out: tell us more about this rule.

<Tabs>
<TabItem value="❌ Incorrect">

```ts
// To fill out: incorrect code
```

</TabItem>
<TabItem value="✅ Correct">

```ts
// To fill out: correct code
```

</TabItem>
</Tabs>

## When Not To Use It

To fill out: why wouldn't you want to use this rule?
For example if this rule requires a feature released in a certain TS version.
