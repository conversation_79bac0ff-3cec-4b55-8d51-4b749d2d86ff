{"name": "css-selector-tokenizer", "version": "0.8.0", "description": "Parses and stringifies CSS selectors", "main": "lib/index.js", "scripts": {"lint": "eslint .", "pretest": "npm run lint", "test": "mocha", "cover": "nyc npm test", "build-regexpu": "node scripts/build-regexpu.js", "report:coveralls": "nyc report --reporter=text-lcov | coveralls", "report:codecov": "nyc report --reporter=text-lcov | codecov --pipe", "publish-patch": "npm test && npm version patch && git push && git push --tags && npm publish"}, "repository": {"type": "git", "url": "https://github.com/css-modules/css-selector-tokenizer.git"}, "keywords": ["css-modules", "selectors"], "files": ["lib"], "author": "<PERSON> @sokra", "license": "MIT", "bugs": {"url": "https://github.com/css-modules/css-selector-tokenizer/issues"}, "homepage": "https://github.com/css-modules/css-selector-tokenizer", "dependencies": {"cssesc": "^3.0.0", "fastparse": "^1.1.2"}, "devDependencies": {"codecov": "^3.6.5", "coveralls": "^3.0.9", "eslint": "^6.8.0", "mocha": "^7.1.0", "nyc": "^15.0.0", "regexpu-core": "^4.6.0"}, "directories": {"test": "test"}}