var q=function(G){return q=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(B){return typeof B}:function(B){return B&&typeof Symbol=="function"&&B.constructor===Symbol&&B!==Symbol.prototype?"symbol":typeof B},q(G)},W=function(G,B){var H=Object.keys(G);if(Object.getOwnPropertySymbols){var E=Object.getOwnPropertySymbols(G);B&&(E=E.filter(function(M){return Object.getOwnPropertyDescriptor(G,M).enumerable})),H.push.apply(H,E)}return H},K=function(G){for(var B=1;B<arguments.length;B++){var H=arguments[B]!=null?arguments[B]:{};B%2?W(Object(H),!0).forEach(function(E){CJ(G,E,H[E])}):Object.getOwnPropertyDescriptors?Object.defineProperties(G,Object.getOwnPropertyDescriptors(H)):W(Object(H)).forEach(function(E){Object.defineProperty(G,E,Object.getOwnPropertyDescriptor(H,E))})}return G},CJ=function(G,B,H){if(B=UJ(B),B in G)Object.defineProperty(G,B,{value:H,enumerable:!0,configurable:!0,writable:!0});else G[B]=H;return G},UJ=function(G){var B=ZJ(G,"string");return q(B)=="symbol"?B:String(B)},ZJ=function(G,B){if(q(G)!="object"||!G)return G;var H=G[Symbol.toPrimitive];if(H!==void 0){var E=H.call(G,B||"default");if(q(E)!="object")return E;throw new TypeError("@@toPrimitive must return a primitive value.")}return(B==="string"?String:Number)(G)};(function(G){var B=Object.defineProperty,H=function J(U,C){for(var Z in C)B(U,Z,{get:C[Z],enumerable:!0,configurable:!0,set:function X(Y){return C[Z]=function(){return Y}}})},E={lessThanXSeconds:{standalone:{one:"weniger als 1 Sekunde",other:"weniger als {{count}} Sekunden"},withPreposition:{one:"weniger als 1 Sekunde",other:"weniger als {{count}} Sekunden"}},xSeconds:{standalone:{one:"1 Sekunde",other:"{{count}} Sekunden"},withPreposition:{one:"1 Sekunde",other:"{{count}} Sekunden"}},halfAMinute:{standalone:"eine halbe Minute",withPreposition:"einer halben Minute"},lessThanXMinutes:{standalone:{one:"weniger als 1 Minute",other:"weniger als {{count}} Minuten"},withPreposition:{one:"weniger als 1 Minute",other:"weniger als {{count}} Minuten"}},xMinutes:{standalone:{one:"1 Minute",other:"{{count}} Minuten"},withPreposition:{one:"1 Minute",other:"{{count}} Minuten"}},aboutXHours:{standalone:{one:"etwa 1 Stunde",other:"etwa {{count}} Stunden"},withPreposition:{one:"etwa 1 Stunde",other:"etwa {{count}} Stunden"}},xHours:{standalone:{one:"1 Stunde",other:"{{count}} Stunden"},withPreposition:{one:"1 Stunde",other:"{{count}} Stunden"}},xDays:{standalone:{one:"1 Tag",other:"{{count}} Tage"},withPreposition:{one:"1 Tag",other:"{{count}} Tagen"}},aboutXWeeks:{standalone:{one:"etwa 1 Woche",other:"etwa {{count}} Wochen"},withPreposition:{one:"etwa 1 Woche",other:"etwa {{count}} Wochen"}},xWeeks:{standalone:{one:"1 Woche",other:"{{count}} Wochen"},withPreposition:{one:"1 Woche",other:"{{count}} Wochen"}},aboutXMonths:{standalone:{one:"etwa 1 Monat",other:"etwa {{count}} Monate"},withPreposition:{one:"etwa 1 Monat",other:"etwa {{count}} Monaten"}},xMonths:{standalone:{one:"1 Monat",other:"{{count}} Monate"},withPreposition:{one:"1 Monat",other:"{{count}} Monaten"}},aboutXYears:{standalone:{one:"etwa 1 Jahr",other:"etwa {{count}} Jahre"},withPreposition:{one:"etwa 1 Jahr",other:"etwa {{count}} Jahren"}},xYears:{standalone:{one:"1 Jahr",other:"{{count}} Jahre"},withPreposition:{one:"1 Jahr",other:"{{count}} Jahren"}},overXYears:{standalone:{one:"mehr als 1 Jahr",other:"mehr als {{count}} Jahre"},withPreposition:{one:"mehr als 1 Jahr",other:"mehr als {{count}} Jahren"}},almostXYears:{standalone:{one:"fast 1 Jahr",other:"fast {{count}} Jahre"},withPreposition:{one:"fast 1 Jahr",other:"fast {{count}} Jahren"}}},M=function J(U,C,Z){var X,Y=Z!==null&&Z!==void 0&&Z.addSuffix?E[U].withPreposition:E[U].standalone;if(typeof Y==="string")X=Y;else if(C===1)X=Y.one;else X=Y.other.replace("{{count}}",String(C));if(Z!==null&&Z!==void 0&&Z.addSuffix)if(Z.comparison&&Z.comparison>0)return"in "+X;else return"vor "+X;return X};function S(J){return function(){var U=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},C=U.width?String(U.width):J.defaultWidth,Z=J.formats[C]||J.formats[J.defaultWidth];return Z}}var $={full:"EEEE, do MMMM y",long:"do MMMM y",medium:"do MMM y",short:"dd.MM.y"},D={full:"HH:mm:ss zzzz",long:"HH:mm:ss z",medium:"HH:mm:ss",short:"HH:mm"},R={full:"{{date}} 'um' {{time}}",long:"{{date}} 'um' {{time}}",medium:"{{date}} {{time}}",short:"{{date}} {{time}}"},f={date:S({formats:$,defaultWidth:"full"}),time:S({formats:D,defaultWidth:"full"}),dateTime:S({formats:R,defaultWidth:"full"})},L={lastWeek:"'letzten' eeee 'um' p",yesterday:"'gestern um' p",today:"'heute um' p",tomorrow:"'morgen um' p",nextWeek:"eeee 'um' p",other:"P"},j=function J(U,C,Z,X){return L[U]};function A(J){return function(U){var C=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},Z=C.width,X=Z&&J.matchPatterns[Z]||J.matchPatterns[J.defaultMatchWidth],Y=U.match(X);if(!Y)return null;var I=Y[0],O=Z&&J.parsePatterns[Z]||J.parsePatterns[J.defaultParseWidth],x=Array.isArray(O)?w(O,function(z){return z.test(I)}):V(O,function(z){return z.test(I)}),Q;Q=J.valueCallback?J.valueCallback(x):x,Q=C.valueCallback?C.valueCallback(Q):Q;var JJ=U.slice(I.length);return{value:Q,rest:JJ}}}var V=function J(U,C){for(var Z in U)if(Object.prototype.hasOwnProperty.call(U,Z)&&C(U[Z]))return Z;return},w=function J(U,C){for(var Z=0;Z<U.length;Z++)if(C(U[Z]))return Z;return};function v(J){return function(U){var C=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},Z=U.match(J.matchPattern);if(!Z)return null;var X=Z[0],Y=U.match(J.parsePattern);if(!Y)return null;var I=J.valueCallback?J.valueCallback(Y[0]):Y[0];I=C.valueCallback?C.valueCallback(I):I;var O=U.slice(X.length);return{value:I,rest:O}}}var _=/^(\d+)(\.)?/i,P=/\d+/i,F={narrow:/^(v\.? ?Chr\.?|n\.? ?Chr\.?)/i,abbreviated:/^(v\.? ?Chr\.?|n\.? ?Chr\.?)/i,wide:/^(vor Christus|vor unserer Zeitrechnung|nach Christus|unserer Zeitrechnung)/i},b={any:[/^v/i,/^n/i]},h={narrow:/^[1234]/i,abbreviated:/^q[1234]/i,wide:/^[1234](\.)? Quartal/i},m={any:[/1/i,/2/i,/3/i,/4/i]},k={narrow:/^[jfmasond]/i,abbreviated:/^(j[aä]n|feb|mär[z]?|apr|mai|jun[i]?|jul[i]?|aug|sep|okt|nov|dez)\.?/i,wide:/^(januar|februar|märz|april|mai|juni|juli|august|september|oktober|november|dezember)/i},c={narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^j[aä]/i,/^f/i,/^mär/i,/^ap/i,/^mai/i,/^jun/i,/^jul/i,/^au/i,/^s/i,/^o/i,/^n/i,/^d/i]},y={narrow:/^[smdmf]/i,short:/^(so|mo|di|mi|do|fr|sa)/i,abbreviated:/^(son?|mon?|die?|mit?|don?|fre?|sam?)\.?/i,wide:/^(sonntag|montag|dienstag|mittwoch|donnerstag|freitag|samstag)/i},g={any:[/^so/i,/^mo/i,/^di/i,/^mi/i,/^do/i,/^f/i,/^sa/i]},d={narrow:/^(vm\.?|nm\.?|Mitternacht|Mittag|morgens|nachm\.?|abends|nachts)/i,abbreviated:/^(vorm\.?|nachm\.?|Mitternacht|Mittag|morgens|nachm\.?|abends|nachts)/i,wide:/^(vormittags|nachmittags|Mitternacht|Mittag|morgens|nachmittags|abends|nachts)/i},p={any:{am:/^v/i,pm:/^n/i,midnight:/^Mitte/i,noon:/^Mitta/i,morning:/morgens/i,afternoon:/nachmittags/i,evening:/abends/i,night:/nachts/i}},u={ordinalNumber:v({matchPattern:_,parsePattern:P,valueCallback:function J(U){return parseInt(U)}}),era:A({matchPatterns:F,defaultMatchWidth:"wide",parsePatterns:b,defaultParseWidth:"any"}),quarter:A({matchPatterns:h,defaultMatchWidth:"wide",parsePatterns:m,defaultParseWidth:"any",valueCallback:function J(U){return U+1}}),month:A({matchPatterns:k,defaultMatchWidth:"wide",parsePatterns:c,defaultParseWidth:"any"}),day:A({matchPatterns:y,defaultMatchWidth:"wide",parsePatterns:g,defaultParseWidth:"any"}),dayPeriod:A({matchPatterns:d,defaultMatchWidth:"wide",parsePatterns:p,defaultParseWidth:"any"})};function T(J){return function(U,C){var Z=C!==null&&C!==void 0&&C.context?String(C.context):"standalone",X;if(Z==="formatting"&&J.formattingValues){var Y=J.defaultFormattingWidth||J.defaultWidth,I=C!==null&&C!==void 0&&C.width?String(C.width):Y;X=J.formattingValues[I]||J.formattingValues[Y]}else{var O=J.defaultWidth,x=C!==null&&C!==void 0&&C.width?String(C.width):J.defaultWidth;X=J.values[x]||J.values[O]}var Q=J.argumentCallback?J.argumentCallback(U):U;return X[Q]}}var l={narrow:["v.Chr.","n.Chr."],abbreviated:["v.Chr.","n.Chr."],wide:["vor Christus","nach Christus"]},i={narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["1. Quartal","2. Quartal","3. Quartal","4. Quartal"]},N={narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],abbreviated:["J\xE4n","Feb","M\xE4r","Apr","Mai","Jun","Jul","Aug","Sep","Okt","Nov","Dez"],wide:["J\xE4nner","Februar","M\xE4rz","April","Mai","Juni","Juli","August","September","Oktober","November","Dezember"]},n={narrow:N.narrow,abbreviated:["J\xE4n.","Feb.","M\xE4rz","Apr.","Mai","Juni","Juli","Aug.","Sep.","Okt.","Nov.","Dez."],wide:N.wide},s={narrow:["S","M","D","M","D","F","S"],short:["So","Mo","Di","Mi","Do","Fr","Sa"],abbreviated:["So.","Mo.","Di.","Mi.","Do.","Fr.","Sa."],wide:["Sonntag","Montag","Dienstag","Mittwoch","Donnerstag","Freitag","Samstag"]},o={narrow:{am:"vm.",pm:"nm.",midnight:"Mitternacht",noon:"Mittag",morning:"Morgen",afternoon:"Nachm.",evening:"Abend",night:"Nacht"},abbreviated:{am:"vorm.",pm:"nachm.",midnight:"Mitternacht",noon:"Mittag",morning:"Morgen",afternoon:"Nachmittag",evening:"Abend",night:"Nacht"},wide:{am:"vormittags",pm:"nachmittags",midnight:"Mitternacht",noon:"Mittag",morning:"Morgen",afternoon:"Nachmittag",evening:"Abend",night:"Nacht"}},r={narrow:{am:"vm.",pm:"nm.",midnight:"Mitternacht",noon:"Mittag",morning:"morgens",afternoon:"nachm.",evening:"abends",night:"nachts"},abbreviated:{am:"vorm.",pm:"nachm.",midnight:"Mitternacht",noon:"Mittag",morning:"morgens",afternoon:"nachmittags",evening:"abends",night:"nachts"},wide:{am:"vormittags",pm:"nachmittags",midnight:"Mitternacht",noon:"Mittag",morning:"morgens",afternoon:"nachmittags",evening:"abends",night:"nachts"}},e=function J(U){var C=Number(U);return C+"."},a={ordinalNumber:e,era:T({values:l,defaultWidth:"wide"}),quarter:T({values:i,defaultWidth:"wide",argumentCallback:function J(U){return U-1}}),month:T({values:N,formattingValues:n,defaultWidth:"wide"}),day:T({values:s,defaultWidth:"wide"}),dayPeriod:T({values:o,defaultWidth:"wide",formattingValues:r,defaultFormattingWidth:"wide"})},t={code:"de-AT",formatDistance:M,formatLong:f,formatRelative:j,localize:a,match:u,options:{weekStartsOn:1,firstWeekContainsDate:4}};window.dateFns=K(K({},window.dateFns),{},{locale:K(K({},(G=window.dateFns)===null||G===void 0?void 0:G.locale),{},{deAT:t})})})();

//# debugId=5191BAE409FC3E2264756e2164756e21
