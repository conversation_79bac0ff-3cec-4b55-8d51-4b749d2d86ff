{"name": "hsl-to-rgb-for-reals", "version": "1.1.1", "description": "simple HSL to RGB converter", "main": "converter.js", "scripts": {"test": "mocha"}, "repository": {"type": "git", "url": "https://github.com/davidmarkclements/hsl_rgb_converter/"}, "keywords": ["hsl", "rgb", "color", "converter", "convert", "hue", "saturation", "light"], "license": "ISC", "bugs": {"url": "https://github.com/kayellpeee/hsl_rgb_converter/issues"}, "homepage": "https://github.com/davidmarkclements/hsl_rgb_converter/", "devDependencies": {"chai": "^4.2.0", "mocha": "^6.2.0"}}