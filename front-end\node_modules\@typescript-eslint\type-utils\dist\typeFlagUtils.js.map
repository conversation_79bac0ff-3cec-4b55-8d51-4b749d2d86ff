{"version": 3, "file": "typeFlagUtils.js", "sourceRoot": "", "sources": ["../src/typeFlagUtils.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAQA,oCAOC;AAWD,sCAcC;AAxCD,sDAAwC;AACxC,+CAAiC;AAEjC,MAAM,cAAc,GAAG,EAAE,CAAC,SAAS,CAAC,GAAG,GAAG,EAAE,CAAC,SAAS,CAAC,OAAO,CAAC;AAE/D;;GAEG;AACH,SAAgB,YAAY,CAAC,IAAa;IACxC,wGAAwG;IACxG,IAAI,KAAK,GAAiB,CAAC,CAAC;IAC5B,KAAK,MAAM,CAAC,IAAI,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,CAAC;QAC7C,KAAK,IAAI,CAAC,CAAC,KAAK,CAAC;IACnB,CAAC;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AAED;;;;;;;;GAQG;AACH,SAAgB,aAAa,CAC3B,IAAa,EACb,YAA0B;AAC1B,4EAA4E;AAC5E,UAAoB;IAEpB,MAAM,KAAK,GAAG,YAAY,CAAC,IAAI,CAAC,CAAC;IAEjC,wEAAwE;IACxE,IAAI,UAAU,IAAI,KAAK,GAAG,cAAc,EAAE,CAAC;QACzC,OAAO,IAAI,CAAC;IACd,CAAC;IAED,OAAO,CAAC,KAAK,GAAG,YAAY,CAAC,KAAK,CAAC,CAAC;AACtC,CAAC"}