{"name": "strip-json-comments", "version": "3.1.1", "description": "Strip comments from JSON. Lets you use comments in your JSON files!", "license": "MIT", "repository": "sindresorhus/strip-json-comments", "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "engines": {"node": ">=8"}, "scripts": {"test": "xo && ava && tsd", "bench": "matcha benchmark.js"}, "files": ["index.js", "index.d.ts"], "keywords": ["json", "strip", "comments", "remove", "delete", "trim", "multiline", "parse", "config", "configuration", "settings", "util", "env", "environment", "jsonc"], "devDependencies": {"ava": "^1.4.1", "matcha": "^0.7.0", "tsd": "^0.7.2", "xo": "^0.24.0"}}