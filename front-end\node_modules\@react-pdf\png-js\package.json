{"name": "@react-pdf/png-js", "description": "A PNG decoder in JS", "version": "3.0.0", "license": "MIT", "type": "module", "main": "./lib/png-js.js", "browser": {"./lib/png-js.js": "./lib/png-js.browser.js"}, "repository": {"type": "git", "url": "https://github.com/diegomura/react-pdf.git", "directory": "packages/png-js"}, "author": {"name": "Devon Govett", "email": "<EMAIL>", "url": "http://badassjs.com/"}, "scripts": {"build": "rimraf ./lib && rollup -c", "watch": "rimraf ./lib && rollup -c -w"}, "files": ["lib"], "dependencies": {"browserify-zlib": "^0.2.0"}}