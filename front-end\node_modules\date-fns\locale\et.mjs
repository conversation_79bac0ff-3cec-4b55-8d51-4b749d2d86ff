import { formatDistance } from "./et/_lib/formatDistance.mjs";
import { formatLong } from "./et/_lib/formatLong.mjs";
import { formatRelative } from "./et/_lib/formatRelative.mjs";
import { localize } from "./et/_lib/localize.mjs";
import { match } from "./et/_lib/match.mjs";

/**
 * @category Locales
 * @summary Estonian locale.
 * @language Estonian
 * @iso-639-2 est
 * <AUTHOR> [@HansenPriit](https://github.com/priithansen)
 */
export const et = {
  code: "et",
  formatDistance: formatDistance,
  formatLong: formatLong,
  formatRelative: formatRelative,
  localize: localize,
  match: match,
  options: {
    weekStartsOn: 1 /* Monday */,
    firstWeekContainsDate: 4,
  },
};

// Fallback for modularized imports:
export default et;
