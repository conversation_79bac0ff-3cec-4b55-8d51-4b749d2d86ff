var q=function(H){return q=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(G){return typeof G}:function(G){return G&&typeof Symbol=="function"&&G.constructor===Symbol&&G!==Symbol.prototype?"symbol":typeof G},q(H)},z=function(H,G){var J=Object.keys(H);if(Object.getOwnPropertySymbols){var Z=Object.getOwnPropertySymbols(H);G&&(Z=Z.filter(function(K){return Object.getOwnPropertyDescriptor(H,K).enumerable})),J.push.apply(J,Z)}return J},x=function(H){for(var G=1;G<arguments.length;G++){var J=arguments[G]!=null?arguments[G]:{};G%2?z(Object(J),!0).forEach(function(Z){C0(H,Z,J[Z])}):Object.getOwnPropertyDescriptors?Object.defineProperties(H,Object.getOwnPropertyDescriptors(J)):z(Object(J)).forEach(function(Z){Object.defineProperty(H,Z,Object.getOwnPropertyDescriptor(J,Z))})}return H},C0=function(H,G,J){if(G=A0(G),G in H)Object.defineProperty(H,G,{value:J,enumerable:!0,configurable:!0,writable:!0});else H[G]=J;return H},A0=function(H){var G=U0(H,"string");return q(G)=="symbol"?G:String(G)},U0=function(H,G){if(q(H)!="object"||!H)return H;var J=H[Symbol.toPrimitive];if(J!==void 0){var Z=J.call(H,G||"default");if(q(Z)!="object")return Z;throw new TypeError("@@toPrimitive must return a primitive value.")}return(G==="string"?String:Number)(H)};(function(H){var G=Object.defineProperty,J=function C(U,A){for(var B in A)G(U,B,{get:A[B],enumerable:!0,configurable:!0,set:function X(Y){return A[B]=function(){return Y}}})},Z={lessThanXSeconds:{one:"\u0628\u0649\u0631 \u0633\u0649\u0643\u06C7\u0646\u062A \u0626\u0649\u0686\u0649\u062F\u06D5",other:"\u0633\u0649\u0643\u06C7\u0646\u062A \u0626\u0649\u0686\u0649\u062F\u06D5 {{count}}"},xSeconds:{one:"\u0628\u0649\u0631 \u0633\u0649\u0643\u06C7\u0646\u062A",other:"\u0633\u0649\u0643\u06C7\u0646\u062A {{count}}"},halfAMinute:"\u064A\u0649\u0631\u0649\u0645 \u0645\u0649\u0646\u06C7\u062A",lessThanXMinutes:{one:"\u0628\u0649\u0631 \u0645\u0649\u0646\u06C7\u062A \u0626\u0649\u0686\u0649\u062F\u06D5",other:"\u0645\u0649\u0646\u06C7\u062A \u0626\u0649\u0686\u0649\u062F\u06D5 {{count}}"},xMinutes:{one:"\u0628\u0649\u0631 \u0645\u0649\u0646\u06C7\u062A",other:"\u0645\u0649\u0646\u06C7\u062A {{count}}"},aboutXHours:{one:"\u062A\u06D5\u062E\u0645\u0649\u0646\u06D5\u0646 \u0628\u0649\u0631 \u0633\u0627\u0626\u06D5\u062A",other:"\u0633\u0627\u0626\u06D5\u062A {{count}} \u062A\u06D5\u062E\u0645\u0649\u0646\u06D5\u0646"},xHours:{one:"\u0628\u0649\u0631 \u0633\u0627\u0626\u06D5\u062A",other:"\u0633\u0627\u0626\u06D5\u062A {{count}}"},xDays:{one:"\u0628\u0649\u0631 \u0643\u06C8\u0646",other:"\u0643\u06C8\u0646 {{count}}"},aboutXWeeks:{one:"\u062A\u06D5\u062E\u0645\u0649\u0646\u06D5\u0646 \u0628\u0649\u0631\u06BE\u06D5\u067E\u062A\u06D5",other:"\u06BE\u06D5\u067E\u062A\u06D5 {{count}} \u062A\u06D5\u062E\u0645\u0649\u0646\u06D5\u0646"},xWeeks:{one:"\u0628\u0649\u0631\u06BE\u06D5\u067E\u062A\u06D5",other:"\u06BE\u06D5\u067E\u062A\u06D5 {{count}}"},aboutXMonths:{one:"\u062A\u06D5\u062E\u0645\u0649\u0646\u06D5\u0646 \u0628\u0649\u0631 \u0626\u0627\u064A",other:"\u0626\u0627\u064A {{count}} \u062A\u06D5\u062E\u0645\u0649\u0646\u06D5\u0646"},xMonths:{one:"\u0628\u0649\u0631 \u0626\u0627\u064A",other:"\u0626\u0627\u064A {{count}}"},aboutXYears:{one:"\u062A\u06D5\u062E\u0645\u0649\u0646\u06D5\u0646 \u0628\u0649\u0631 \u064A\u0649\u0644",other:"\u064A\u0649\u0644 {{count}} \u062A\u06D5\u062E\u0645\u0649\u0646\u06D5\u0646"},xYears:{one:"\u0628\u0649\u0631 \u064A\u0649\u0644",other:"\u064A\u0649\u0644 {{count}}"},overXYears:{one:"\u0628\u0649\u0631 \u064A\u0649\u0644\u062F\u0649\u0646 \u0626\u0627\u0631\u062A\u06C7\u0642",other:"\u064A\u0649\u0644\u062F\u0649\u0646 \u0626\u0627\u0631\u062A\u06C7\u0642 {{count}}"},almostXYears:{one:"\u0626\u0627\u0633\u0627\u0633\u06D5\u0646 \u0628\u0649\u0631 \u064A\u0649\u0644",other:"\u064A\u0649\u0644 {{count}} \u0626\u0627\u0633\u0627\u0633\u06D5\u0646"}},K=function C(U,A,B){var X,Y=Z[U];if(typeof Y==="string")X=Y;else if(A===1)X=Y.one;else X=Y.other.replace("{{count}}",String(A));if(B!==null&&B!==void 0&&B.addSuffix)if(B.comparison&&B.comparison>0)return X;else return X+" \u0628\u0648\u0644\u062F\u0649";return X};function N(C){return function(){var U=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},A=U.width?String(U.width):C.defaultWidth,B=C.formats[A]||C.formats[C.defaultWidth];return B}}var R={full:"EEEE, MMMM do, y",long:"MMMM do, y",medium:"MMM d, y",short:"MM/dd/yyyy"},$={full:"h:mm:ss a zzzz",long:"h:mm:ss a z",medium:"h:mm:ss a",short:"h:mm a"},S={full:"{{date}} '\u062F\u06D5' {{time}}",long:"{{date}} '\u062F\u06D5' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},M={date:N({formats:R,defaultWidth:"full"}),time:N({formats:$,defaultWidth:"full"}),dateTime:N({formats:S,defaultWidth:"full"})},L={lastWeek:"'\u0626\u200D\u06C6\u062A\u0643\u06D5\u0646' eeee '\u062F\u06D5' p",yesterday:"'\u062A\u06C8\u0646\u06C8\u06AF\u06C8\u0646 \u062F\u06D5' p",today:"'\u0628\u06C8\u06AF\u06C8\u0646 \u062F\u06D5' p",tomorrow:"'\u0626\u06D5\u062A\u06D5 \u062F\u06D5' p",nextWeek:"eeee '\u062F\u06D5' p",other:"P"},V=function C(U,A,B,X){return L[U]};function O(C){return function(U,A){var B=A!==null&&A!==void 0&&A.context?String(A.context):"standalone",X;if(B==="formatting"&&C.formattingValues){var Y=C.defaultFormattingWidth||C.defaultWidth,E=A!==null&&A!==void 0&&A.width?String(A.width):Y;X=C.formattingValues[E]||C.formattingValues[Y]}else{var I=C.defaultWidth,D=A!==null&&A!==void 0&&A.width?String(A.width):C.defaultWidth;X=C.values[D]||C.values[I]}var T=C.argumentCallback?C.argumentCallback(U):U;return X[T]}}var j={narrow:["\u0628","\u0643"],abbreviated:["\u0628","\u0643"],wide:["\u0645\u0649\u064A\u0644\u0627\u062F\u0649\u062F\u0649\u0646 \u0628\u06C7\u0631\u06C7\u0646","\u0645\u0649\u064A\u0644\u0627\u062F\u0649\u062F\u0649\u0646 \u0643\u0649\u064A\u0649\u0646"]},f={narrow:["1","2","3","4"],abbreviated:["1","2","3","4"],wide:["\u0628\u0649\u0631\u0649\u0646\u062C\u0649 \u0686\u0627\u0631\u06D5\u0643","\u0626\u0649\u0643\u0643\u0649\u0646\u062C\u0649 \u0686\u0627\u0631\u06D5\u0643","\u0626\u06C8\u0686\u0649\u0646\u062C\u0649 \u0686\u0627\u0631\u06D5\u0643","\u062A\u06C6\u062A\u0649\u0646\u062C\u0649 \u0686\u0627\u0631\u06D5\u0643"]},v={narrow:["\u064A","\u0641","\u0645","\u0627","\u0645","\u0649","\u0649","\u0627","\u0633","\u06C6","\u0646","\u062F"],abbreviated:["\u064A\u0627\u0646\u06CB\u0627\u0631","\u0641\u06D0\u06CB\u0649\u0631\u0627\u0644","\u0645\u0627\u0631\u062A","\u0626\u0627\u067E\u0631\u0649\u0644","\u0645\u0627\u064A","\u0626\u0649\u064A\u06C7\u0646","\u0626\u0649\u064A\u0648\u0644","\u0626\u0627\u06CB\u063A\u06C7\u0633\u062A","\u0633\u0649\u0646\u062A\u06D5\u0628\u0649\u0631","\u0626\u06C6\u0643\u062A\u06D5\u0628\u0649\u0631","\u0646\u0648\u064A\u0627\u0628\u0649\u0631","\u062F\u0649\u0643\u0627\u0628\u0649\u0631"],wide:["\u064A\u0627\u0646\u06CB\u0627\u0631","\u0641\u06D0\u06CB\u0649\u0631\u0627\u0644","\u0645\u0627\u0631\u062A","\u0626\u0627\u067E\u0631\u0649\u0644","\u0645\u0627\u064A","\u0626\u0649\u064A\u06C7\u0646","\u0626\u0649\u064A\u0648\u0644","\u0626\u0627\u06CB\u063A\u06C7\u0633\u062A","\u0633\u0649\u0646\u062A\u06D5\u0628\u0649\u0631","\u0626\u06C6\u0643\u062A\u06D5\u0628\u0649\u0631","\u0646\u0648\u064A\u0627\u0628\u0649\u0631","\u062F\u0649\u0643\u0627\u0628\u0649\u0631"]},P={narrow:["\u064A","\u062F","\u0633","\u0686","\u067E","\u062C","\u0634"],short:["\u064A","\u062F","\u0633","\u0686","\u067E","\u062C","\u0634"],abbreviated:["\u064A\u06D5\u0643\u0634\u06D5\u0646\u0628\u06D5","\u062F\u06C8\u0634\u06D5\u0646\u0628\u06D5","\u0633\u06D5\u064A\u0634\u06D5\u0646\u0628\u06D5","\u0686\u0627\u0631\u0634\u06D5\u0646\u0628\u06D5","\u067E\u06D5\u064A\u0634\u06D5\u0646\u0628\u06D5","\u062C\u06C8\u0645\u06D5","\u0634\u06D5\u0646\u0628\u06D5"],wide:["\u064A\u06D5\u0643\u0634\u06D5\u0646\u0628\u06D5","\u062F\u06C8\u0634\u06D5\u0646\u0628\u06D5","\u0633\u06D5\u064A\u0634\u06D5\u0646\u0628\u06D5","\u0686\u0627\u0631\u0634\u06D5\u0646\u0628\u06D5","\u067E\u06D5\u064A\u0634\u06D5\u0646\u0628\u06D5","\u062C\u06C8\u0645\u06D5","\u0634\u06D5\u0646\u0628\u06D5"]},w={narrow:{am:"\u0626\u06D5",pm:"\u0686",midnight:"\u0643",noon:"\u0686",morning:"\u0626\u06D5\u062A\u0649\u06AF\u06D5\u0646",afternoon:"\u0686\u06C8\u0634\u062A\u0649\u0646 \u0643\u0649\u064A\u0649\u0646",evening:"\u0626\u0627\u062E\u0634\u0649\u0645",night:"\u0643\u0649\u0686\u06D5"},abbreviated:{am:"\u0626\u06D5",pm:"\u0686",midnight:"\u0643",noon:"\u0686",morning:"\u0626\u06D5\u062A\u0649\u06AF\u06D5\u0646",afternoon:"\u0686\u06C8\u0634\u062A\u0649\u0646 \u0643\u0649\u064A\u0649\u0646",evening:"\u0626\u0627\u062E\u0634\u0649\u0645",night:"\u0643\u0649\u0686\u06D5"},wide:{am:"\u0626\u06D5",pm:"\u0686",midnight:"\u0643",noon:"\u0686",morning:"\u0626\u06D5\u062A\u0649\u06AF\u06D5\u0646",afternoon:"\u0686\u06C8\u0634\u062A\u0649\u0646 \u0643\u0649\u064A\u0649\u0646",evening:"\u0626\u0627\u062E\u0634\u0649\u0645",night:"\u0643\u0649\u0686\u06D5"}},_={narrow:{am:"\u0626\u06D5",pm:"\u0686",midnight:"\u0643",noon:"\u0686",morning:"\u0626\u06D5\u062A\u0649\u06AF\u06D5\u0646\u062F\u06D5",afternoon:"\u0686\u06C8\u0634\u062A\u0649\u0646 \u0643\u0649\u064A\u0649\u0646",evening:"\u0626\u0627\u062E\u0634\u0627\u0645\u062F\u0627",night:"\u0643\u0649\u0686\u0649\u062F\u06D5"},abbreviated:{am:"\u0626\u06D5",pm:"\u0686",midnight:"\u0643",noon:"\u0686",morning:"\u0626\u06D5\u062A\u0649\u06AF\u06D5\u0646\u062F\u06D5",afternoon:"\u0686\u06C8\u0634\u062A\u0649\u0646 \u0643\u0649\u064A\u0649\u0646",evening:"\u0626\u0627\u062E\u0634\u0627\u0645\u062F\u0627",night:"\u0643\u0649\u0686\u0649\u062F\u06D5"},wide:{am:"\u0626\u06D5",pm:"\u0686",midnight:"\u0643",noon:"\u0686",morning:"\u0626\u06D5\u062A\u0649\u06AF\u06D5\u0646\u062F\u06D5",afternoon:"\u0686\u06C8\u0634\u062A\u0649\u0646 \u0643\u0649\u064A\u0649\u0646",evening:"\u0626\u0627\u062E\u0634\u0627\u0645\u062F\u0627",night:"\u0643\u0649\u0686\u0649\u062F\u06D5"}},F=function C(U,A){return String(U)},b={ordinalNumber:F,era:O({values:j,defaultWidth:"wide"}),quarter:O({values:f,defaultWidth:"wide",argumentCallback:function C(U){return U-1}}),month:O({values:v,defaultWidth:"wide"}),day:O({values:P,defaultWidth:"wide"}),dayPeriod:O({values:w,defaultWidth:"wide",formattingValues:_,defaultFormattingWidth:"wide"})};function Q(C){return function(U){var A=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},B=A.width,X=B&&C.matchPatterns[B]||C.matchPatterns[C.defaultMatchWidth],Y=U.match(X);if(!Y)return null;var E=Y[0],I=B&&C.parsePatterns[B]||C.parsePatterns[C.defaultParseWidth],D=Array.isArray(I)?m(I,function(W){return W.test(E)}):k(I,function(W){return W.test(E)}),T;T=C.valueCallback?C.valueCallback(D):D,T=A.valueCallback?A.valueCallback(T):T;var t=U.slice(E.length);return{value:T,rest:t}}}var k=function C(U,A){for(var B in U)if(Object.prototype.hasOwnProperty.call(U,B)&&A(U[B]))return B;return},m=function C(U,A){for(var B=0;B<U.length;B++)if(A(U[B]))return B;return};function h(C){return function(U){var A=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},B=U.match(C.matchPattern);if(!B)return null;var X=B[0],Y=U.match(C.parsePattern);if(!Y)return null;var E=C.valueCallback?C.valueCallback(Y[0]):Y[0];E=A.valueCallback?A.valueCallback(E):E;var I=U.slice(X.length);return{value:E,rest:I}}}var u=/^(\d+)(th|st|nd|rd)?/i,c=/\d+/i,y={narrow:/^(ب|ك)/i,wide:/^(مىيلادىدىن بۇرۇن|مىيلادىدىن كىيىن)/i},p={any:[/^بۇرۇن/i,/^كىيىن/i]},g={narrow:/^[1234]/i,abbreviated:/^چ[1234]/i,wide:/^چارەك [1234]/i},d={any:[/1/i,/2/i,/3/i,/4/i]},l={narrow:/^[يفمئامئ‍ئاسۆند]/i,abbreviated:/^(يانۋار|فېۋىرال|مارت|ئاپرىل|ماي|ئىيۇن|ئىيول|ئاۋغۇست|سىنتەبىر|ئۆكتەبىر|نويابىر|دىكابىر)/i,wide:/^(يانۋار|فېۋىرال|مارت|ئاپرىل|ماي|ئىيۇن|ئىيول|ئاۋغۇست|سىنتەبىر|ئۆكتەبىر|نويابىر|دىكابىر)/i},i={narrow:[/^ي/i,/^ف/i,/^م/i,/^ا/i,/^م/i,/^ى‍/i,/^ى‍/i,/^ا‍/i,/^س/i,/^ۆ/i,/^ن/i,/^د/i],any:[/^يان/i,/^فېۋ/i,/^مار/i,/^ئاپ/i,/^ماي/i,/^ئىيۇن/i,/^ئىيول/i,/^ئاۋ/i,/^سىن/i,/^ئۆك/i,/^نوي/i,/^دىك/i]},n={narrow:/^[دسچپجشي]/i,short:/^(يە|دۈ|سە|چا|پە|جۈ|شە)/i,abbreviated:/^(يە|دۈ|سە|چا|پە|جۈ|شە)/i,wide:/^(يەكشەنبە|دۈشەنبە|سەيشەنبە|چارشەنبە|پەيشەنبە|جۈمە|شەنبە)/i},s={narrow:[/^ي/i,/^د/i,/^س/i,/^چ/i,/^پ/i,/^ج/i,/^ش/i],any:[/^ي/i,/^د/i,/^س/i,/^چ/i,/^پ/i,/^ج/i,/^ش/i]},o={narrow:/^(ئە|چ|ك|چ|(دە|ئەتىگەن) ( ئە‍|چۈشتىن كىيىن|ئاخشىم|كىچە))/i,any:/^(ئە|چ|ك|چ|(دە|ئەتىگەن) ( ئە‍|چۈشتىن كىيىن|ئاخشىم|كىچە))/i},r={any:{am:/^ئە/i,pm:/^چ/i,midnight:/^ك/i,noon:/^چ/i,morning:/ئەتىگەن/i,afternoon:/چۈشتىن كىيىن/i,evening:/ئاخشىم/i,night:/كىچە/i}},a={ordinalNumber:h({matchPattern:u,parsePattern:c,valueCallback:function C(U){return parseInt(U,10)}}),era:Q({matchPatterns:y,defaultMatchWidth:"wide",parsePatterns:p,defaultParseWidth:"any"}),quarter:Q({matchPatterns:g,defaultMatchWidth:"wide",parsePatterns:d,defaultParseWidth:"any",valueCallback:function C(U){return U+1}}),month:Q({matchPatterns:l,defaultMatchWidth:"wide",parsePatterns:i,defaultParseWidth:"any"}),day:Q({matchPatterns:n,defaultMatchWidth:"wide",parsePatterns:s,defaultParseWidth:"any"}),dayPeriod:Q({matchPatterns:o,defaultMatchWidth:"any",parsePatterns:r,defaultParseWidth:"any"})},e={code:"ug",formatDistance:K,formatLong:M,formatRelative:V,localize:b,match:a,options:{weekStartsOn:0,firstWeekContainsDate:1}};window.dateFns=x(x({},window.dateFns),{},{locale:x(x({},(H=window.dateFns)===null||H===void 0?void 0:H.locale),{},{ug:e})})})();

//# debugId=3D349C5952D57ED664756e2164756e21
