{"version": 3, "file": "getMemberHeadLoc.js", "sourceRoot": "", "sources": ["../../src/util/getMemberHeadLoc.ts"], "names": [], "mappings": ";;AA4BA,4CAqCC;AAaD,kEA6BC;AA1GD,wEAG+C;AAE/C;;;;;;;;;;;;;;;;;;;;;GAqBG;AACH,SAAgB,gBAAgB,CAC9B,UAAyC,EACzC,IAIyC;IAEzC,IAAI,KAAwB,CAAC;IAE7B,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QACjC,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC;IACzB,CAAC;SAAM,CAAC;QACN,MAAM,aAAa,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QAClE,MAAM,SAAS,GAAG,IAAA,yBAAU,EAC1B,UAAU,CAAC,aAAa,CAAC,aAAa,CAAC,EACvC,gCAAiB,CAAC,YAAY,CAAC,OAAO,EAAE,gBAAgB,CAAC,CAC1D,CAAC;QACF,KAAK,GAAG,SAAS,CAAC,GAAG,CAAC,KAAK,CAAC;IAC9B,CAAC;IAED,IAAI,GAAsB,CAAC;IAE3B,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;QACnB,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC;IACzB,CAAC;SAAM,CAAC;QACN,MAAM,cAAc,GAAG,IAAA,yBAAU,EAC/B,UAAU,CAAC,aAAa,CAAC,IAAI,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,KAAK,KAAK,GAAG,CAAC,EAChE,gCAAiB,CAAC,YAAY,CAAC,GAAG,EAAE,IAAI,CAAC,IAAI,CAAC,CAC/C,CAAC;QACF,GAAG,GAAG,cAAc,CAAC,GAAG,CAAC,GAAG,CAAC;IAC/B,CAAC;IAED,OAAO;QACL,KAAK,EAAE,eAAe,CAAC,KAAK,CAAC;QAC7B,GAAG,EAAE,eAAe,CAAC,GAAG,CAAC;KAC1B,CAAC;AACJ,CAAC;AAED;;;;;;;;;;GAUG;AACH,SAAgB,2BAA2B,CACzC,UAAyC,EACzC,IAAkC,EAClC,QAAgB;IAEhB,8DAA8D;IAC9D,4BAA4B;IAE5B,IAAI,KAAwB,CAAC;IAE7B,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QACjC,KAAK,GAAG,eAAe,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;IAC1C,CAAC;SAAM,CAAC;QACN,MAAM,aAAa,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QAClE,MAAM,SAAS,GAAG,IAAA,yBAAU,EAC1B,UAAU,CAAC,aAAa,CAAC,aAAa,CAAC,EACvC,gCAAiB,CAAC,YAAY,CAAC,OAAO,EAAE,gBAAgB,CAAC,CAC1D,CAAC;QACF,KAAK,GAAG,eAAe,CAAC,SAAS,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;IAC/C,CAAC;IAED,MAAM,GAAG,GAAG,UAAU,CAAC,eAAe,CACpC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,MAAM,CAC1C,CAAC;IAEF,OAAO;QACL,KAAK;QACL,GAAG;KACJ,CAAC;AACJ,CAAC"}