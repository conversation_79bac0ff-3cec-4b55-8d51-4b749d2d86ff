{"version": 3, "file": "max-params.js", "sourceRoot": "", "sources": ["../../src/rules/max-params.ts"], "names": [], "mappings": ";;AACA,oDAA0D;AAM1D,kCAAqC;AACrC,iEAA8D;AAS9D,MAAM,QAAQ,GAAG,IAAA,qCAAiB,EAAC,YAAY,CAAC,CAAC;AAKjD,kBAAe,IAAA,iBAAU,EAAsB;IAC7C,IAAI,EAAE,YAAY;IAClB,IAAI,EAAE;QACJ,IAAI,EAAE,YAAY;QAClB,IAAI,EAAE;YACJ,WAAW,EACT,gEAAgE;YAClE,eAAe,EAAE,IAAI;SACtB;QACD,MAAM,EAAE;YACN;gBACE,IAAI,EAAE,QAAQ;gBACd,UAAU,EAAE;oBACV,GAAG,EAAE;wBACH,WAAW,EACT,yDAAyD;wBAC3D,IAAI,EAAE,SAAS;wBACf,OAAO,EAAE,CAAC;qBACX;oBACD,OAAO,EAAE;wBACP,WAAW,EACT,sEAAsE;wBACxE,IAAI,EAAE,SAAS;wBACf,OAAO,EAAE,CAAC;qBACX;oBACD,aAAa,EAAE;wBACb,WAAW,EACT,gEAAgE;wBAClE,IAAI,EAAE,SAAS;qBAChB;iBACF;gBACD,oBAAoB,EAAE,KAAK;aAC5B;SACF;QACD,QAAQ,EAAE,QAAQ,CAAC,IAAI,CAAC,QAAQ;KACjC;IACD,cAAc,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,aAAa,EAAE,KAAK,EAAE,CAAC;IAElD,MAAM,CAAC,OAAO,EAAE,CAAC,EAAE,aAAa,EAAE,CAAC;QACjC,MAAM,SAAS,GAAG,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QAE3C,IAAI,aAAa,KAAK,IAAI,EAAE,CAAC;YAC3B,OAAO,SAAS,CAAC;QACnB,CAAC;QAED,MAAM,mBAAmB,GAAG,CAAyB,IAAO,EAAK,EAAE;YACjE,IACE,IAAI,CAAC,MAAM,CAAC,MAAM,KAAK,CAAC;gBACxB,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,sBAAc,CAAC,UAAU;gBACjD,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,MAAM;gBAC9B,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,cAAc,EAAE,cAAc,CAAC,IAAI;oBAChD,sBAAc,CAAC,aAAa,EAC9B,CAAC;gBACD,OAAO,IAAI,CAAC;YACd,CAAC;YAED,OAAO;gBACL,GAAG,IAAI;gBACP,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;aAC7B,CAAC;QACJ,CAAC,CAAC;QAEF,MAAM,YAAY,GAAG,CACnB,QAAiC,EACR,EAAE;YAC3B,OAAO,CAAC,IAAO,EAAQ,EAAE;gBACvB,QAAQ,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC,CAAC;YACtC,CAAC,CAAC;QACJ,CAAC,CAAC;QAEF,OAAO;YACL,uBAAuB,EAAE,YAAY,CAAC,SAAS,CAAC,uBAAuB,CAAC;YACxE,mBAAmB,EAAE,YAAY,CAAC,SAAS,CAAC,mBAAmB,CAAC;YAChE,kBAAkB,EAAE,YAAY,CAAC,SAAS,CAAC,kBAAkB,CAAC;SAC/D,CAAC;IACJ,CAAC;CACF,CAAC,CAAC"}