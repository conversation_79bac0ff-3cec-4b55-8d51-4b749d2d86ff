/**
 * React Router DOM v6.26.2
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */
import*as e from"react";import*as t from"react-dom";import{UNSAFE_mapRouteProperties as n,UNSAFE_DataRouterContext as a,UNSAFE_DataRouterStateContext as r,Router as o,UNSAFE_useRoutesImpl as i,UNSAFE_NavigationContext as s,useHref as u,useResolvedPath as l,useLocation as c,useNavigate as f,createPath as d,UNSAFE_useRouteId as m,UNSAFE_RouteContext as h,useMatches as p,useNavigation as w,useBlocker as v}from"react-router";export{AbortedDeferredError,Await,MemoryRouter,Navigate,NavigationType,Outlet,Route,Router,Routes,UNSAFE_DataRouterContext,UNSAFE_DataRouterStateContext,UNSAFE_LocationContext,UNSAFE_NavigationContext,UNSAFE_RouteContext,UNSAFE_useRouteId,createMemoryRouter,createPath,createRoutesFromChildren,createRoutesFromElements,defer,generatePath,isRouteErrorResponse,json,matchPath,matchRoutes,parsePath,redirect,redirectDocument,renderMatches,replace,resolvePath,useActionData,useAsyncError,useAsyncValue,useBlocker,useHref,useInRouterContext,useLoaderData,useLocation,useMatch,useMatches,useNavigate,useNavigation,useNavigationType,useOutlet,useOutletContext,useParams,useResolvedPath,useRevalidator,useRouteError,useRouteLoaderData,useRoutes}from"react-router";import{stripBasename as g,createRouter as y,createBrowserHistory as b,createHashHistory as S,UNSAFE_ErrorResponseImpl as R,UNSAFE_invariant as E,joinPaths as _,IDLE_FETCHER as T,matchPath as L}from"@remix-run/router";export{UNSAFE_ErrorResponseImpl}from"@remix-run/router";const x="application/x-www-form-urlencoded";function C(e){return null!=e&&"string"==typeof e.tagName}function A(e=""){return new URLSearchParams("string"==typeof e||Array.isArray(e)||e instanceof URLSearchParams?e:Object.keys(e).reduce(((t,n)=>{let a=e[n];return t.concat(Array.isArray(a)?a.map((e=>[n,e])):[[n,a]])}),[]))}let F=null;const U=new Set(["application/x-www-form-urlencoded","multipart/form-data","text/plain"]);function k(e){return null==e||U.has(e)?e:null}function N(e,t){let n,a,r,o,i;if(C(s=e)&&"form"===s.tagName.toLowerCase()){let i=e.getAttribute("action");a=i?g(i,t):null,n=e.getAttribute("method")||"get",r=k(e.getAttribute("enctype"))||x,o=new FormData(e)}else if(function(e){return C(e)&&"button"===e.tagName.toLowerCase()}(e)||function(e){return C(e)&&"input"===e.tagName.toLowerCase()}(e)&&("submit"===e.type||"image"===e.type)){let i=e.form;if(null==i)throw new Error('Cannot submit a <button> or <input type="submit"> without a <form>');let s=e.getAttribute("formaction")||i.getAttribute("action");if(a=s?g(s,t):null,n=e.getAttribute("formmethod")||i.getAttribute("method")||"get",r=k(e.getAttribute("formenctype"))||k(i.getAttribute("enctype"))||x,o=new FormData(i,e),!function(){if(null===F)try{new FormData(document.createElement("form"),0),F=!1}catch(e){F=!0}return F}()){let{name:t,type:n,value:a}=e;if("image"===n){let e=t?`${t}.`:"";o.append(`${e}x`,"0"),o.append(`${e}y`,"0")}else t&&o.append(t,a)}}else{if(C(e))throw new Error('Cannot submit element that is not <form>, <button>, or <input type="submit|image">');n="get",a=null,r=x,i=e}var s;return o&&"text/plain"===r&&(i=o,o=void 0),{action:a,method:n.toLowerCase(),encType:r,formData:o,body:i}}try{window.__reactRouterVersion="6"}catch(be){}function P(e,t){return y({basename:t?.basename,future:{...t?.future,v7_prependBasename:!0},history:b({window:t?.window}),hydrationData:t?.hydrationData||O(),routes:e,mapRouteProperties:n,unstable_dataStrategy:t?.unstable_dataStrategy,unstable_patchRoutesOnNavigation:t?.unstable_patchRoutesOnNavigation,window:t?.window}).initialize()}function D(e,t){return y({basename:t?.basename,future:{...t?.future,v7_prependBasename:!0},history:S({window:t?.window}),hydrationData:t?.hydrationData||O(),routes:e,mapRouteProperties:n,unstable_dataStrategy:t?.unstable_dataStrategy,unstable_patchRoutesOnNavigation:t?.unstable_patchRoutesOnNavigation,window:t?.window}).initialize()}function O(){let e=window?.__staticRouterHydrationData;return e&&e.errors&&(e={...e,errors:K(e.errors)}),e}function K(e){if(!e)return null;let t=Object.entries(e),n={};for(let[a,r]of t)if(r&&"RouteErrorResponse"===r.__type)n[a]=new R(r.status,r.statusText,r.data,!0===r.internal);else if(r&&"Error"===r.__type){if(r.__subType){let e=window[r.__subType];if("function"==typeof e)try{let t=new e(r.message);t.stack="",n[a]=t}catch(be){}}if(null==n[a]){let e=new Error(r.message);e.stack="",n[a]=e}}else n[a]=r;return n}const M=e.createContext({isTransitioning:!1}),V=e.createContext(new Map),j=e.startTransition,I=t.flushSync,H=e.useId;function z(e){I?I(e):e()}class B{status="pending";constructor(){this.promise=new Promise(((e,t)=>{this.resolve=t=>{"pending"===this.status&&(this.status="resolved",e(t))},this.reject=e=>{"pending"===this.status&&(this.status="rejected",t(e))}}))}}function $({fallbackElement:t,router:n,future:i}){let[s,u]=e.useState(n.state),[l,c]=e.useState(),[f,d]=e.useState({isTransitioning:!1}),[m,h]=e.useState(),[p,w]=e.useState(),[v,g]=e.useState(),y=e.useRef(new Map),{v7_startTransition:b}=i||{},S=e.useCallback((e=>{b?function(e){j?j(e):e()}(e):e()}),[b]),R=e.useCallback(((e,{deletedFetchers:t,unstable_flushSync:a,unstable_viewTransitionOpts:r})=>{t.forEach((e=>y.current.delete(e))),e.fetchers.forEach(((e,t)=>{void 0!==e.data&&y.current.set(t,e.data)}));let o=null==n.window||null==n.window.document||"function"!=typeof n.window.document.startViewTransition;if(r&&!o){if(a){z((()=>{p&&(m&&m.resolve(),p.skipTransition()),d({isTransitioning:!0,flushSync:!0,currentLocation:r.currentLocation,nextLocation:r.nextLocation})}));let t=n.window.document.startViewTransition((()=>{z((()=>u(e)))}));return t.finished.finally((()=>{z((()=>{h(void 0),w(void 0),c(void 0),d({isTransitioning:!1})}))})),void z((()=>w(t)))}p?(m&&m.resolve(),p.skipTransition(),g({state:e,currentLocation:r.currentLocation,nextLocation:r.nextLocation})):(c(e),d({isTransitioning:!0,flushSync:!1,currentLocation:r.currentLocation,nextLocation:r.nextLocation}))}else a?z((()=>u(e))):S((()=>u(e)))}),[n.window,p,m,y,S]);e.useLayoutEffect((()=>n.subscribe(R)),[n,R]),e.useEffect((()=>{f.isTransitioning&&!f.flushSync&&h(new B)}),[f]),e.useEffect((()=>{if(m&&l&&n.window){let e=l,t=m.promise,a=n.window.document.startViewTransition((async()=>{S((()=>u(e))),await t}));a.finished.finally((()=>{h(void 0),w(void 0),c(void 0),d({isTransitioning:!1})})),w(a)}}),[S,l,m,n.window]),e.useEffect((()=>{m&&l&&s.location.key===l.location.key&&m.resolve()}),[m,p,s.location,l]),e.useEffect((()=>{!f.isTransitioning&&v&&(c(v.state),d({isTransitioning:!0,flushSync:!1,currentLocation:v.currentLocation,nextLocation:v.nextLocation}),g(void 0))}),[f.isTransitioning,v]),e.useEffect((()=>{}),[]);let E=e.useMemo((()=>({createHref:n.createHref,encodeLocation:n.encodeLocation,go:e=>n.navigate(e),push:(e,t,a)=>n.navigate(e,{state:t,preventScrollReset:a?.preventScrollReset}),replace:(e,t,a)=>n.navigate(e,{replace:!0,state:t,preventScrollReset:a?.preventScrollReset})})),[n]),_=n.basename||"/",T=e.useMemo((()=>({router:n,navigator:E,static:!1,basename:_})),[n,E,_]),L=e.useMemo((()=>({v7_relativeSplatPath:n.future.v7_relativeSplatPath})),[n.future.v7_relativeSplatPath]);return e.createElement(e.Fragment,null,e.createElement(a.Provider,{value:T},e.createElement(r.Provider,{value:s},e.createElement(V.Provider,{value:y.current},e.createElement(M.Provider,{value:f},e.createElement(o,{basename:_,location:s.location,navigationType:s.historyAction,navigator:E,future:L},s.initialized||n.future.v7_partialHydration?e.createElement(W,{routes:n.routes,future:n.future,state:s}):t))))),null)}const W=e.memo(Y);function Y({routes:e,future:t,state:n}){return i(e,void 0,n,t)}function J({basename:t,children:n,future:a,window:r}){let i=e.useRef();null==i.current&&(i.current=b({window:r,v5Compat:!0}));let s=i.current,[u,l]=e.useState({action:s.action,location:s.location}),{v7_startTransition:c}=a||{},f=e.useCallback((e=>{c&&j?j((()=>l(e))):l(e)}),[l,c]);return e.useLayoutEffect((()=>s.listen(f)),[s,f]),e.createElement(o,{basename:t,children:n,location:u.location,navigationType:u.action,navigator:s,future:a})}function q({basename:t,children:n,future:a,window:r}){let i=e.useRef();null==i.current&&(i.current=S({window:r,v5Compat:!0}));let s=i.current,[u,l]=e.useState({action:s.action,location:s.location}),{v7_startTransition:c}=a||{},f=e.useCallback((e=>{c&&j?j((()=>l(e))):l(e)}),[l,c]);return e.useLayoutEffect((()=>s.listen(f)),[s,f]),e.createElement(o,{basename:t,children:n,location:u.location,navigationType:u.action,navigator:s,future:a})}function G({basename:t,children:n,future:a,history:r}){let[i,s]=e.useState({action:r.action,location:r.location}),{v7_startTransition:u}=a||{},l=e.useCallback((e=>{u&&j?j((()=>s(e))):s(e)}),[s,u]);return e.useLayoutEffect((()=>r.listen(l)),[r,l]),e.createElement(o,{basename:t,children:n,location:i.location,navigationType:i.action,navigator:r,future:a})}const Q="undefined"!=typeof window&&void 0!==window.document&&void 0!==window.document.createElement,X=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,Z=e.forwardRef((function({onClick:t,relative:n,reloadDocument:a,replace:r,state:o,target:i,to:l,preventScrollReset:c,unstable_viewTransition:f,...d},m){let h,{basename:p}=e.useContext(s),w=!1;if("string"==typeof l&&X.test(l)&&(h=l,Q))try{let e=new URL(window.location.href),t=l.startsWith("//")?new URL(e.protocol+l):new URL(l),n=g(t.pathname,p);t.origin===e.origin&&null!=n?l=n+t.search+t.hash:w=!0}catch(be){}let v=u(l,{relative:n}),y=se(l,{replace:r,state:o,target:i,preventScrollReset:c,relative:n,unstable_viewTransition:f});return e.createElement("a",Object.assign({},d,{href:h||v,onClick:w||a?t:function(e){t&&t(e),e.defaultPrevented||y(e)},ref:m,target:i}))})),ee=e.forwardRef((function({"aria-current":t="page",caseSensitive:n=!1,className:a="",end:o=!1,style:i,to:u,unstable_viewTransition:f,children:d,...m},h){let p=l(u,{relative:m.relative}),w=c(),v=e.useContext(r),{navigator:y,basename:b}=e.useContext(s),S=null!=v&&ye(p)&&!0===f,R=y.encodeLocation?y.encodeLocation(p).pathname:p.pathname,E=w.pathname,_=v&&v.navigation&&v.navigation.location?v.navigation.location.pathname:null;n||(E=E.toLowerCase(),_=_?_.toLowerCase():null,R=R.toLowerCase()),_&&b&&(_=g(_,b)||_);const T="/"!==R&&R.endsWith("/")?R.length-1:R.length;let L,x=E===R||!o&&E.startsWith(R)&&"/"===E.charAt(T),C=null!=_&&(_===R||!o&&_.startsWith(R)&&"/"===_.charAt(R.length)),A={isActive:x,isPending:C,isTransitioning:S},F=x?t:void 0;L="function"==typeof a?a(A):[a,x?"active":null,C?"pending":null,S?"transitioning":null].filter(Boolean).join(" ");let U="function"==typeof i?i(A):i;return e.createElement(Z,Object.assign({},m,{"aria-current":F,className:L,ref:h,style:U,to:u,unstable_viewTransition:f}),"function"==typeof d?d(A):d)})),te=e.forwardRef((({fetcherKey:t,navigate:n,reloadDocument:a,replace:r,state:o,method:i="get",action:s,onSubmit:u,relative:l,preventScrollReset:c,unstable_viewTransition:f,...d},m)=>{let h=fe(),p=de(s,{relative:l}),w="get"===i.toLowerCase()?"get":"post";return e.createElement("form",Object.assign({ref:m,method:w,action:p,onSubmit:a?u:e=>{if(u&&u(e),e.defaultPrevented)return;e.preventDefault();let a=e.nativeEvent.submitter,s=a?.getAttribute("formmethod")||i;h(a||e.currentTarget,{fetcherKey:t,method:s,navigate:n,replace:r,state:o,relative:l,preventScrollReset:c,unstable_viewTransition:f})}},d))}));function ne({getKey:e,storageKey:t}){return we({getKey:e,storageKey:t}),null}var ae=function(e){return e.UseScrollRestoration="useScrollRestoration",e.UseSubmit="useSubmit",e.UseSubmitFetcher="useSubmitFetcher",e.UseFetcher="useFetcher",e.useViewTransitionState="useViewTransitionState",e}(ae||{}),re=function(e){return e.UseFetcher="useFetcher",e.UseFetchers="useFetchers",e.UseScrollRestoration="useScrollRestoration",e}(re||{});function oe(t){let n=e.useContext(a);return n||E(!1),n}function ie(t){let n=e.useContext(r);return n||E(!1),n}function se(t,{target:n,replace:a,state:r,preventScrollReset:o,relative:i,unstable_viewTransition:s}={}){let u=f(),m=c(),h=l(t,{relative:i});return e.useCallback((e=>{if(function(e,t){return!(0!==e.button||t&&"_self"!==t||function(e){return!!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)}(e))}(e,n)){e.preventDefault();let n=void 0!==a?a:d(m)===d(h);u(t,{replace:n,state:r,preventScrollReset:o,relative:i,unstable_viewTransition:s})}}),[m,u,h,a,r,n,t,o,i,s])}function ue(t){let n=e.useRef(A(t)),a=e.useRef(!1),r=c(),o=e.useMemo((()=>function(e,t){let n=A(e);return t&&t.forEach(((e,a)=>{n.has(a)||t.getAll(a).forEach((e=>{n.append(a,e)}))})),n}(r.search,a.current?null:n.current)),[r.search]),i=f(),s=e.useCallback(((e,t)=>{const n=A("function"==typeof e?e(o):e);a.current=!0,i("?"+n,t)}),[i,o]);return[o,s]}let le=0,ce=()=>`__${String(++le)}__`;function fe(){let{router:t}=oe(ae.UseSubmit),{basename:n}=e.useContext(s),a=m();return e.useCallback(((e,r={})=>{!function(){if("undefined"==typeof document)throw new Error("You are calling submit during the server render. Try calling submit within a `useEffect` or callback instead.")}();let{action:o,method:i,encType:s,formData:u,body:l}=N(e,n);if(!1===r.navigate){let e=r.fetcherKey||ce();t.fetch(e,a,r.action||o,{preventScrollReset:r.preventScrollReset,formData:u,body:l,formMethod:r.method||i,formEncType:r.encType||s,unstable_flushSync:r.unstable_flushSync})}else t.navigate(r.action||o,{preventScrollReset:r.preventScrollReset,formData:u,body:l,formMethod:r.method||i,formEncType:r.encType||s,replace:r.replace,state:r.state,fromRouteId:a,unstable_flushSync:r.unstable_flushSync,unstable_viewTransition:r.unstable_viewTransition})}),[t,n,a])}function de(t,{relative:n}={}){let{basename:a}=e.useContext(s),r=e.useContext(h);r||E(!1);let[o]=r.matches.slice(-1),i={...l(t||".",{relative:n})},u=c();if(null==t){i.search=u.search;let e=new URLSearchParams(i.search);e.has("index")&&""===e.get("index")&&(e.delete("index"),i.search=e.toString()?`?${e.toString()}`:"")}return t&&"."!==t||!o.route.index||(i.search=i.search?i.search.replace(/^\?/,"?index&"):"?index"),"/"!==a&&(i.pathname="/"===i.pathname?a:_([a,i.pathname])),d(i)}function me({key:t}={}){let{router:n}=oe(ae.UseFetcher),a=ie(re.UseFetcher),r=e.useContext(V),o=e.useContext(h),i=o.matches[o.matches.length-1]?.route.id;r||E(!1),o||E(!1),null==i&&E(!1);let s=H?H():"",[u,l]=e.useState(t||s);t&&t!==u?l(t):u||l(ce()),e.useEffect((()=>(n.getFetcher(u),()=>{n.deleteFetcher(u)})),[n,u]);let c=e.useCallback(((e,t)=>{i||E(!1),n.fetch(u,i,e,t)}),[u,i,n]),f=fe(),d=e.useCallback(((e,t)=>{f(e,{...t,navigate:!1,fetcherKey:u})}),[u,f]),m=e.useMemo((()=>e.forwardRef(((t,n)=>e.createElement(te,Object.assign({},t,{navigate:!1,fetcherKey:u,ref:n}))))),[u]),p=a.fetchers.get(u)||T,w=r.get(u);return e.useMemo((()=>({Form:m,submit:d,load:c,...p,data:w})),[m,d,c,p,w])}function he(){let e=ie(re.UseFetchers);return Array.from(e.fetchers.entries()).map((([e,t])=>({...t,key:e})))}let pe={};function we({getKey:t,storageKey:n}={}){let{router:a}=oe(ae.UseScrollRestoration),{restoreScrollPosition:r,preventScrollReset:o}=ie(re.UseScrollRestoration),{basename:i}=e.useContext(s),u=c(),l=p(),f=w();e.useEffect((()=>(window.history.scrollRestoration="manual",()=>{window.history.scrollRestoration="auto"})),[]),function(t,n){let{capture:a}=n||{};e.useEffect((()=>{let e=null!=a?{capture:a}:void 0;return window.addEventListener("pagehide",t,e),()=>{window.removeEventListener("pagehide",t,e)}}),[t,a])}(e.useCallback((()=>{if("idle"===f.state){let e=(t?t(u,l):null)||u.key;pe[e]=window.scrollY}try{sessionStorage.setItem(n||"react-router-scroll-positions",JSON.stringify(pe))}catch(e){}window.history.scrollRestoration="auto"}),[n,t,f.state,u,l])),"undefined"!=typeof document&&(e.useLayoutEffect((()=>{try{let e=sessionStorage.getItem(n||"react-router-scroll-positions");e&&(pe=JSON.parse(e))}catch(be){}}),[n]),e.useLayoutEffect((()=>{let e=t&&"/"!==i?(e,n)=>t({...e,pathname:g(e.pathname,i)||e.pathname},n):t,n=a?.enableScrollRestoration(pe,(()=>window.scrollY),e);return()=>n&&n()}),[a,i,t]),e.useLayoutEffect((()=>{if(!1!==r)if("number"!=typeof r){if(u.hash){let e=document.getElementById(decodeURIComponent(u.hash.slice(1)));if(e)return void e.scrollIntoView()}!0!==o&&window.scrollTo(0,0)}else window.scrollTo(0,r)}),[u,r,o]))}function ve(t,n){let{capture:a}=n||{};e.useEffect((()=>{let e=null!=a?{capture:a}:void 0;return window.addEventListener("beforeunload",t,e),()=>{window.removeEventListener("beforeunload",t,e)}}),[t,a])}function ge({when:t,message:n}){let a=v(t);e.useEffect((()=>{if("blocked"===a.state){window.confirm(n)?setTimeout(a.proceed,0):a.reset()}}),[a,n]),e.useEffect((()=>{"blocked"!==a.state||t||a.reset()}),[a,t])}function ye(t,n={}){let a=e.useContext(M);null==a&&E(!1);let{basename:r}=oe(ae.useViewTransitionState),o=l(t,{relative:n.relative});if(!a.isTransitioning)return!1;let i=g(a.currentLocation.pathname,r)||a.currentLocation.pathname,s=g(a.nextLocation.pathname,r)||a.nextLocation.pathname;return null!=L(o.pathname,s)||null!=L(o.pathname,i)}export{J as BrowserRouter,te as Form,q as HashRouter,Z as Link,ee as NavLink,$ as RouterProvider,ne as ScrollRestoration,V as UNSAFE_FetchersContext,M as UNSAFE_ViewTransitionContext,we as UNSAFE_useScrollRestoration,P as createBrowserRouter,D as createHashRouter,A as createSearchParams,G as unstable_HistoryRouter,ge as unstable_usePrompt,ye as unstable_useViewTransitionState,ve as useBeforeUnload,me as useFetcher,he as useFetchers,de as useFormAction,se as useLinkClickHandler,ue as useSearchParams,fe as useSubmit};
//# sourceMappingURL=react-router-dom.production.min.js.map
